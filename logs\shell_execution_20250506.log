[2025-05-06 00:10:42] 开始执行日报脚本
[2025-05-06 00:10:42] 工作目录: /volume3/SSD-soft/fzsrb
[2025-05-06 00:10:42] 检查Python依赖...
WARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager, possibly rendering your system unusable. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv. Use the --root-user-action option if you know what you are doing and want to suppress this warning.
[2025-05-06 00:10:42] 开始执行简单日报脚本...
2025-05-06 00:10:44,042 - INFO - ==================================================
2025-05-06 00:10:44,042 - INFO - 开始生成日报统计数据...
2025-05-06 00:10:44,080 - INFO - 开始生成 2025-05-05 的报表数据...
2025-05-06 00:10:44,080 - INFO - 获取 2025-05-05 的订单数据（下单时间维度）...
2025-05-06 00:10:44,107 - INFO - 成功获取到 207 条去重后的订单数据（下单时间维度）
2025-05-06 00:10:44,107 - INFO - 开始分类并过滤订单数据...
2025-05-06 00:10:44,107 - INFO - 订单分类完成: 有效订单 207 单，其中骑手卡 191 单，流量卡 16 单
2025-05-06 00:10:44,107 - INFO - 计算各渠道的D0下单和D1激活数据...
2025-05-06 00:10:44,130 - INFO - 成功获取到 177 条当日激活订单
2025-05-06 00:10:44,134 - INFO - 样本订单日期检查 (共5个):
2025-05-06 00:10:44,134 - INFO - 订单 20250503123411809497: 下单时间=2025-05-03, 激活时间=2025-05-05, 相差=2天
2025-05-06 00:10:44,134 - INFO - 订单 20250504114794150094: 下单时间=2025-05-04, 激活时间=2025-05-05, 相差=1天
2025-05-06 00:10:44,134 - INFO - 订单 20250504070619148052: 下单时间=2025-05-04, 激活时间=2025-05-05, 相差=1天
2025-05-06 00:10:44,134 - INFO - 订单 20250504170170009258: 下单时间=2025-05-04, 激活时间=2025-05-05, 相差=1天
2025-05-06 00:10:44,134 - INFO - 订单 20250504130579419077: 下单时间=2025-05-04, 激活时间=2025-05-05, 相差=1天
2025-05-06 00:10:44,135 - INFO - 熟龄度处理统计: 总订单=177, 成功处理=177, 处理失败=0
2025-05-06 00:10:44,135 - INFO - 熟龄度分布: 当天=12, 昨天=60, 前天=36, 更早=69
2025-05-06 00:10:44,135 - INFO - 骑手卡熟龄度分布: 当天=12, 昨天=60, 前天=33, 更早=51
2025-05-06 00:10:44,135 - INFO - 流量卡熟龄度分布: 当天=0, 昨天=0, 前天=3, 更早=18
2025-05-06 00:10:44,135 - INFO - 熟龄度数据: {'same_day': 12, 'one_day': 60, 'two_days': 36, 'more_days': 69}
2025-05-06 00:10:44,135 - INFO - 骑手卡熟龄度数据: {'same_day': 12, 'one_day': 60, 'two_days': 33, 'more_days': 51}, 总数: 156
2025-05-06 00:10:44,135 - INFO - 流量卡熟龄度数据: {'same_day': 0, 'one_day': 0, 'two_days': 3, 'more_days': 18}, 总数: 21
2025-05-06 00:10:44,135 - INFO - 当日下单当日激活比例: 6.8%（12/177）
2025-05-06 00:10:44,135 - INFO - 样本订单日期检查 (共5个): 177，熟龄度分布: 当天=12, 昨天=60, 前天=36, 更早=69
2025-05-06 00:10:44,136 - INFO - 生成HTML报表模板，加载数据: 总活跃=177, 骑手卡=156, 流量卡=21
2025-05-06 00:10:44,136 - INFO - 骑手卡数据: [12, 60, 33, 51]
2025-05-06 00:10:44,136 - INFO - 流量卡数据: [0, 0, 3, 18]
2025-05-06 00:10:44,136 - INFO - HTML报表已生成: reports/report_20250505.html
2025-05-06 00:10:44,136 - INFO - 准备上传文件到FTP服务器: report_20250505.html
2025-05-06 00:10:44,136 - INFO - FTP配置: 主机=211.149.249.234, 端口=21, 目录=reports
2025-05-06 00:10:44,136 - INFO - 本地文件: reports/report_20250505.html, 大小: 33578 字节
2025-05-06 00:10:44,136 - INFO - 正在连接到FTP服务器: 211.149.249.234:21
2025-05-06 00:10:44,200 - INFO - 正在登录FTP服务器: 用户名=reports
2025-05-06 00:10:44,285 - INFO - FTP登录成功
2025-05-06 00:10:44,315 - INFO - 当前FTP目录: /
2025-05-06 00:10:44,315 - INFO - 尝试切换到目录: reports
2025-05-06 00:10:44,375 - INFO - 成功切换到目录: /reports
2025-05-06 00:10:44,502 - INFO - 上传前目录内容: ['report_20250503.html', 'report_20250502.html', '..', 'report_20250501.html', 'report_20250504.html', '.']
2025-05-06 00:10:44,502 - INFO - 开始上传文件: reports/report_20250505.html -> report_20250505.html
2025-05-06 00:10:44,686 - INFO - FTP上传结果: 226-File successfully transferred
226 0.062 seconds (measured here), 0.51 Mbytes per second
2025-05-06 00:10:44,808 - INFO - 上传后目录内容: ['report_20250503.html', 'report_20250502.html', '..', 'report_20250501.html', 'report_20250505.html', 'report_20250504.html', '.']
2025-05-06 00:10:44,808 - INFO - 文件已成功上传并验证: report_20250505.html
2025-05-06 00:10:44,808 - INFO - 文件访问URL: https://b.zhoumeiren.cn/reports/report_20250505.html
2025-05-06 00:10:44,838 - INFO - FTP连接已关闭
2025-05-06 00:10:44,838 - INFO - 报表已上传到服务器，URL: https://b.zhoumeiren.cn/reports/report_20250505.html
2025-05-06 00:10:44,838 - INFO - 日报生成完成
2025-05-06 00:10:44,839 - INFO - ==================================================
*get* '220---------- Welcome to Pure-FTPd [privsep] [TLS] ----------\n'
*get* '220-You are user number 1 of 50 allowed.\n'
*get* '220-Local time is now 00:10. Server port: 21.\n'
*get* '220-This is a private system - No anonymous login\n'
*get* '220-IPv6 connections are also welcome on this server.\n'
*get* '220 You will be disconnected after 15 minutes of inactivity.\n'
*resp* '220---------- Welcome to Pure-FTPd [privsep] [TLS] ----------\n220-You are user number 1 of 50 allowed.\n220-Local time is now 00:10. Server port: 21.\n220-This is a private system - No anonymous login\n220-IPv6 connections are also welcome on this server.\n220 You will be disconnected after 15 minutes of inactivity.'
*cmd* 'USER reports'
*put* 'USER reports\r\n'
*get* '331 User reports OK. Password required\n'
*resp* '331 User reports OK. Password required'
*cmd* 'PASS ****************'
*put* 'PASS ****************\r\n'
*get* '230 OK. Current directory is /\n'
*resp* '230 OK. Current directory is /'
*cmd* 'PWD'
*put* 'PWD\r\n'
*get* '257 "/" is your current location\n'
*resp* '257 "/" is your current location'
*cmd* 'CWD reports'
*put* 'CWD reports\r\n'
*get* '250 OK. Current directory is /reports\n'
*resp* '250 OK. Current directory is /reports'
*cmd* 'PWD'
*put* 'PWD\r\n'
*get* '257 "/reports" is your current location\n'
*resp* '257 "/reports" is your current location'
*cmd* 'TYPE A'
*put* 'TYPE A\r\n'
*get* '200 TYPE is now ASCII\n'
*resp* '200 TYPE is now ASCII'
*cmd* 'PASV'
*put* 'PASV\r\n'
*get* '227 Entering Passive Mode (211,149,249,234,152,108)\n'
*resp* '227 Entering Passive Mode (211,149,249,234,152,108)'
*cmd* 'NLST'
*put* 'NLST\r\n'
*get* '150 Accepted data connection\n'
*resp* '150 Accepted data connection'
*get* '226 6 matches total\n'
*resp* '226 6 matches total'
*cmd* 'TYPE I'
*put* 'TYPE I\r\n'
*get* '200 TYPE is now 8-bit binary\n'
*resp* '200 TYPE is now 8-bit binary'
*cmd* 'PASV'
*put* 'PASV\r\n'
*get* '227 Entering Passive Mode (211,149,249,234,152,91)\n'
*resp* '227 Entering Passive Mode (211,149,249,234,152,91)'
*cmd* 'STOR report_20250505.html'
*put* 'STOR report_20250505.html\r\n'
*get* '150 Accepted data connection\n'
*resp* '150 Accepted data connection'
*get* '226-File successfully transferred\n'
*get* '226 0.062 seconds (measured here), 0.51 Mbytes per second\n'
*resp* '226-File successfully transferred\n226 0.062 seconds (measured here), 0.51 Mbytes per second'
*cmd* 'TYPE A'
*put* 'TYPE A\r\n'
*get* '200 TYPE is now ASCII\n'
*resp* '200 TYPE is now ASCII'
*cmd* 'PASV'
*put* 'PASV\r\n'
*get* '227 Entering Passive Mode (211,149,249,234,153,19)\n'
*resp* '227 Entering Passive Mode (211,149,249,234,153,19)'
*cmd* 'NLST'
*put* 'NLST\r\n'
*get* '150 Accepted data connection\n'
*resp* '150 Accepted data connection'
*get* '226 7 matches total\n'
*resp* '226 7 matches total'
*cmd* 'QUIT'
*put* 'QUIT\r\n'
*get* '221-Goodbye. You uploaded 33 and downloaded 0 kbytes.\n'
*get* '221 Logout.\n'
*resp* '221-Goodbye. You uploaded 33 and downloaded 0 kbytes.\n221 Logout.'
[2025-05-06 00:10:42] 简单日报脚本执行成功
[2025-05-06 00:10:42] 开始执行全渠道日报脚本...
2025-05-06 00:10:45,044 - INFO - ==================================================
2025-05-06 00:10:45,044 - INFO - 开始生成并发送昨天的日报（测试环境）...
2025-05-06 00:10:45,044 - INFO - 开始执行昨天的日报发送流程（测试环境）...
2025-05-06 00:10:45,061 - INFO - 昨天日期: 2025-05-05
2025-05-06 00:10:45,061 - INFO - 开始获取昨天的订单数据（下单时间维度）...
2025-05-06 00:10:45,061 - INFO - 获取 2025-05-05 的订单数据（下单时间维度）...
2025-05-06 00:10:45,087 - INFO - 成功获取到 207 条去重后的订单数据（下单时间维度）
2025-05-06 00:10:45,087 - INFO - 开始获取昨天的订单数据（激活时间维度）...
2025-05-06 00:10:45,087 - INFO - 获取 2025-05-05 的订单数据（激活时间维度）...
2025-05-06 00:10:45,111 - INFO - 成功获取到 177 条去重后的订单数据（激活时间维度）
2025-05-06 00:10:45,112 - INFO - 成功获取到订单数据，继续生成日报
2025-05-06 00:10:45,112 - INFO - 开始生成昨天的日报...
2025-05-06 00:10:45,112 - INFO - 开始生成日报...
2025-05-06 00:10:45,112 - INFO - 开始分类并过滤订单数据...
2025-05-06 00:10:45,112 - INFO - 订单分类完成: 有效订单 207 单，其中骑手卡 191 单，大流量卡 16 单
2025-05-06 00:10:45,112 - INFO - 统计各类订单按渠道分类...
2025-05-06 00:10:45,112 - INFO - 开始分类并过滤订单数据...
2025-05-06 00:10:45,112 - INFO - 订单分类完成: 有效订单 177 单，其中骑手卡 156 单，大流量卡 21 单
2025-05-06 00:10:45,113 - INFO - 统计各类订单按渠道分类...
2025-05-06 00:10:45,113 - INFO - 日报生成完成
2025-05-06 00:10:45,113 - INFO - 开始分类并过滤订单数据...
2025-05-06 00:10:45,113 - INFO - 订单分类完成: 有效订单 207 单，其中骑手卡 191 单，大流量卡 16 单
2025-05-06 00:10:45,113 - INFO - 统计各类订单按渠道分类...
2025-05-06 00:10:45,113 - INFO - 开始分类并过滤订单数据...
2025-05-06 00:10:45,113 - INFO - 订单分类完成: 有效订单 177 单，其中骑手卡 156 单，大流量卡 21 单
2025-05-06 00:10:45,114 - INFO - 统计各类订单按渠道分类...
2025-05-06 00:10:45,115 - INFO - 开始生成 2025-05-05 的报表数据...
2025-05-06 00:10:45,115 - INFO - 获取 2025-05-05 的订单数据（下单时间维度）...
2025-05-06 00:10:45,139 - INFO - 成功获取到 207 条去重后的订单数据（下单时间维度）
2025-05-06 00:10:45,140 - INFO - 开始分类并过滤订单数据...
2025-05-06 00:10:45,140 - INFO - 订单分类完成: 有效订单 207 单，其中骑手卡 191 单，流量卡 16 单
2025-05-06 00:10:45,140 - INFO - 计算各渠道的D0下单和D1激活数据...
2025-05-06 00:10:45,163 - INFO - 成功获取到 177 条当日激活订单
2025-05-06 00:10:45,165 - INFO - 样本订单日期检查 (共5个):
2025-05-06 00:10:45,165 - INFO - 订单 20250503123411809497: 下单时间=2025-05-03, 激活时间=2025-05-05, 相差=2天
2025-05-06 00:10:45,165 - INFO - 订单 20250504114794150094: 下单时间=2025-05-04, 激活时间=2025-05-05, 相差=1天
2025-05-06 00:10:45,165 - INFO - 订单 20250504070619148052: 下单时间=2025-05-04, 激活时间=2025-05-05, 相差=1天
2025-05-06 00:10:45,165 - INFO - 订单 20250504170170009258: 下单时间=2025-05-04, 激活时间=2025-05-05, 相差=1天
2025-05-06 00:10:45,165 - INFO - 订单 20250504130579419077: 下单时间=2025-05-04, 激活时间=2025-05-05, 相差=1天
2025-05-06 00:10:45,166 - INFO - 熟龄度处理统计: 总订单=177, 成功处理=177, 处理失败=0
2025-05-06 00:10:45,166 - INFO - 熟龄度分布: 当天=12, 昨天=60, 前天=36, 更早=69
2025-05-06 00:10:45,166 - INFO - 骑手卡熟龄度分布: 当天=12, 昨天=60, 前天=33, 更早=51
2025-05-06 00:10:45,166 - INFO - 流量卡熟龄度分布: 当天=0, 昨天=0, 前天=3, 更早=18
2025-05-06 00:10:45,166 - INFO - 熟龄度数据: {'same_day': 12, 'one_day': 60, 'two_days': 36, 'more_days': 69}
2025-05-06 00:10:45,166 - INFO - 骑手卡熟龄度数据: {'same_day': 12, 'one_day': 60, 'two_days': 33, 'more_days': 51}, 总数: 156
2025-05-06 00:10:45,166 - INFO - 流量卡熟龄度数据: {'same_day': 0, 'one_day': 0, 'two_days': 3, 'more_days': 18}, 总数: 21
2025-05-06 00:10:45,166 - INFO - 当日下单当日激活比例: 6.8%（12/177）
2025-05-06 00:10:45,166 - INFO - 样本订单日期检查 (共5个): 177，熟龄度分布: 当天=12, 昨天=60, 前天=36, 更早=69
2025-05-06 00:10:45,167 - INFO - 开始发送模板卡片消息到企业微信...
2025-05-06 00:10:45,167 - INFO - 开始发送消息到企业微信（测试环境）...
2025-05-06 00:10:45,485 - INFO - 企业微信API响应状态码: 200
2025-05-06 00:10:45,485 - INFO - 企业微信消息发送成功
2025-05-06 00:10:45,485 - INFO - 昨天的日报发送成功
2025-05-06 00:10:45,485 - INFO - 昨天的日报处理完成。
2025-05-06 00:10:45,485 - INFO - ==================================================
[2025-05-06 00:10:42] 全渠道日报脚本执行成功
[2025-05-06 00:10:42] 开始执行OPPO渠道日报脚本...
2025-05-06 00:10:45,647 - INFO - ==================================================
2025-05-06 00:10:45,647 - INFO - 开始生成并发送OPPO和荣耀渠道日报（测试环境）...
2025-05-06 00:10:45,647 - INFO - 开始执行OPPO和荣耀渠道日报发送流程（测试环境）...
2025-05-06 00:10:45,664 - INFO - 查询日期: 2025-05-05
2025-05-06 00:10:45,664 - INFO - 获取 2025-05-05 的OPPO和荣耀渠道订单数据（下单时间维度）...
2025-05-06 00:10:45,679 - INFO - 查询到 15 条去重后的订单数据
2025-05-06 00:10:45,679 - INFO - 开始生成OPPO和荣耀渠道Markdown格式日报...
2025-05-06 00:10:45,679 - INFO - 开始处理订单数据，总数据条数: 15
2025-05-06 00:10:45,679 - INFO - 统计结果: 总订单 15
2025-05-06 00:10:45,679 - INFO -   荣耀: 2单
2025-05-06 00:10:45,680 - INFO -   OPPO: 13单
2025-05-06 00:10:45,680 - INFO - Markdown格式日报生成完成
2025-05-06 00:10:45,680 - INFO - 开始发送消息到企业微信（测试环境）...
2025-05-06 00:10:45,929 - INFO - 企业微信API响应状态码: 200
2025-05-06 00:10:45,930 - INFO - 企业微信消息发送成功
2025-05-06 00:10:45,930 - INFO - OPPO和荣耀渠道日报发送成功
2025-05-06 00:10:45,930 - INFO - OPPO和荣耀渠道日报处理完成。
2025-05-06 00:10:45,930 - INFO - ==================================================
[2025-05-06 00:10:42] OPPO渠道日报脚本执行成功
[2025-05-06 00:10:45] 所有日报脚本执行完成
----------------------------------------
[2025-05-06 08:00:02] 开始执行日报脚本
[2025-05-06 08:00:02] 工作目录: /volume3/SSD-soft/fzsrb
[2025-05-06 08:00:02] 检查Python依赖...
WARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager, possibly rendering your system unusable. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv. Use the --root-user-action option if you know what you are doing and want to suppress this warning.
[2025-05-06 08:00:02] 开始执行简单日报脚本...
2025-05-06 08:00:02,603 - INFO - ==================================================
2025-05-06 08:00:02,603 - INFO - 开始生成日报统计数据...
2025-05-06 08:00:02,620 - INFO - 开始生成 2025-05-05 的报表数据...
2025-05-06 08:00:02,620 - INFO - 获取 2025-05-05 的订单数据（下单时间维度）...
2025-05-06 08:00:02,646 - INFO - 成功获取到 207 条去重后的订单数据（下单时间维度）
2025-05-06 08:00:02,646 - INFO - 开始分类并过滤订单数据...
2025-05-06 08:00:02,646 - INFO - 订单分类完成: 有效订单 207 单，其中骑手卡 191 单，流量卡 16 单
2025-05-06 08:00:02,647 - INFO - 计算各渠道的D0下单和D1激活数据...
2025-05-06 08:00:02,670 - INFO - 成功获取到 177 条当日激活订单
2025-05-06 08:00:02,673 - INFO - 样本订单日期检查 (共5个):
2025-05-06 08:00:02,673 - INFO - 订单 20250503123411809497: 下单时间=2025-05-03, 激活时间=2025-05-05, 相差=2天
2025-05-06 08:00:02,673 - INFO - 订单 20250504114794150094: 下单时间=2025-05-04, 激活时间=2025-05-05, 相差=1天
2025-05-06 08:00:02,673 - INFO - 订单 20250504070619148052: 下单时间=2025-05-04, 激活时间=2025-05-05, 相差=1天
2025-05-06 08:00:02,674 - INFO - 订单 20250504170170009258: 下单时间=2025-05-04, 激活时间=2025-05-05, 相差=1天
2025-05-06 08:00:02,674 - INFO - 订单 20250504130579419077: 下单时间=2025-05-04, 激活时间=2025-05-05, 相差=1天
2025-05-06 08:00:02,674 - INFO - 熟龄度处理统计: 总订单=177, 成功处理=177, 处理失败=0
2025-05-06 08:00:02,674 - INFO - 熟龄度分布: 当天=12, 昨天=60, 前天=36, 更早=69
2025-05-06 08:00:02,674 - INFO - 骑手卡熟龄度分布: 当天=12, 昨天=60, 前天=33, 更早=51
2025-05-06 08:00:02,674 - INFO - 流量卡熟龄度分布: 当天=0, 昨天=0, 前天=3, 更早=18
2025-05-06 08:00:02,674 - INFO - 熟龄度数据: {'same_day': 12, 'one_day': 60, 'two_days': 36, 'more_days': 69}
2025-05-06 08:00:02,674 - INFO - 骑手卡熟龄度数据: {'same_day': 12, 'one_day': 60, 'two_days': 33, 'more_days': 51}, 总数: 156
2025-05-06 08:00:02,675 - INFO - 流量卡熟龄度数据: {'same_day': 0, 'one_day': 0, 'two_days': 3, 'more_days': 18}, 总数: 21
2025-05-06 08:00:02,675 - INFO - 当日下单当日激活比例: 6.8%（12/177）
2025-05-06 08:00:02,675 - INFO - 样本订单日期检查 (共5个): 177，熟龄度分布: 当天=12, 昨天=60, 前天=36, 更早=69
2025-05-06 08:00:02,675 - INFO - 生成HTML报表模板，加载数据: 总活跃=177, 骑手卡=156, 流量卡=21
2025-05-06 08:00:02,675 - INFO - 骑手卡数据: [12, 60, 33, 51]
2025-05-06 08:00:02,675 - INFO - 流量卡数据: [0, 0, 3, 18]
2025-05-06 08:00:02,675 - INFO - HTML报表已生成: reports/report_20250505.html
2025-05-06 08:00:02,676 - INFO - 准备上传文件到FTP服务器: report_20250505.html
2025-05-06 08:00:02,676 - INFO - FTP配置: 主机=211.149.249.234, 端口=21, 目录=reports
2025-05-06 08:00:02,676 - INFO - 本地文件: reports/report_20250505.html, 大小: 33578 字节
2025-05-06 08:00:02,676 - INFO - 正在连接到FTP服务器: 211.149.249.234:21
2025-05-06 08:00:02,741 - INFO - 正在登录FTP服务器: 用户名=reports
2025-05-06 08:00:02,830 - INFO - FTP登录成功
2025-05-06 08:00:02,861 - INFO - 当前FTP目录: /
2025-05-06 08:00:02,861 - INFO - 尝试切换到目录: reports
2025-05-06 08:00:02,925 - INFO - 成功切换到目录: /reports
2025-05-06 08:00:03,067 - INFO - 上传前目录内容: ['report_20250503.html', 'report_20250502.html', '..', 'report_20250501.html', 'report_20250505.html', 'report_20250504.html', '.']
2025-05-06 08:00:03,067 - INFO - 开始上传文件: reports/report_20250505.html -> report_20250505.html
2025-05-06 08:00:03,263 - INFO - FTP上传结果: 226-File successfully transferred
226 0.067 seconds (measured here), 487.16 Kbytes per second
2025-05-06 08:00:03,395 - INFO - 上传后目录内容: ['report_20250503.html', 'report_20250502.html', '..', 'report_20250501.html', 'report_20250505.html', 'report_20250504.html', '.']
2025-05-06 08:00:03,395 - INFO - 文件已成功上传并验证: report_20250505.html
2025-05-06 08:00:03,395 - INFO - 文件访问URL: https://b.zhoumeiren.cn/reports/report_20250505.html
2025-05-06 08:00:03,429 - INFO - FTP连接已关闭
2025-05-06 08:00:03,429 - INFO - 报表已上传到服务器，URL: https://b.zhoumeiren.cn/reports/report_20250505.html
2025-05-06 08:00:03,430 - INFO - 日报生成完成
2025-05-06 08:00:03,430 - INFO - ==================================================
*get* '220---------- Welcome to Pure-FTPd [privsep] [TLS] ----------\n'
*get* '220-You are user number 1 of 50 allowed.\n'
*get* '220-Local time is now 08:00. Server port: 21.\n'
*get* '220-This is a private system - No anonymous login\n'
*get* '220-IPv6 connections are also welcome on this server.\n'
*get* '220 You will be disconnected after 15 minutes of inactivity.\n'
*resp* '220---------- Welcome to Pure-FTPd [privsep] [TLS] ----------\n220-You are user number 1 of 50 allowed.\n220-Local time is now 08:00. Server port: 21.\n220-This is a private system - No anonymous login\n220-IPv6 connections are also welcome on this server.\n220 You will be disconnected after 15 minutes of inactivity.'
*cmd* 'USER reports'
*put* 'USER reports\r\n'
*get* '331 User reports OK. Password required\n'
*resp* '331 User reports OK. Password required'
*cmd* 'PASS ****************'
*put* 'PASS ****************\r\n'
*get* '230 OK. Current directory is /\n'
*resp* '230 OK. Current directory is /'
*cmd* 'PWD'
*put* 'PWD\r\n'
*get* '257 "/" is your current location\n'
*resp* '257 "/" is your current location'
*cmd* 'CWD reports'
*put* 'CWD reports\r\n'
*get* '250 OK. Current directory is /reports\n'
*resp* '250 OK. Current directory is /reports'
*cmd* 'PWD'
*put* 'PWD\r\n'
*get* '257 "/reports" is your current location\n'
*resp* '257 "/reports" is your current location'
*cmd* 'TYPE A'
*put* 'TYPE A\r\n'
*get* '200 TYPE is now ASCII\n'
*resp* '200 TYPE is now ASCII'
*cmd* 'PASV'
*put* 'PASV\r\n'
*get* '227 Entering Passive Mode (211,149,249,234,156,39)\n'
*resp* '227 Entering Passive Mode (211,149,249,234,156,39)'
*cmd* 'NLST'
*put* 'NLST\r\n'
*get* '150 Accepted data connection\n'
*resp* '150 Accepted data connection'
*get* '226 7 matches total\n'
*resp* '226 7 matches total'
*cmd* 'TYPE I'
*put* 'TYPE I\r\n'
*get* '200 TYPE is now 8-bit binary\n'
*resp* '200 TYPE is now 8-bit binary'
*cmd* 'PASV'
*put* 'PASV\r\n'
*get* '227 Entering Passive Mode (211,149,249,234,153,99)\n'
*resp* '227 Entering Passive Mode (211,149,249,234,153,99)'
*cmd* 'STOR report_20250505.html'
*put* 'STOR report_20250505.html\r\n'
*get* '150 Accepted data connection\n'
*resp* '150 Accepted data connection'
*get* '226-File successfully transferred\n'
*get* '226 0.067 seconds (measured here), 487.16 Kbytes per second\n'
*resp* '226-File successfully transferred\n226 0.067 seconds (measured here), 487.16 Kbytes per second'
*cmd* 'TYPE A'
*put* 'TYPE A\r\n'
*get* '200 TYPE is now ASCII\n'
*resp* '200 TYPE is now ASCII'
*cmd* 'PASV'
*put* 'PASV\r\n'
*get* '227 Entering Passive Mode (211,149,249,234,153,150)\n'
*resp* '227 Entering Passive Mode (211,149,249,234,153,150)'
*cmd* 'NLST'
*put* 'NLST\r\n'
*get* '150 Accepted data connection\n'
*resp* '150 Accepted data connection'
*get* '226 7 matches total\n'
*resp* '226 7 matches total'
*cmd* 'QUIT'
*put* 'QUIT\r\n'
*get* '221-Goodbye. You uploaded 33 and downloaded 0 kbytes.\n'
*get* '221 Logout.\n'
*resp* '221-Goodbye. You uploaded 33 and downloaded 0 kbytes.\n221 Logout.'
[2025-05-06 08:00:02] 简单日报脚本执行成功
[2025-05-06 08:00:02] 开始执行全渠道日报脚本...
2025-05-06 08:00:03,596 - INFO - ==================================================
2025-05-06 08:00:03,596 - INFO - 开始生成并发送昨天的日报（全渠道生产环境）...
2025-05-06 08:00:03,596 - INFO - 开始执行昨天的日报发送流程（全渠道生产环境）...
2025-05-06 08:00:03,614 - INFO - 昨天日期: 2025-05-05
2025-05-06 08:00:03,614 - INFO - 开始获取昨天的订单数据（下单时间维度）...
2025-05-06 08:00:03,614 - INFO - 获取 2025-05-05 的订单数据（下单时间维度）...
2025-05-06 08:00:03,640 - INFO - 成功获取到 207 条去重后的订单数据（下单时间维度）
2025-05-06 08:00:03,640 - INFO - 开始获取昨天的订单数据（激活时间维度）...
2025-05-06 08:00:03,640 - INFO - 获取 2025-05-05 的订单数据（激活时间维度）...
2025-05-06 08:00:03,663 - INFO - 成功获取到 177 条去重后的订单数据（激活时间维度）
2025-05-06 08:00:03,663 - INFO - 成功获取到订单数据，继续生成日报
2025-05-06 08:00:03,663 - INFO - 开始生成昨天的日报...
2025-05-06 08:00:03,663 - INFO - 开始生成日报...
2025-05-06 08:00:03,663 - INFO - 开始分类并过滤订单数据...
2025-05-06 08:00:03,664 - INFO - 订单分类完成: 有效订单 207 单，其中骑手卡 191 单，大流量卡 16 单
2025-05-06 08:00:03,664 - INFO - 统计各类订单按渠道分类...
2025-05-06 08:00:03,664 - INFO - 开始分类并过滤订单数据...
2025-05-06 08:00:03,664 - INFO - 订单分类完成: 有效订单 177 单，其中骑手卡 156 单，大流量卡 21 单
2025-05-06 08:00:03,664 - INFO - 统计各类订单按渠道分类...
2025-05-06 08:00:03,665 - INFO - 日报生成完成
2025-05-06 08:00:03,665 - INFO - 开始分类并过滤订单数据...
2025-05-06 08:00:03,665 - INFO - 订单分类完成: 有效订单 207 单，其中骑手卡 191 单，大流量卡 16 单
2025-05-06 08:00:03,665 - INFO - 统计各类订单按渠道分类...
2025-05-06 08:00:03,665 - INFO - 开始分类并过滤订单数据...
2025-05-06 08:00:03,665 - INFO - 订单分类完成: 有效订单 177 单，其中骑手卡 156 单，大流量卡 21 单
2025-05-06 08:00:03,665 - INFO - 统计各类订单按渠道分类...
2025-05-06 08:00:03,667 - INFO - 开始生成 2025-05-05 的报表数据...
2025-05-06 08:00:03,667 - INFO - 获取 2025-05-05 的订单数据（下单时间维度）...
2025-05-06 08:00:03,692 - INFO - 成功获取到 207 条去重后的订单数据（下单时间维度）
2025-05-06 08:00:03,692 - INFO - 开始分类并过滤订单数据...
2025-05-06 08:00:03,693 - INFO - 订单分类完成: 有效订单 207 单，其中骑手卡 191 单，流量卡 16 单
2025-05-06 08:00:03,693 - INFO - 计算各渠道的D0下单和D1激活数据...
2025-05-06 08:00:03,715 - INFO - 成功获取到 177 条当日激活订单
2025-05-06 08:00:03,717 - INFO - 样本订单日期检查 (共5个):
2025-05-06 08:00:03,717 - INFO - 订单 20250503123411809497: 下单时间=2025-05-03, 激活时间=2025-05-05, 相差=2天
2025-05-06 08:00:03,717 - INFO - 订单 20250504114794150094: 下单时间=2025-05-04, 激活时间=2025-05-05, 相差=1天
2025-05-06 08:00:03,717 - INFO - 订单 20250504070619148052: 下单时间=2025-05-04, 激活时间=2025-05-05, 相差=1天
2025-05-06 08:00:03,717 - INFO - 订单 20250504170170009258: 下单时间=2025-05-04, 激活时间=2025-05-05, 相差=1天
2025-05-06 08:00:03,717 - INFO - 订单 20250504130579419077: 下单时间=2025-05-04, 激活时间=2025-05-05, 相差=1天
2025-05-06 08:00:03,718 - INFO - 熟龄度处理统计: 总订单=177, 成功处理=177, 处理失败=0
2025-05-06 08:00:03,718 - INFO - 熟龄度分布: 当天=12, 昨天=60, 前天=36, 更早=69
2025-05-06 08:00:03,718 - INFO - 骑手卡熟龄度分布: 当天=12, 昨天=60, 前天=33, 更早=51
2025-05-06 08:00:03,718 - INFO - 流量卡熟龄度分布: 当天=0, 昨天=0, 前天=3, 更早=18
2025-05-06 08:00:03,718 - INFO - 熟龄度数据: {'same_day': 12, 'one_day': 60, 'two_days': 36, 'more_days': 69}
2025-05-06 08:00:03,718 - INFO - 骑手卡熟龄度数据: {'same_day': 12, 'one_day': 60, 'two_days': 33, 'more_days': 51}, 总数: 156
2025-05-06 08:00:03,718 - INFO - 流量卡熟龄度数据: {'same_day': 0, 'one_day': 0, 'two_days': 3, 'more_days': 18}, 总数: 21
2025-05-06 08:00:03,718 - INFO - 当日下单当日激活比例: 6.8%（12/177）
2025-05-06 08:00:03,718 - INFO - 样本订单日期检查 (共5个): 177，熟龄度分布: 当天=12, 昨天=60, 前天=36, 更早=69
2025-05-06 08:00:03,719 - INFO - 开始发送模板卡片消息到企业微信...
2025-05-06 08:00:03,719 - INFO - 开始发送消息到企业微信（全渠道生产环境）...
2025-05-06 08:00:03,964 - INFO - 企业微信API响应状态码: 200
2025-05-06 08:00:03,965 - INFO - 企业微信消息发送成功
2025-05-06 08:00:03,965 - INFO - 昨天的日报发送成功
2025-05-06 08:00:03,965 - INFO - 昨天的日报处理完成。
2025-05-06 08:00:03,965 - INFO - ==================================================
[2025-05-06 08:00:02] 全渠道日报脚本执行成功
[2025-05-06 08:00:02] 开始执行OPPO渠道日报脚本...
2025-05-06 08:00:04,132 - INFO - ==================================================
2025-05-06 08:00:04,132 - INFO - 开始生成并发送OPPO和荣耀渠道日报（OPPO生产环境）...
2025-05-06 08:00:04,132 - INFO - 开始执行OPPO和荣耀渠道日报发送流程（OPPO生产环境）...
2025-05-06 08:00:04,149 - INFO - 查询日期: 2025-05-05
2025-05-06 08:00:04,149 - INFO - 获取 2025-05-05 的OPPO和荣耀渠道订单数据（下单时间维度）...
2025-05-06 08:00:04,164 - INFO - 查询到 15 条去重后的订单数据
2025-05-06 08:00:04,164 - INFO - 开始生成OPPO和荣耀渠道Markdown格式日报...
2025-05-06 08:00:04,164 - INFO - 开始处理订单数据，总数据条数: 15
2025-05-06 08:00:04,165 - INFO - 统计结果: 总订单 15
2025-05-06 08:00:04,165 - INFO -   荣耀: 2单
2025-05-06 08:00:04,165 - INFO -   OPPO: 13单
2025-05-06 08:00:04,165 - INFO - Markdown格式日报生成完成
2025-05-06 08:00:04,165 - INFO - 开始发送消息到企业微信（OPPO生产环境）...
2025-05-06 08:00:04,383 - INFO - 企业微信API响应状态码: 200
2025-05-06 08:00:04,384 - INFO - 企业微信消息发送成功
2025-05-06 08:00:04,384 - INFO - OPPO和荣耀渠道日报发送成功
2025-05-06 08:00:04,384 - INFO - OPPO和荣耀渠道日报处理完成。
2025-05-06 08:00:04,384 - INFO - ==================================================
[2025-05-06 08:00:02] OPPO渠道日报脚本执行成功
[2025-05-06 08:00:04] 所有日报脚本执行完成
----------------------------------------
