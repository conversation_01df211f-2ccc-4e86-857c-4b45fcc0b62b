2025-05-27 10:12:19,170 - INFO - ==================================================
2025-05-27 10:12:19,170 - INFO - 开始生成并发送驿舟相关渠道日报（驿舟日报环境）...
2025-05-27 10:12:19,170 - INFO - 开始执行驿舟相关渠道日报发送流程（驿舟日报环境）...
2025-05-27 10:12:20,610 - INFO - 获取 2025-05-26 的驿舟相关渠道订单数据（下单时间维度）...
2025-05-27 10:12:20,726 - INFO - 查询到 55 条去重后的订单数据
2025-05-27 10:12:20,726 - INFO - 获取 2025-05-26 的驿舟相关渠道订单数据（激活时间维度）...
2025-05-27 10:12:20,824 - INFO - 查询到 23 条去重后的订单数据
2025-05-27 10:12:21,010 - INFO - 开始发送消息到企业微信（驿舟日报环境）...
2025-05-27 10:12:21,442 - INFO - 企业微信API响应状态码: 200
2025-05-27 10:12:21,443 - INFO - 企业微信消息发送成功
2025-05-27 10:12:21,443 - INFO - 驿舟相关渠道日报发送成功
2025-05-27 10:12:21,445 - INFO - 驿舟相关渠道日报处理完成。
2025-05-27 10:12:21,445 - INFO - ==================================================
2025-05-27 10:24:32,927 - INFO - ==================================================
2025-05-27 10:24:32,927 - INFO - 开始生成并发送驿舟相关渠道日报（测试环境）...
2025-05-27 10:24:32,927 - INFO - 开始执行驿舟相关渠道日报发送流程（测试环境）...
2025-05-27 10:24:32,955 - INFO - 获取 2025-05-26 的驿舟相关渠道订单数据（下单时间维度）...
2025-05-27 10:24:33,072 - INFO - 查询到 55 条去重后的订单数据
2025-05-27 10:24:33,072 - INFO - 获取 2025-05-26 的驿舟相关渠道订单数据（激活时间维度）...
2025-05-27 10:24:33,168 - INFO - 查询到 23 条去重后的订单数据
2025-05-27 10:24:33,382 - INFO - 开始发送消息到企业微信（测试环境）...
2025-05-27 10:24:33,842 - INFO - 企业微信API响应状态码: 200
2025-05-27 10:24:33,842 - INFO - 企业微信消息发送成功
2025-05-27 10:24:33,843 - INFO - 驿舟相关渠道日报发送成功
2025-05-27 10:24:33,843 - INFO - 驿舟相关渠道日报处理完成。
2025-05-27 10:24:33,843 - INFO - ==================================================
2025-05-27 10:25:48,798 - INFO - ==================================================
2025-05-27 10:25:48,798 - INFO - 开始生成并发送驿舟相关渠道日报（驿舟日报环境）...
2025-05-27 10:25:48,799 - INFO - 开始执行驿舟相关渠道日报发送流程（驿舟日报环境）...
2025-05-27 10:25:48,827 - INFO - 获取 2025-05-26 的驿舟相关渠道订单数据（下单时间维度）...
2025-05-27 10:25:49,471 - INFO - 查询到 55 条去重后的订单数据
2025-05-27 10:25:49,472 - INFO - 获取 2025-05-26 的驿舟相关渠道订单数据（激活时间维度）...
2025-05-27 10:25:49,661 - INFO - 查询到 23 条去重后的订单数据
2025-05-27 10:25:49,933 - INFO - 开始发送消息到企业微信（驿舟日报环境）...
2025-05-27 10:25:50,288 - INFO - 企业微信API响应状态码: 200
2025-05-27 10:25:50,289 - INFO - 企业微信消息发送成功
2025-05-27 10:25:50,290 - INFO - 驿舟相关渠道日报发送成功
2025-05-27 10:25:50,290 - INFO - 驿舟相关渠道日报处理完成。
2025-05-27 10:25:50,290 - INFO - ==================================================
2025-05-27 12:08:42,561 - INFO - ==================================================
2025-05-27 12:08:42,561 - INFO - 开始生成并发送驿舟相关渠道日报（驿舟日报环境）...
2025-05-27 12:08:42,561 - INFO - 开始执行驿舟相关渠道日报发送流程（驿舟日报环境）...
2025-05-27 12:08:44,029 - INFO - 获取 2025-05-26 的驿舟相关渠道订单数据（下单时间维度）...
2025-05-27 12:08:54,058 - ERROR - 获取驿舟相关订单数据时出错: (2003, "Can't connect to MySQL server on '172.25.165.28' (timed out)")
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pymysql\connections.py", line 649, in connect
    sock = socket.create_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 853, in create_connection
    raise exceptions[0]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 838, in create_connection
    sock.connect(sa)
TimeoutError: timed out

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\个人文档\dev\fzsrb\fzsrb_yizhou.py", line 147, in get_yizhou_order_data
    conn = pymysql.connect(**MYSQL_CONFIG)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pymysql\connections.py", line 361, in __init__
    self.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pymysql\connections.py", line 716, in connect
    raise exc
pymysql.err.OperationalError: (2003, "Can't connect to MySQL server on '172.25.165.28' (timed out)")
2025-05-27 12:08:54,060 - INFO - 获取 2025-05-26 的驿舟相关渠道订单数据（激活时间维度）...
2025-05-27 12:09:04,062 - ERROR - 获取驿舟相关订单数据时出错: (2003, "Can't connect to MySQL server on '172.25.165.28' (timed out)")
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pymysql\connections.py", line 649, in connect
    sock = socket.create_connection(
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 853, in create_connection
    raise exceptions[0]
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\socket.py", line 838, in create_connection
    sock.connect(sa)
TimeoutError: timed out

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\个人文档\dev\fzsrb\fzsrb_yizhou.py", line 147, in get_yizhou_order_data
    conn = pymysql.connect(**MYSQL_CONFIG)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pymysql\connections.py", line 361, in __init__
    self.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\pymysql\connections.py", line 716, in connect
    raise exc
pymysql.err.OperationalError: (2003, "Can't connect to MySQL server on '172.25.165.28' (timed out)")
2025-05-27 12:14:10,848 - INFO - ==================================================
2025-05-27 12:14:10,849 - INFO - 开始生成并发送驿舟相关渠道日报（驿舟日报环境）...
2025-05-27 12:14:10,849 - INFO - 开始执行驿舟相关渠道日报发送流程（驿舟日报环境）...
2025-05-27 12:14:10,877 - INFO - 获取 2025-05-26 的驿舟相关渠道订单数据（下单时间维度）...
2025-05-27 12:14:11,021 - INFO - 查询到 55 条去重后的订单数据
2025-05-27 12:14:11,022 - INFO - 获取 2025-05-26 的驿舟相关渠道订单数据（激活时间维度）...
2025-05-27 12:14:11,128 - INFO - 查询到 23 条去重后的订单数据
2025-05-27 12:14:11,443 - INFO - 开始发送消息到企业微信（驿舟日报环境）...
2025-05-27 12:14:11,886 - INFO - 企业微信API响应状态码: 200
2025-05-27 12:14:11,886 - INFO - 企业微信消息发送成功
2025-05-27 12:14:11,887 - INFO - 驿舟相关渠道日报发送成功
2025-05-27 12:14:11,887 - INFO - 驿舟相关渠道日报处理完成。
2025-05-27 12:14:11,888 - INFO - ==================================================
2025-05-27 12:16:48,696 - INFO - ==================================================
2025-05-27 12:16:48,697 - INFO - 开始生成并发送驿舟相关渠道日报（驿舟日报环境）...
2025-05-27 12:16:48,697 - INFO - 开始执行驿舟相关渠道日报发送流程（驿舟日报环境）...
2025-05-27 12:16:48,729 - INFO - 获取 2025-05-26 的驿舟相关渠道订单数据（下单时间维度）...
2025-05-27 12:16:48,849 - INFO - 查询到 55 条去重后的订单数据
2025-05-27 12:16:48,849 - INFO - 获取 2025-05-26 的驿舟相关渠道订单数据（激活时间维度）...
2025-05-27 12:16:48,952 - INFO - 查询到 23 条去重后的订单数据
2025-05-27 12:16:49,279 - INFO - 开始发送消息到企业微信（驿舟日报环境）...
2025-05-27 12:16:49,647 - INFO - 企业微信API响应状态码: 200
2025-05-27 12:16:49,648 - INFO - 企业微信消息发送成功
2025-05-27 12:16:49,649 - INFO - 驿舟相关渠道日报发送成功
2025-05-27 12:16:49,649 - INFO - 驿舟相关渠道日报处理完成。
2025-05-27 12:16:49,649 - INFO - ==================================================
