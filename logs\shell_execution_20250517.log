[2025-05-17 08:00:02] 开始执行日报脚本
[2025-05-17 08:00:02] 工作目录: /volume3/SSD-soft/fzsrb
[2025-05-17 08:00:02] 检查Python依赖...
WARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager, possibly rendering your system unusable. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv. Use the --root-user-action option if you know what you are doing and want to suppress this warning.
[2025-05-17 08:00:02] 开始执行简单日报脚本...
2025-05-17 08:00:05,239 - INFO - ==================================================
2025-05-17 08:00:05,240 - INFO - 开始生成日报统计数据...
2025-05-17 08:00:05,274 - INFO - 开始生成 2025-05-16 的报表数据...
2025-05-17 08:00:05,275 - INFO - 获取 2025-05-16 的订单数据（下单时间维度）...
2025-05-17 08:00:05,308 - INFO - 成功获取到 253 条去重后的订单数据（下单时间维度）
2025-05-17 08:00:05,308 - INFO - 开始分类并过滤订单数据...
2025-05-17 08:00:05,309 - INFO - 订单分类完成: 有效订单 253 单，其中骑手卡 240 单，流量卡 13 单
2025-05-17 08:00:05,309 - INFO - 计算各渠道的D0下单和D1激活数据...
2025-05-17 08:00:05,336 - INFO - 成功获取到 156 条当日激活订单
2025-05-17 08:00:05,343 - INFO - 样本订单日期检查 (共5个):
2025-05-17 08:00:05,343 - INFO - 订单 20250514100217481713: 下单时间=2025-05-14, 激活时间=2025-05-16, 相差=2天
2025-05-17 08:00:05,343 - INFO - 订单 20250515080552870373: 下单时间=2025-05-15, 激活时间=2025-05-16, 相差=1天
2025-05-17 08:00:05,343 - INFO - 订单 20250514145649494945: 下单时间=2025-05-14, 激活时间=2025-05-16, 相差=2天
2025-05-17 08:00:05,343 - INFO - 订单 20250515113103682316: 下单时间=2025-05-15, 激活时间=2025-05-16, 相差=1天
2025-05-17 08:00:05,343 - INFO - 订单 20250515100698589421: 下单时间=2025-05-15, 激活时间=2025-05-16, 相差=1天
2025-05-17 08:00:05,343 - INFO - 熟龄度处理统计: 总订单=156, 成功处理=156, 处理失败=0
2025-05-17 08:00:05,343 - INFO - 熟龄度分布: 当天=12, 昨天=94, 前天=25, 更早=25
2025-05-17 08:00:05,344 - INFO - 骑手卡熟龄度分布: 当天=12, 昨天=92, 前天=24, 更早=23
2025-05-17 08:00:05,344 - INFO - 流量卡熟龄度分布: 当天=0, 昨天=2, 前天=1, 更早=2
2025-05-17 08:00:05,344 - INFO - 获取报表日期与前一日订单数据对比...
2025-05-17 08:00:05,344 - INFO - 对比日期: 报表日期=2025-05-16, 前一日=2025-05-15
2025-05-17 08:00:05,344 - INFO - 获取 2025-05-16 的订单数据（下单时间维度）...
2025-05-17 08:00:05,376 - INFO - 成功获取到 253 条去重后的订单数据（下单时间维度）
2025-05-17 08:00:05,376 - INFO - 获取 2025-05-15 的订单数据（下单时间维度）...
2025-05-17 08:00:05,408 - INFO - 成功获取到 234 条去重后的订单数据（下单时间维度）
2025-05-17 08:00:05,409 - INFO - 获取周订单趋势数据...
2025-05-17 08:00:05,409 - INFO - 查询日期范围: 2025-05-10 00:00:00 至 2025-05-16 23:59:59
2025-05-17 08:00:05,433 - INFO - 成功获取到 7 天的订单趋势数据
2025-05-17 08:00:05,433 - INFO - 熟龄度数据: {'same_day': 12, 'one_day': 94, 'two_days': 25, 'more_days': 25}
2025-05-17 08:00:05,433 - INFO - 骑手卡熟龄度数据: {'same_day': 12, 'one_day': 92, 'two_days': 24, 'more_days': 23}, 总数: 151
2025-05-17 08:00:05,433 - INFO - 流量卡熟龄度数据: {'same_day': 0, 'one_day': 2, 'two_days': 1, 'more_days': 2}, 总数: 5
2025-05-17 08:00:05,433 - INFO - 当日下单当日激活比例: 7.7%（12/156）
2025-05-17 08:00:05,433 - INFO - 样本订单日期检查 (共5个): 156，熟龄度分布: 当天=12, 昨天=94, 前天=25, 更早=25
2025-05-17 08:00:05,433 - INFO - 环比分析: 当前=253单, 前一日=234单, 变化=8.1%
2025-05-17 08:00:05,433 - INFO - 周趋势数据获取成功，共7天数据
2025-05-17 08:00:05,434 - INFO - 生成HTML报表模板，加载数据: 总活跃=156, 骑手卡=151, 流量卡=5
2025-05-17 08:00:05,434 - INFO - 骑手卡数据: [12, 92, 24, 23]
2025-05-17 08:00:05,434 - INFO - 流量卡数据: [0, 2, 1, 2]
2025-05-17 08:00:05,434 - INFO - HTML报表已生成: reports/report_20250516.html
2025-05-17 08:00:05,434 - INFO - 准备上传文件到FTP服务器: report_20250516.html
2025-05-17 08:00:05,434 - INFO - FTP配置: 主机=211.149.249.234, 端口=21, 目录=reports
2025-05-17 08:00:05,435 - INFO - 本地文件: reports/report_20250516.html, 大小: 52052 字节
2025-05-17 08:00:05,435 - INFO - 正在连接到FTP服务器: 211.149.249.234:21
2025-05-17 08:00:05,504 - INFO - 正在登录FTP服务器: 用户名=reports
2025-05-17 08:00:05,590 - INFO - FTP登录成功
2025-05-17 08:00:05,623 - INFO - 当前FTP目录: /
2025-05-17 08:00:05,623 - INFO - 尝试切换到目录: reports
2025-05-17 08:00:05,690 - INFO - 成功切换到目录: /reports
2025-05-17 08:00:05,826 - INFO - 上传前目录内容: ['report_20250506.html', 'report_20250511.html', 'report_20250515.html', 'report_20250512.html', 'report_20250503.html', 'report_20250508.html', 'report_20250502.html', '..', 'report_20250513.html', 'report_20250509.html', 'report_20250514.html', 'report_20250510.html', 'report_20250501.html', 'report_20250505.html', 'report_20250504.html', 'report_20250507.html', '.']
2025-05-17 08:00:05,826 - INFO - 开始上传文件: reports/report_20250516.html -> report_20250516.html
2025-05-17 08:00:06,073 - INFO - FTP上传结果: 226-File successfully transferred
226 0.108 seconds (measured here), 471.56 Kbytes per second
2025-05-17 08:00:06,207 - INFO - 上传后目录内容: ['report_20250506.html', 'report_20250511.html', 'report_20250515.html', 'report_20250516.html', 'report_20250512.html', 'report_20250503.html', 'report_20250508.html', 'report_20250502.html', '..', 'report_20250513.html', 'report_20250509.html', 'report_20250514.html', 'report_20250510.html', 'report_20250501.html', 'report_20250505.html', 'report_20250504.html', 'report_20250507.html', '.']
2025-05-17 08:00:06,207 - INFO - 文件已成功上传并验证: report_20250516.html
2025-05-17 08:00:06,207 - INFO - 文件访问URL: https://rep.331126.com/reports/report_20250516.html
2025-05-17 08:00:06,240 - INFO - FTP连接已关闭
2025-05-17 08:00:06,240 - INFO - 报表已上传到服务器，URL: https://rep.331126.com/reports/report_20250516.html
2025-05-17 08:00:06,240 - INFO - 日报生成完成
2025-05-17 08:00:06,240 - INFO - ==================================================
*get* '220---------- Welcome to Pure-FTPd [privsep] [TLS] ----------\n'
*get* '220-You are user number 1 of 50 allowed.\n'
*get* '220-Local time is now 08:00. Server port: 21.\n'
*get* '220-This is a private system - No anonymous login\n'
*get* '220-IPv6 connections are also welcome on this server.\n'
*get* '220 You will be disconnected after 15 minutes of inactivity.\n'
*resp* '220---------- Welcome to Pure-FTPd [privsep] [TLS] ----------\n220-You are user number 1 of 50 allowed.\n220-Local time is now 08:00. Server port: 21.\n220-This is a private system - No anonymous login\n220-IPv6 connections are also welcome on this server.\n220 You will be disconnected after 15 minutes of inactivity.'
*cmd* 'USER reports'
*put* 'USER reports\r\n'
*get* '331 User reports OK. Password required\n'
*resp* '331 User reports OK. Password required'
*cmd* 'PASS ****************'
*put* 'PASS ****************\r\n'
*get* '230 OK. Current directory is /\n'
*resp* '230 OK. Current directory is /'
*cmd* 'PWD'
*put* 'PWD\r\n'
*get* '257 "/" is your current location\n'
*resp* '257 "/" is your current location'
*cmd* 'CWD reports'
*put* 'CWD reports\r\n'
*get* '250 OK. Current directory is /reports\n'
*resp* '250 OK. Current directory is /reports'
*cmd* 'PWD'
*put* 'PWD\r\n'
*get* '257 "/reports" is your current location\n'
*resp* '257 "/reports" is your current location'
*cmd* 'TYPE A'
*put* 'TYPE A\r\n'
*get* '200 TYPE is now ASCII\n'
*resp* '200 TYPE is now ASCII'
*cmd* 'PASV'
*put* 'PASV\r\n'
*get* '227 Entering Passive Mode (211,149,249,234,155,197)\n'
*resp* '227 Entering Passive Mode (211,149,249,234,155,197)'
*cmd* 'NLST'
*put* 'NLST\r\n'
*get* '150 Accepted data connection\n'
*resp* '150 Accepted data connection'
*get* '226 17 matches total\n'
*resp* '226 17 matches total'
*cmd* 'TYPE I'
*put* 'TYPE I\r\n'
*get* '200 TYPE is now 8-bit binary\n'
*resp* '200 TYPE is now 8-bit binary'
*cmd* 'PASV'
*put* 'PASV\r\n'
*get* '227 Entering Passive Mode (211,149,249,234,153,126)\n'
*resp* '227 Entering Passive Mode (211,149,249,234,153,126)'
*cmd* 'STOR report_20250516.html'
*put* 'STOR report_20250516.html\r\n'
*get* '150 Accepted data connection\n'
*resp* '150 Accepted data connection'
*get* '226-File successfully transferred\n'
*get* '226 0.108 seconds (measured here), 471.56 Kbytes per second\n'
*resp* '226-File successfully transferred\n226 0.108 seconds (measured here), 471.56 Kbytes per second'
*cmd* 'TYPE A'
*put* 'TYPE A\r\n'
*get* '200 TYPE is now ASCII\n'
*resp* '200 TYPE is now ASCII'
*cmd* 'PASV'
*put* 'PASV\r\n'
*get* '227 Entering Passive Mode (211,149,249,234,152,132)\n'
*resp* '227 Entering Passive Mode (211,149,249,234,152,132)'
*cmd* 'NLST'
*put* 'NLST\r\n'
*get* '150 Accepted data connection\n'
*resp* '150 Accepted data connection'
*get* '226 18 matches total\n'
*resp* '226 18 matches total'
*cmd* 'QUIT'
*put* 'QUIT\r\n'
*get* '221-Goodbye. You uploaded 51 and downloaded 0 kbytes.\n'
*get* '221 Logout.\n'
*resp* '221-Goodbye. You uploaded 51 and downloaded 0 kbytes.\n221 Logout.'
[2025-05-17 08:00:02] 简单日报脚本执行成功
[2025-05-17 08:00:02] 开始执行全渠道日报脚本...
2025-05-17 08:00:06,450 - INFO - ==================================================
2025-05-17 08:00:06,450 - INFO - 开始生成并发送昨天的日报（全渠道生产环境）...
2025-05-17 08:00:06,450 - INFO - 开始执行昨天的日报发送流程（全渠道生产环境）...
2025-05-17 08:00:06,467 - INFO - 昨天日期: 2025-05-16
2025-05-17 08:00:06,467 - INFO - 开始获取昨天的订单数据（下单时间维度）...
2025-05-17 08:00:06,467 - INFO - 获取 2025-05-16 的订单数据（下单时间维度）...
2025-05-17 08:00:06,506 - INFO - 成功获取到 253 条去重后的订单数据（下单时间维度）
2025-05-17 08:00:06,506 - INFO - 开始获取昨天的订单数据（激活时间维度）...
2025-05-17 08:00:06,506 - INFO - 获取 2025-05-16 的订单数据（激活时间维度）...
2025-05-17 08:00:06,533 - INFO - 成功获取到 156 条去重后的订单数据（激活时间维度）
2025-05-17 08:00:06,533 - INFO - 成功获取到订单数据，继续生成日报
2025-05-17 08:00:06,533 - INFO - 开始生成昨天的日报...
2025-05-17 08:00:06,533 - INFO - 开始生成日报...
2025-05-17 08:00:06,533 - INFO - 开始分类并过滤订单数据...
2025-05-17 08:00:06,534 - INFO - 订单分类完成: 有效订单 253 单，其中骑手卡 240 单，大流量卡 13 单
2025-05-17 08:00:06,534 - INFO - 统计各类订单按渠道分类...
2025-05-17 08:00:06,534 - INFO - 开始分类并过滤订单数据...
2025-05-17 08:00:06,534 - INFO - 订单分类完成: 有效订单 156 单，其中骑手卡 151 单，大流量卡 5 单
2025-05-17 08:00:06,534 - INFO - 统计各类订单按渠道分类...
2025-05-17 08:00:06,534 - INFO - 日报生成完成
2025-05-17 08:00:06,535 - INFO - 开始分类并过滤订单数据...
2025-05-17 08:00:06,535 - INFO - 订单分类完成: 有效订单 253 单，其中骑手卡 240 单，大流量卡 13 单
2025-05-17 08:00:06,535 - INFO - 统计各类订单按渠道分类...
2025-05-17 08:00:06,535 - INFO - 开始分类并过滤订单数据...
2025-05-17 08:00:06,535 - INFO - 订单分类完成: 有效订单 156 单，其中骑手卡 151 单，大流量卡 5 单
2025-05-17 08:00:06,535 - INFO - 统计各类订单按渠道分类...
2025-05-17 08:00:06,537 - INFO - 开始生成 2025-05-16 的报表数据...
2025-05-17 08:00:06,538 - INFO - 获取 2025-05-16 的订单数据（下单时间维度）...
2025-05-17 08:00:06,570 - INFO - 成功获取到 253 条去重后的订单数据（下单时间维度）
2025-05-17 08:00:06,571 - INFO - 开始分类并过滤订单数据...
2025-05-17 08:00:06,571 - INFO - 订单分类完成: 有效订单 253 单，其中骑手卡 240 单，流量卡 13 单
2025-05-17 08:00:06,571 - INFO - 计算各渠道的D0下单和D1激活数据...
2025-05-17 08:00:06,598 - INFO - 成功获取到 156 条当日激活订单
2025-05-17 08:00:06,600 - INFO - 样本订单日期检查 (共5个):
2025-05-17 08:00:06,600 - INFO - 订单 20250514100217481713: 下单时间=2025-05-14, 激活时间=2025-05-16, 相差=2天
2025-05-17 08:00:06,600 - INFO - 订单 20250515080552870373: 下单时间=2025-05-15, 激活时间=2025-05-16, 相差=1天
2025-05-17 08:00:06,600 - INFO - 订单 20250514145649494945: 下单时间=2025-05-14, 激活时间=2025-05-16, 相差=2天
2025-05-17 08:00:06,600 - INFO - 订单 20250515113103682316: 下单时间=2025-05-15, 激活时间=2025-05-16, 相差=1天
2025-05-17 08:00:06,600 - INFO - 订单 20250515100698589421: 下单时间=2025-05-15, 激活时间=2025-05-16, 相差=1天
2025-05-17 08:00:06,601 - INFO - 熟龄度处理统计: 总订单=156, 成功处理=156, 处理失败=0
2025-05-17 08:00:06,601 - INFO - 熟龄度分布: 当天=12, 昨天=94, 前天=25, 更早=25
2025-05-17 08:00:06,601 - INFO - 骑手卡熟龄度分布: 当天=12, 昨天=92, 前天=24, 更早=23
2025-05-17 08:00:06,601 - INFO - 流量卡熟龄度分布: 当天=0, 昨天=2, 前天=1, 更早=2
2025-05-17 08:00:06,601 - INFO - 获取报表日期与前一日订单数据对比...
2025-05-17 08:00:06,601 - INFO - 对比日期: 报表日期=2025-05-16, 前一日=2025-05-15
2025-05-17 08:00:06,601 - INFO - 获取 2025-05-16 的订单数据（下单时间维度）...
2025-05-17 08:00:06,632 - INFO - 成功获取到 253 条去重后的订单数据（下单时间维度）
2025-05-17 08:00:06,632 - INFO - 获取 2025-05-15 的订单数据（下单时间维度）...
2025-05-17 08:00:06,662 - INFO - 成功获取到 234 条去重后的订单数据（下单时间维度）
2025-05-17 08:00:06,663 - INFO - 获取周订单趋势数据...
2025-05-17 08:00:06,663 - INFO - 查询日期范围: 2025-05-10 00:00:00 至 2025-05-16 23:59:59
2025-05-17 08:00:06,687 - INFO - 成功获取到 7 天的订单趋势数据
2025-05-17 08:00:06,687 - INFO - 熟龄度数据: {'same_day': 12, 'one_day': 94, 'two_days': 25, 'more_days': 25}
2025-05-17 08:00:06,687 - INFO - 骑手卡熟龄度数据: {'same_day': 12, 'one_day': 92, 'two_days': 24, 'more_days': 23}, 总数: 151
2025-05-17 08:00:06,687 - INFO - 流量卡熟龄度数据: {'same_day': 0, 'one_day': 2, 'two_days': 1, 'more_days': 2}, 总数: 5
2025-05-17 08:00:06,687 - INFO - 当日下单当日激活比例: 7.7%（12/156）
2025-05-17 08:00:06,687 - INFO - 样本订单日期检查 (共5个): 156，熟龄度分布: 当天=12, 昨天=94, 前天=25, 更早=25
2025-05-17 08:00:06,687 - INFO - 环比分析: 当前=253单, 前一日=234单, 变化=8.1%
2025-05-17 08:00:06,687 - INFO - 周趋势数据获取成功，共7天数据
2025-05-17 08:00:06,687 - INFO - 开始发送模板卡片消息到企业微信...
2025-05-17 08:00:06,687 - INFO - 开始发送消息到企业微信（全渠道生产环境）...
2025-05-17 08:00:06,951 - INFO - 企业微信API响应状态码: 200
2025-05-17 08:00:06,951 - INFO - 企业微信消息发送成功
2025-05-17 08:00:06,951 - INFO - 昨天的日报发送成功
2025-05-17 08:00:06,952 - INFO - 昨天的日报处理完成。
2025-05-17 08:00:06,952 - INFO - ==================================================
[2025-05-17 08:00:02] 全渠道日报脚本执行成功
[2025-05-17 08:00:02] 开始执行OPPO渠道日报脚本...
2025-05-17 08:00:07,124 - INFO - ==================================================
2025-05-17 08:00:07,124 - INFO - 开始生成并发送OPPO和荣耀渠道日报（OPPO生产环境）...
2025-05-17 08:00:07,124 - INFO - 开始执行OPPO和荣耀渠道日报发送流程（OPPO生产环境）...
2025-05-17 08:00:07,140 - INFO - 查询日期: 2025-05-16
2025-05-17 08:00:07,140 - INFO - 获取 2025-05-16 的OPPO和荣耀渠道订单数据（下单时间维度）...
2025-05-17 08:00:07,160 - INFO - 查询到 13 条去重后的订单数据
2025-05-17 08:00:07,160 - INFO - 开始生成OPPO和荣耀渠道Markdown格式日报...
2025-05-17 08:00:07,160 - INFO - 开始处理订单数据，总数据条数: 13
2025-05-17 08:00:07,160 - INFO - 统计结果: 总订单 13
2025-05-17 08:00:07,160 - INFO -   OPPO: 8单
2025-05-17 08:00:07,160 - INFO -   荣耀: 5单
2025-05-17 08:00:07,160 - INFO - Markdown格式日报生成完成
2025-05-17 08:00:07,161 - INFO - 开始发送消息到企业微信（OPPO生产环境）...
2025-05-17 08:00:07,449 - INFO - 企业微信API响应状态码: 200
2025-05-17 08:00:07,449 - INFO - 企业微信消息发送成功
2025-05-17 08:00:07,449 - INFO - OPPO和荣耀渠道日报发送成功
2025-05-17 08:00:07,449 - INFO - OPPO和荣耀渠道日报处理完成。
2025-05-17 08:00:07,449 - INFO - ==================================================
[2025-05-17 08:00:02] OPPO渠道日报脚本执行成功
[2025-05-17 08:00:07] 所有日报脚本执行完成
----------------------------------------
