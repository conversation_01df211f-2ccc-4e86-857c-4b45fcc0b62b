2025-06-11 08:00:04,030 - INFO - ==================================================
2025-06-11 08:00:04,030 - INFO - 开始生成日报统计数据...
2025-06-11 08:00:04,062 - INFO - 开始生成 2025-06-10 的报表数据...
2025-06-11 08:00:04,063 - INFO - 获取 2025-06-10 的订单数据（下单时间维度）...
2025-06-11 08:00:04,116 - INFO - 成功获取到 223 条去重后的订单数据（下单时间维度）
2025-06-11 08:00:04,116 - INFO - 开始分类并过滤订单数据...
2025-06-11 08:00:04,116 - INFO - 订单分类完成: 有效订单 223 单，其中骑手卡 216 单，流量卡 7 单
2025-06-11 08:00:04,116 - INFO - 计算各渠道的D0下单和D1激活数据...
2025-06-11 08:00:04,149 - INFO - 成功获取到 142 条当日激活订单
2025-06-11 08:00:04,153 - INFO - 样本订单日期检查 (共5个):
2025-06-11 08:00:04,153 - INFO - 订单 20250609221207683029: 下单时间=2025-06-09, 激活时间=2025-06-10, 相差=1天
2025-06-11 08:00:04,153 - INFO - 订单 20250609143000500267: 下单时间=2025-06-09, 激活时间=2025-06-10, 相差=1天
2025-06-11 08:00:04,154 - INFO - 订单 20250609160062515823: 下单时间=2025-06-09, 激活时间=2025-06-10, 相差=1天
2025-06-11 08:00:04,154 - INFO - 订单 20250608041284761735: 下单时间=2025-06-08, 激活时间=2025-06-10, 相差=2天
2025-06-11 08:00:04,154 - INFO - 订单 20250608215554222448: 下单时间=2025-06-08, 激活时间=2025-06-10, 相差=2天
2025-06-11 08:00:04,154 - INFO - 熟龄度处理统计: 总订单=142, 成功处理=142, 处理失败=0
2025-06-11 08:00:04,154 - INFO - 熟龄度分布: 当天=9, 昨天=83, 前天=18, 更早=32
2025-06-11 08:00:04,154 - INFO - 骑手卡熟龄度分布: 当天=9, 昨天=83, 前天=18, 更早=32
2025-06-11 08:00:04,154 - INFO - 流量卡熟龄度分布: 当天=0, 昨天=0, 前天=0, 更早=0
2025-06-11 08:00:04,154 - INFO - 获取报表日期与前一日订单数据对比...
2025-06-11 08:00:04,155 - INFO - 对比日期: 报表日期=2025-06-10, 前一日=2025-06-09
2025-06-11 08:00:04,155 - INFO - 获取 2025-06-10 的订单数据（下单时间维度）...
2025-06-11 08:00:04,192 - INFO - 成功获取到 223 条去重后的订单数据（下单时间维度）
2025-06-11 08:00:04,192 - INFO - 获取 2025-06-09 的订单数据（下单时间维度）...
2025-06-11 08:00:04,232 - INFO - 成功获取到 211 条去重后的订单数据（下单时间维度）
2025-06-11 08:00:04,232 - INFO - 获取周订单趋势数据...
2025-06-11 08:00:04,232 - INFO - 查询日期范围: 2025-06-04 00:00:00 至 2025-06-10 23:59:59
2025-06-11 08:00:04,260 - INFO - 成功获取到 7 天的订单趋势数据
2025-06-11 08:00:04,260 - INFO - 熟龄度数据: {'same_day': 9, 'one_day': 83, 'two_days': 18, 'more_days': 32}
2025-06-11 08:00:04,260 - INFO - 骑手卡熟龄度数据: {'same_day': 9, 'one_day': 83, 'two_days': 18, 'more_days': 32}, 总数: 142
2025-06-11 08:00:04,260 - INFO - 流量卡熟龄度数据: {'same_day': 0, 'one_day': 0, 'two_days': 0, 'more_days': 0}, 总数: 0
2025-06-11 08:00:04,260 - INFO - 当日下单当日激活比例: 6.3%（9/142）
2025-06-11 08:00:04,260 - INFO - 样本订单日期检查 (共5个): 142，熟龄度分布: 当天=9, 昨天=83, 前天=18, 更早=32
2025-06-11 08:00:04,260 - INFO - 环比分析: 当前=223单, 前一日=211单, 变化=5.7%
2025-06-11 08:00:04,260 - INFO - 周趋势数据获取成功，共7天数据
2025-06-11 08:00:04,261 - INFO - 生成HTML报表模板，加载数据: 总活跃=142, 骑手卡=142, 流量卡=0
2025-06-11 08:00:04,261 - INFO - 骑手卡数据: [9, 83, 18, 32]
2025-06-11 08:00:04,261 - INFO - 流量卡数据: [0, 0, 0, 0]
2025-06-11 08:00:04,261 - INFO - HTML报表已生成: reports/report_20250610.html
2025-06-11 08:00:04,261 - INFO - 准备上传文件到FTP服务器: report_20250610.html
2025-06-11 08:00:04,261 - INFO - FTP配置: 主机=211.149.249.234, 端口=21, 目录=reports
2025-06-11 08:00:04,262 - INFO - 本地文件: reports/report_20250610.html, 大小: 51995 字节
2025-06-11 08:00:04,262 - INFO - 正在连接到FTP服务器: 211.149.249.234:21
2025-06-11 08:00:04,335 - INFO - 正在登录FTP服务器: 用户名=reports
2025-06-11 08:00:04,430 - INFO - FTP登录成功
2025-06-11 08:00:04,466 - INFO - 当前FTP目录: /
2025-06-11 08:00:04,466 - INFO - 尝试切换到目录: reports
2025-06-11 08:00:04,537 - INFO - 成功切换到目录: /reports
2025-06-11 08:00:04,677 - INFO - 上传前目录内容: ['report_20250506.html', 'report_20250522.html', 'report_20250511.html', 'report_20250515.html', 'report_20250605.html', 'report_20250518.html', 'report_20250516.html', 'report_20250512.html', 'report_20250503.html', 'report_20250602.html', 'report_20250508.html', 'report_20250609.html', 'report_20250502.html', 'report_20250601.html', 'report_20250531.html', 'report_20250519.html', 'report_20250608.html', 'report_20250525.html', '..', 'report_20250513.html', 'report_20250527.html', 'report_20250524.html', 'report_20250528.html', 'report_20250606.html', 'report_20250509.html', 'report_20250514.html', 'report_20250604.html', 'report_20250510.html', 'report_20250520.html', 'report_20250521.html', 'report_20250523.html', 'report_20250530.html', 'report_20250607.html', 'report_20250529.html', 'report_20250501.html', 'report_20250505.html', 'report_20250504.html', 'report_20250507.html', 'report_20250517.html', '.', 'report_20250526.html', 'report_20250603.html']
2025-06-11 08:00:04,677 - INFO - 开始上传文件: reports/report_20250610.html -> report_20250610.html
2025-06-11 08:00:04,926 - INFO - FTP上传结果: 226-File successfully transferred
226 0.106 seconds (measured here), 479.65 Kbytes per second
2025-06-11 08:00:05,065 - INFO - 上传后目录内容: ['report_20250506.html', 'report_20250522.html', 'report_20250511.html', 'report_20250515.html', 'report_20250605.html', 'report_20250518.html', 'report_20250610.html', 'report_20250516.html', 'report_20250512.html', 'report_20250503.html', 'report_20250602.html', 'report_20250508.html', 'report_20250609.html', 'report_20250502.html', 'report_20250601.html', 'report_20250531.html', 'report_20250519.html', 'report_20250608.html', 'report_20250525.html', '..', 'report_20250513.html', 'report_20250527.html', 'report_20250524.html', 'report_20250528.html', 'report_20250606.html', 'report_20250509.html', 'report_20250514.html', 'report_20250604.html', 'report_20250510.html', 'report_20250520.html', 'report_20250521.html', 'report_20250523.html', 'report_20250530.html', 'report_20250607.html', 'report_20250529.html', 'report_20250501.html', 'report_20250505.html', 'report_20250504.html', 'report_20250507.html', 'report_20250517.html', '.', 'report_20250526.html', 'report_20250603.html']
2025-06-11 08:00:05,065 - INFO - 文件已成功上传并验证: report_20250610.html
2025-06-11 08:00:05,065 - INFO - 文件访问URL: https://rep.331126.com/reports/report_20250610.html
2025-06-11 08:00:05,100 - INFO - FTP连接已关闭
2025-06-11 08:00:05,100 - INFO - 报表已上传到服务器，URL: https://rep.331126.com/reports/report_20250610.html
2025-06-11 08:00:05,100 - INFO - 日报生成完成
2025-06-11 08:00:05,100 - INFO - ==================================================
2025-06-11 08:00:05,309 - INFO - ==================================================
2025-06-11 08:00:05,309 - INFO - 开始生成并发送昨天的日报（全渠道生产环境）...
2025-06-11 08:00:05,310 - INFO - 开始执行昨天的日报发送流程（全渠道生产环境）...
2025-06-11 08:00:05,328 - INFO - 昨天日期: 2025-06-10
2025-06-11 08:00:05,329 - INFO - 开始获取昨天的订单数据（下单时间维度）...
2025-06-11 08:00:05,329 - INFO - 获取 2025-06-10 的订单数据（下单时间维度）...
2025-06-11 08:00:05,365 - INFO - 成功获取到 223 条去重后的订单数据（下单时间维度）
2025-06-11 08:00:05,366 - INFO - 开始获取昨天的订单数据（激活时间维度）...
2025-06-11 08:00:05,366 - INFO - 获取 2025-06-10 的订单数据（激活时间维度）...
2025-06-11 08:00:05,399 - INFO - 成功获取到 142 条去重后的订单数据（激活时间维度）
2025-06-11 08:00:05,399 - INFO - 成功获取到订单数据，继续生成日报
2025-06-11 08:00:05,399 - INFO - 开始生成昨天的日报...
2025-06-11 08:00:05,400 - INFO - 开始生成日报...
2025-06-11 08:00:05,400 - INFO - 开始分类并过滤订单数据...
2025-06-11 08:00:05,400 - INFO - 订单分类完成: 有效订单 223 单，其中骑手卡 216 单，大流量卡 7 单
2025-06-11 08:00:05,400 - INFO - 统计各类订单按渠道分类...
2025-06-11 08:00:05,400 - INFO - 开始分类并过滤订单数据...
2025-06-11 08:00:05,400 - INFO - 订单分类完成: 有效订单 142 单，其中骑手卡 142 单，大流量卡 0 单
2025-06-11 08:00:05,400 - INFO - 统计各类订单按渠道分类...
2025-06-11 08:00:05,401 - INFO - 日报生成完成
2025-06-11 08:00:05,401 - INFO - 开始分类并过滤订单数据...
2025-06-11 08:00:05,401 - INFO - 订单分类完成: 有效订单 223 单，其中骑手卡 216 单，大流量卡 7 单
2025-06-11 08:00:05,401 - INFO - 统计各类订单按渠道分类...
2025-06-11 08:00:05,401 - INFO - 开始分类并过滤订单数据...
2025-06-11 08:00:05,401 - INFO - 订单分类完成: 有效订单 142 单，其中骑手卡 142 单，大流量卡 0 单
2025-06-11 08:00:05,401 - INFO - 统计各类订单按渠道分类...
2025-06-11 08:00:05,403 - INFO - 开始生成 2025-06-10 的报表数据...
2025-06-11 08:00:05,403 - INFO - 获取 2025-06-10 的订单数据（下单时间维度）...
2025-06-11 08:00:05,441 - INFO - 成功获取到 223 条去重后的订单数据（下单时间维度）
2025-06-11 08:00:05,441 - INFO - 开始分类并过滤订单数据...
2025-06-11 08:00:05,441 - INFO - 订单分类完成: 有效订单 223 单，其中骑手卡 216 单，流量卡 7 单
2025-06-11 08:00:05,442 - INFO - 计算各渠道的D0下单和D1激活数据...
2025-06-11 08:00:05,475 - INFO - 成功获取到 142 条当日激活订单
2025-06-11 08:00:05,477 - INFO - 样本订单日期检查 (共5个):
2025-06-11 08:00:05,477 - INFO - 订单 20250609221207683029: 下单时间=2025-06-09, 激活时间=2025-06-10, 相差=1天
2025-06-11 08:00:05,477 - INFO - 订单 20250609143000500267: 下单时间=2025-06-09, 激活时间=2025-06-10, 相差=1天
2025-06-11 08:00:05,477 - INFO - 订单 20250609160062515823: 下单时间=2025-06-09, 激活时间=2025-06-10, 相差=1天
2025-06-11 08:00:05,477 - INFO - 订单 20250608041284761735: 下单时间=2025-06-08, 激活时间=2025-06-10, 相差=2天
2025-06-11 08:00:05,477 - INFO - 订单 20250608215554222448: 下单时间=2025-06-08, 激活时间=2025-06-10, 相差=2天
2025-06-11 08:00:05,478 - INFO - 熟龄度处理统计: 总订单=142, 成功处理=142, 处理失败=0
2025-06-11 08:00:05,478 - INFO - 熟龄度分布: 当天=9, 昨天=83, 前天=18, 更早=32
2025-06-11 08:00:05,478 - INFO - 骑手卡熟龄度分布: 当天=9, 昨天=83, 前天=18, 更早=32
2025-06-11 08:00:05,478 - INFO - 流量卡熟龄度分布: 当天=0, 昨天=0, 前天=0, 更早=0
2025-06-11 08:00:05,478 - INFO - 获取报表日期与前一日订单数据对比...
2025-06-11 08:00:05,478 - INFO - 对比日期: 报表日期=2025-06-10, 前一日=2025-06-09
2025-06-11 08:00:05,478 - INFO - 获取 2025-06-10 的订单数据（下单时间维度）...
2025-06-11 08:00:05,517 - INFO - 成功获取到 223 条去重后的订单数据（下单时间维度）
2025-06-11 08:00:05,517 - INFO - 获取 2025-06-09 的订单数据（下单时间维度）...
2025-06-11 08:00:05,555 - INFO - 成功获取到 211 条去重后的订单数据（下单时间维度）
2025-06-11 08:00:05,555 - INFO - 获取周订单趋势数据...
2025-06-11 08:00:05,555 - INFO - 查询日期范围: 2025-06-04 00:00:00 至 2025-06-10 23:59:59
2025-06-11 08:00:05,579 - INFO - 成功获取到 7 天的订单趋势数据
2025-06-11 08:00:05,580 - INFO - 熟龄度数据: {'same_day': 9, 'one_day': 83, 'two_days': 18, 'more_days': 32}
2025-06-11 08:00:05,580 - INFO - 骑手卡熟龄度数据: {'same_day': 9, 'one_day': 83, 'two_days': 18, 'more_days': 32}, 总数: 142
2025-06-11 08:00:05,580 - INFO - 流量卡熟龄度数据: {'same_day': 0, 'one_day': 0, 'two_days': 0, 'more_days': 0}, 总数: 0
2025-06-11 08:00:05,580 - INFO - 当日下单当日激活比例: 6.3%（9/142）
2025-06-11 08:00:05,580 - INFO - 样本订单日期检查 (共5个): 142，熟龄度分布: 当天=9, 昨天=83, 前天=18, 更早=32
2025-06-11 08:00:05,580 - INFO - 环比分析: 当前=223单, 前一日=211单, 变化=5.7%
2025-06-11 08:00:05,580 - INFO - 周趋势数据获取成功，共7天数据
2025-06-11 08:00:05,629 - INFO - 开始发送模板卡片消息到企业微信...
2025-06-11 08:00:05,629 - INFO - 开始发送消息到企业微信（全渠道生产环境）...
2025-06-11 08:00:05,889 - INFO - 企业微信API响应状态码: 200
2025-06-11 08:00:05,889 - INFO - 企业微信消息发送成功
2025-06-11 08:00:05,889 - INFO - 昨天的日报发送成功
2025-06-11 08:00:05,890 - INFO - 昨天的日报处理完成。
2025-06-11 08:00:05,890 - INFO - ==================================================
