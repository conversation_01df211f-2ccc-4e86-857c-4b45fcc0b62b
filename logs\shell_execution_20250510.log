[2025-05-10 08:00:01] 开始执行日报脚本
[2025-05-10 08:00:01] 工作目录: /volume3/SSD-soft/fzsrb
[2025-05-10 08:00:01] 检查Python依赖...
WARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager, possibly rendering your system unusable. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv. Use the --root-user-action option if you know what you are doing and want to suppress this warning.
[2025-05-10 08:00:01] 开始执行简单日报脚本...
2025-05-10 08:00:03,082 - INFO - ==================================================
2025-05-10 08:00:03,082 - INFO - 开始生成日报统计数据...
2025-05-10 08:00:03,125 - INFO - 开始生成 2025-05-09 的报表数据...
2025-05-10 08:00:03,125 - INFO - 获取 2025-05-09 的订单数据（下单时间维度）...
2025-05-10 08:00:03,156 - INFO - 成功获取到 198 条去重后的订单数据（下单时间维度）
2025-05-10 08:00:03,156 - INFO - 开始分类并过滤订单数据...
2025-05-10 08:00:03,156 - INFO - 订单分类完成: 有效订单 198 单，其中骑手卡 172 单，流量卡 26 单
2025-05-10 08:00:03,156 - INFO - 计算各渠道的D0下单和D1激活数据...
2025-05-10 08:00:03,184 - INFO - 成功获取到 206 条当日激活订单
2025-05-10 08:00:03,188 - INFO - 样本订单日期检查 (共5个):
2025-05-10 08:00:03,188 - INFO - 订单 20250508212364134151: 下单时间=2025-05-08, 激活时间=2025-05-09, 相差=1天
2025-05-10 08:00:03,188 - INFO - 订单 20250507165919013730: 下单时间=2025-05-07, 激活时间=2025-05-09, 相差=2天
2025-05-10 08:00:03,188 - INFO - 订单 20250508131532304716: 下单时间=2025-05-08, 激活时间=2025-05-09, 相差=1天
2025-05-10 08:00:03,188 - INFO - 订单 20250508230676538280: 下单时间=2025-05-08, 激活时间=2025-05-09, 相差=1天
2025-05-10 08:00:03,188 - INFO - 订单 20250508221827232193: 下单时间=2025-05-08, 激活时间=2025-05-09, 相差=1天
2025-05-10 08:00:03,188 - INFO - 熟龄度处理统计: 总订单=206, 成功处理=206, 处理失败=0
2025-05-10 08:00:03,189 - INFO - 熟龄度分布: 当天=9, 昨天=99, 前天=54, 更早=44
2025-05-10 08:00:03,189 - INFO - 骑手卡熟龄度分布: 当天=8, 昨天=97, 前天=52, 更早=34
2025-05-10 08:00:03,189 - INFO - 流量卡熟龄度分布: 当天=1, 昨天=2, 前天=2, 更早=10
2025-05-10 08:00:03,189 - INFO - 熟龄度数据: {'same_day': 9, 'one_day': 99, 'two_days': 54, 'more_days': 44}
2025-05-10 08:00:03,189 - INFO - 骑手卡熟龄度数据: {'same_day': 8, 'one_day': 97, 'two_days': 52, 'more_days': 34}, 总数: 191
2025-05-10 08:00:03,189 - INFO - 流量卡熟龄度数据: {'same_day': 1, 'one_day': 2, 'two_days': 2, 'more_days': 10}, 总数: 15
2025-05-10 08:00:03,189 - INFO - 当日下单当日激活比例: 4.4%（9/206）
2025-05-10 08:00:03,189 - INFO - 样本订单日期检查 (共5个): 206，熟龄度分布: 当天=9, 昨天=99, 前天=54, 更早=44
2025-05-10 08:00:03,190 - INFO - 生成HTML报表模板，加载数据: 总活跃=206, 骑手卡=191, 流量卡=15
2025-05-10 08:00:03,190 - INFO - 骑手卡数据: [8, 97, 52, 34]
2025-05-10 08:00:03,190 - INFO - 流量卡数据: [1, 2, 2, 10]
2025-05-10 08:00:03,190 - INFO - HTML报表已生成: reports/report_20250509.html
2025-05-10 08:00:03,190 - INFO - 准备上传文件到FTP服务器: report_20250509.html
2025-05-10 08:00:03,190 - INFO - FTP配置: 主机=211.149.249.234, 端口=21, 目录=reports
2025-05-10 08:00:03,190 - INFO - 本地文件: reports/report_20250509.html, 大小: 33274 字节
2025-05-10 08:00:03,190 - INFO - 正在连接到FTP服务器: 211.149.249.234:21
2025-05-10 08:00:03,269 - INFO - 正在登录FTP服务器: 用户名=reports
2025-05-10 08:00:03,377 - INFO - FTP登录成功
2025-05-10 08:00:03,414 - INFO - 当前FTP目录: /
2025-05-10 08:00:03,414 - INFO - 尝试切换到目录: reports
2025-05-10 08:00:03,489 - INFO - 成功切换到目录: /reports
2025-05-10 08:00:03,640 - INFO - 上传前目录内容: ['report_20250506.html', 'report_20250503.html', 'report_20250508.html', 'report_20250502.html', '..', 'report_20250501.html', 'report_20250505.html', 'report_20250504.html', 'report_20250507.html', '.']
2025-05-10 08:00:03,640 - INFO - 开始上传文件: reports/report_20250509.html -> report_20250509.html
2025-05-10 08:00:03,858 - INFO - FTP上传结果: 226-File successfully transferred
226 0.070 seconds (measured here), 465.73 Kbytes per second
2025-05-10 08:00:04,007 - INFO - 上传后目录内容: ['report_20250506.html', 'report_20250503.html', 'report_20250508.html', 'report_20250502.html', '..', 'report_20250509.html', 'report_20250501.html', 'report_20250505.html', 'report_20250504.html', 'report_20250507.html', '.']
2025-05-10 08:00:04,007 - INFO - 文件已成功上传并验证: report_20250509.html
2025-05-10 08:00:04,007 - INFO - 文件访问URL: https://rep.331126.com/reports/report_20250509.html
2025-05-10 08:00:04,044 - INFO - FTP连接已关闭
2025-05-10 08:00:04,044 - INFO - 报表已上传到服务器，URL: https://rep.331126.com/reports/report_20250509.html
2025-05-10 08:00:04,044 - INFO - 日报生成完成
2025-05-10 08:00:04,044 - INFO - ==================================================
*get* '220---------- Welcome to Pure-FTPd [privsep] [TLS] ----------\n'
*get* '220-You are user number 1 of 50 allowed.\n'
*get* '220-Local time is now 08:00. Server port: 21.\n'
*get* '220-This is a private system - No anonymous login\n'
*get* '220-IPv6 connections are also welcome on this server.\n'
*get* '220 You will be disconnected after 15 minutes of inactivity.\n'
*resp* '220---------- Welcome to Pure-FTPd [privsep] [TLS] ----------\n220-You are user number 1 of 50 allowed.\n220-Local time is now 08:00. Server port: 21.\n220-This is a private system - No anonymous login\n220-IPv6 connections are also welcome on this server.\n220 You will be disconnected after 15 minutes of inactivity.'
*cmd* 'USER reports'
*put* 'USER reports\r\n'
*get* '331 User reports OK. Password required\n'
*resp* '331 User reports OK. Password required'
*cmd* 'PASS ****************'
*put* 'PASS ****************\r\n'
*get* '230 OK. Current directory is /\n'
*resp* '230 OK. Current directory is /'
*cmd* 'PWD'
*put* 'PWD\r\n'
*get* '257 "/" is your current location\n'
*resp* '257 "/" is your current location'
*cmd* 'CWD reports'
*put* 'CWD reports\r\n'
*get* '250 OK. Current directory is /reports\n'
*resp* '250 OK. Current directory is /reports'
*cmd* 'PWD'
*put* 'PWD\r\n'
*get* '257 "/reports" is your current location\n'
*resp* '257 "/reports" is your current location'
*cmd* 'TYPE A'
*put* 'TYPE A\r\n'
*get* '200 TYPE is now ASCII\n'
*resp* '200 TYPE is now ASCII'
*cmd* 'PASV'
*put* 'PASV\r\n'
*get* '227 Entering Passive Mode (211,149,249,234,153,13)\n'
*resp* '227 Entering Passive Mode (211,149,249,234,153,13)'
*cmd* 'NLST'
*put* 'NLST\r\n'
*get* '150 Accepted data connection\n'
*resp* '150 Accepted data connection'
*get* '226 10 matches total\n'
*resp* '226 10 matches total'
*cmd* 'TYPE I'
*put* 'TYPE I\r\n'
*get* '200 TYPE is now 8-bit binary\n'
*resp* '200 TYPE is now 8-bit binary'
*cmd* 'PASV'
*put* 'PASV\r\n'
*get* '227 Entering Passive Mode (211,149,249,234,154,6)\n'
*resp* '227 Entering Passive Mode (211,149,249,234,154,6)'
*cmd* 'STOR report_20250509.html'
*put* 'STOR report_20250509.html\r\n'
*get* '150 Accepted data connection\n'
*resp* '150 Accepted data connection'
*get* '226-File successfully transferred\n'
*get* '226 0.070 seconds (measured here), 465.73 Kbytes per second\n'
*resp* '226-File successfully transferred\n226 0.070 seconds (measured here), 465.73 Kbytes per second'
*cmd* 'TYPE A'
*put* 'TYPE A\r\n'
*get* '200 TYPE is now ASCII\n'
*resp* '200 TYPE is now ASCII'
*cmd* 'PASV'
*put* 'PASV\r\n'
*get* '227 Entering Passive Mode (211,149,249,234,156,3)\n'
*resp* '227 Entering Passive Mode (211,149,249,234,156,3)'
*cmd* 'NLST'
*put* 'NLST\r\n'
*get* '150 Accepted data connection\n'
*resp* '150 Accepted data connection'
*get* '226 11 matches total\n'
*resp* '226 11 matches total'
*cmd* 'QUIT'
*put* 'QUIT\r\n'
*get* '221-Goodbye. You uploaded 33 and downloaded 0 kbytes.\n'
*get* '221 Logout.\n'
*resp* '221-Goodbye. You uploaded 33 and downloaded 0 kbytes.\n221 Logout.'
[2025-05-10 08:00:01] 简单日报脚本执行成功
[2025-05-10 08:00:01] 开始执行全渠道日报脚本...
2025-05-10 08:00:04,202 - INFO - ==================================================
2025-05-10 08:00:04,202 - INFO - 开始生成并发送昨天的日报（全渠道生产环境）...
2025-05-10 08:00:04,202 - INFO - 开始执行昨天的日报发送流程（全渠道生产环境）...
2025-05-10 08:00:04,219 - INFO - 昨天日期: 2025-05-09
2025-05-10 08:00:04,219 - INFO - 开始获取昨天的订单数据（下单时间维度）...
2025-05-10 08:00:04,219 - INFO - 获取 2025-05-09 的订单数据（下单时间维度）...
2025-05-10 08:00:04,246 - INFO - 成功获取到 198 条去重后的订单数据（下单时间维度）
2025-05-10 08:00:04,246 - INFO - 开始获取昨天的订单数据（激活时间维度）...
2025-05-10 08:00:04,246 - INFO - 获取 2025-05-09 的订单数据（激活时间维度）...
2025-05-10 08:00:04,273 - INFO - 成功获取到 206 条去重后的订单数据（激活时间维度）
2025-05-10 08:00:04,273 - INFO - 成功获取到订单数据，继续生成日报
2025-05-10 08:00:04,273 - INFO - 开始生成昨天的日报...
2025-05-10 08:00:04,274 - INFO - 开始生成日报...
2025-05-10 08:00:04,274 - INFO - 开始分类并过滤订单数据...
2025-05-10 08:00:04,274 - INFO - 订单分类完成: 有效订单 198 单，其中骑手卡 172 单，大流量卡 26 单
2025-05-10 08:00:04,274 - INFO - 统计各类订单按渠道分类...
2025-05-10 08:00:04,274 - INFO - 开始分类并过滤订单数据...
2025-05-10 08:00:04,274 - INFO - 订单分类完成: 有效订单 206 单，其中骑手卡 191 单，大流量卡 15 单
2025-05-10 08:00:04,274 - INFO - 统计各类订单按渠道分类...
2025-05-10 08:00:04,275 - INFO - 日报生成完成
2025-05-10 08:00:04,275 - INFO - 开始分类并过滤订单数据...
2025-05-10 08:00:04,275 - INFO - 订单分类完成: 有效订单 198 单，其中骑手卡 172 单，大流量卡 26 单
2025-05-10 08:00:04,275 - INFO - 统计各类订单按渠道分类...
2025-05-10 08:00:04,275 - INFO - 开始分类并过滤订单数据...
2025-05-10 08:00:04,275 - INFO - 订单分类完成: 有效订单 206 单，其中骑手卡 191 单，大流量卡 15 单
2025-05-10 08:00:04,275 - INFO - 统计各类订单按渠道分类...
2025-05-10 08:00:04,277 - INFO - 开始生成 2025-05-09 的报表数据...
2025-05-10 08:00:04,277 - INFO - 获取 2025-05-09 的订单数据（下单时间维度）...
2025-05-10 08:00:04,302 - INFO - 成功获取到 198 条去重后的订单数据（下单时间维度）
2025-05-10 08:00:04,303 - INFO - 开始分类并过滤订单数据...
2025-05-10 08:00:04,303 - INFO - 订单分类完成: 有效订单 198 单，其中骑手卡 172 单，流量卡 26 单
2025-05-10 08:00:04,303 - INFO - 计算各渠道的D0下单和D1激活数据...
2025-05-10 08:00:04,328 - INFO - 成功获取到 206 条当日激活订单
2025-05-10 08:00:04,330 - INFO - 样本订单日期检查 (共5个):
2025-05-10 08:00:04,330 - INFO - 订单 20250508212364134151: 下单时间=2025-05-08, 激活时间=2025-05-09, 相差=1天
2025-05-10 08:00:04,330 - INFO - 订单 20250507165919013730: 下单时间=2025-05-07, 激活时间=2025-05-09, 相差=2天
2025-05-10 08:00:04,330 - INFO - 订单 20250508131532304716: 下单时间=2025-05-08, 激活时间=2025-05-09, 相差=1天
2025-05-10 08:00:04,330 - INFO - 订单 20250508230676538280: 下单时间=2025-05-08, 激活时间=2025-05-09, 相差=1天
2025-05-10 08:00:04,330 - INFO - 订单 20250508221827232193: 下单时间=2025-05-08, 激活时间=2025-05-09, 相差=1天
2025-05-10 08:00:04,331 - INFO - 熟龄度处理统计: 总订单=206, 成功处理=206, 处理失败=0
2025-05-10 08:00:04,331 - INFO - 熟龄度分布: 当天=9, 昨天=99, 前天=54, 更早=44
2025-05-10 08:00:04,331 - INFO - 骑手卡熟龄度分布: 当天=8, 昨天=97, 前天=52, 更早=34
2025-05-10 08:00:04,331 - INFO - 流量卡熟龄度分布: 当天=1, 昨天=2, 前天=2, 更早=10
2025-05-10 08:00:04,331 - INFO - 熟龄度数据: {'same_day': 9, 'one_day': 99, 'two_days': 54, 'more_days': 44}
2025-05-10 08:00:04,331 - INFO - 骑手卡熟龄度数据: {'same_day': 8, 'one_day': 97, 'two_days': 52, 'more_days': 34}, 总数: 191
2025-05-10 08:00:04,331 - INFO - 流量卡熟龄度数据: {'same_day': 1, 'one_day': 2, 'two_days': 2, 'more_days': 10}, 总数: 15
2025-05-10 08:00:04,331 - INFO - 当日下单当日激活比例: 4.4%（9/206）
2025-05-10 08:00:04,331 - INFO - 样本订单日期检查 (共5个): 206，熟龄度分布: 当天=9, 昨天=99, 前天=54, 更早=44
2025-05-10 08:00:04,331 - INFO - 开始发送模板卡片消息到企业微信...
2025-05-10 08:00:04,332 - INFO - 开始发送消息到企业微信（全渠道生产环境）...
2025-05-10 08:00:04,572 - INFO - 企业微信API响应状态码: 200
2025-05-10 08:00:04,572 - INFO - 企业微信消息发送成功
2025-05-10 08:00:04,572 - INFO - 昨天的日报发送成功
2025-05-10 08:00:04,573 - INFO - 昨天的日报处理完成。
2025-05-10 08:00:04,573 - INFO - ==================================================
[2025-05-10 08:00:01] 全渠道日报脚本执行成功
[2025-05-10 08:00:01] 开始执行OPPO渠道日报脚本...
2025-05-10 08:00:04,786 - INFO - ==================================================
2025-05-10 08:00:04,786 - INFO - 开始生成并发送OPPO和荣耀渠道日报（OPPO生产环境）...
2025-05-10 08:00:04,786 - INFO - 开始执行OPPO和荣耀渠道日报发送流程（OPPO生产环境）...
2025-05-10 08:00:04,804 - INFO - 查询日期: 2025-05-09
2025-05-10 08:00:04,804 - INFO - 获取 2025-05-09 的OPPO和荣耀渠道订单数据（下单时间维度）...
2025-05-10 08:00:04,822 - INFO - 查询到 24 条去重后的订单数据
2025-05-10 08:00:04,822 - INFO - 开始生成OPPO和荣耀渠道Markdown格式日报...
2025-05-10 08:00:04,822 - INFO - 开始处理订单数据，总数据条数: 24
2025-05-10 08:00:04,822 - INFO - 统计结果: 总订单 24
2025-05-10 08:00:04,822 - INFO -   OPPO: 22单
2025-05-10 08:00:04,822 - INFO -   荣耀: 2单
2025-05-10 08:00:04,822 - INFO - Markdown格式日报生成完成
2025-05-10 08:00:04,823 - INFO - 开始发送消息到企业微信（OPPO生产环境）...
2025-05-10 08:00:05,083 - INFO - 企业微信API响应状态码: 200
2025-05-10 08:00:05,083 - INFO - 企业微信消息发送成功
2025-05-10 08:00:05,083 - INFO - OPPO和荣耀渠道日报发送成功
2025-05-10 08:00:05,083 - INFO - OPPO和荣耀渠道日报处理完成。
2025-05-10 08:00:05,083 - INFO - ==================================================
[2025-05-10 08:00:01] OPPO渠道日报脚本执行成功
[2025-05-10 08:00:05] 所有日报脚本执行完成
----------------------------------------
