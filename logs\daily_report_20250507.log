2025-05-07 07:05:42,368 - INFO - ==================================================
2025-05-07 07:05:42,368 - INFO - 开始生成日报统计数据...
2025-05-07 07:05:42,412 - INFO - 开始生成 2025-05-06 的报表数据...
2025-05-07 07:05:42,412 - INFO - 获取 2025-05-06 的订单数据（下单时间维度）...
2025-05-07 07:05:42,446 - INFO - 成功获取到 301 条去重后的订单数据（下单时间维度）
2025-05-07 07:05:42,446 - INFO - 开始分类并过滤订单数据...
2025-05-07 07:05:42,446 - INFO - 订单分类完成: 有效订单 301 单，其中骑手卡 262 单，流量卡 39 单
2025-05-07 07:05:42,446 - INFO - 计算各渠道的D0下单和D1激活数据...
2025-05-07 07:05:42,471 - INFO - 成功获取到 183 条当日激活订单
2025-05-07 07:05:42,474 - INFO - 样本订单日期检查 (共5个):
2025-05-07 07:05:42,474 - INFO - 订单 20250505122889525354: 下单时间=2025-05-05, 激活时间=2025-05-06, 相差=1天
2025-05-07 07:05:42,475 - INFO - 订单 20250503153937001317: 下单时间=2025-05-03, 激活时间=2025-05-06, 相差=3天
2025-05-07 07:05:42,475 - INFO - 订单 20250429232683660885: 下单时间=2025-04-29, 激活时间=2025-05-06, 相差=7天
2025-05-07 07:05:42,475 - INFO - 订单 20250505125099532418: 下单时间=2025-05-05, 激活时间=2025-05-06, 相差=1天
2025-05-07 07:05:42,475 - INFO - 订单 20250505122268494460: 下单时间=2025-05-05, 激活时间=2025-05-06, 相差=1天
2025-05-07 07:05:42,475 - INFO - 熟龄度处理统计: 总订单=183, 成功处理=183, 处理失败=0
2025-05-07 07:05:42,475 - INFO - 熟龄度分布: 当天=13, 昨天=74, 前天=35, 更早=61
2025-05-07 07:05:42,475 - INFO - 骑手卡熟龄度分布: 当天=13, 昨天=74, 前天=35, 更早=47
2025-05-07 07:05:42,475 - INFO - 流量卡熟龄度分布: 当天=0, 昨天=0, 前天=0, 更早=14
2025-05-07 07:05:42,476 - INFO - 熟龄度数据: {'same_day': 13, 'one_day': 74, 'two_days': 35, 'more_days': 61}
2025-05-07 07:05:42,476 - INFO - 骑手卡熟龄度数据: {'same_day': 13, 'one_day': 74, 'two_days': 35, 'more_days': 47}, 总数: 169
2025-05-07 07:05:42,476 - INFO - 流量卡熟龄度数据: {'same_day': 0, 'one_day': 0, 'two_days': 0, 'more_days': 14}, 总数: 14
2025-05-07 07:05:42,476 - INFO - 当日下单当日激活比例: 7.1%（13/183）
2025-05-07 07:05:42,476 - INFO - 样本订单日期检查 (共5个): 183，熟龄度分布: 当天=13, 昨天=74, 前天=35, 更早=61
2025-05-07 07:05:42,476 - INFO - 生成HTML报表模板，加载数据: 总活跃=183, 骑手卡=169, 流量卡=14
2025-05-07 07:05:42,476 - INFO - 骑手卡数据: [13, 74, 35, 47]
2025-05-07 07:05:42,476 - INFO - 流量卡数据: [0, 0, 0, 14]
2025-05-07 07:05:42,477 - INFO - HTML报表已生成: reports/report_20250506.html
2025-05-07 07:05:42,477 - INFO - 准备上传文件到FTP服务器: report_20250506.html
2025-05-07 07:05:42,477 - INFO - FTP配置: 主机=211.149.249.234, 端口=21, 目录=reports
2025-05-07 07:05:42,477 - INFO - 本地文件: reports/report_20250506.html, 大小: 33597 字节
2025-05-07 07:05:42,477 - INFO - 正在连接到FTP服务器: 211.149.249.234:21
2025-05-07 07:05:42,551 - INFO - 正在登录FTP服务器: 用户名=reports
2025-05-07 07:05:42,657 - INFO - FTP登录成功
2025-05-07 07:05:42,691 - INFO - 当前FTP目录: /
2025-05-07 07:05:42,691 - INFO - 尝试切换到目录: reports
2025-05-07 07:05:42,760 - INFO - 成功切换到目录: /reports
2025-05-07 07:05:42,904 - INFO - 上传前目录内容: ['report_20250503.html', 'report_20250502.html', '..', 'report_20250501.html', 'report_20250505.html', 'report_20250504.html', '.']
2025-05-07 07:05:42,904 - INFO - 开始上传文件: reports/report_20250506.html -> report_20250506.html
2025-05-07 07:05:43,122 - INFO - FTP上传结果: 226-File successfully transferred
226 0.076 seconds (measured here), 429.92 Kbytes per second
2025-05-07 07:05:43,263 - INFO - 上传后目录内容: ['report_20250506.html', 'report_20250503.html', 'report_20250502.html', '..', 'report_20250501.html', 'report_20250505.html', 'report_20250504.html', '.']
2025-05-07 07:05:43,264 - INFO - 文件已成功上传并验证: report_20250506.html
2025-05-07 07:05:43,264 - INFO - 文件访问URL: https://rep.331126.com/reports/report_20250506.html
2025-05-07 07:05:43,298 - INFO - FTP连接已关闭
2025-05-07 07:05:43,298 - INFO - 报表已上传到服务器，URL: https://rep.331126.com/reports/report_20250506.html
2025-05-07 07:05:43,298 - INFO - 日报生成完成
2025-05-07 07:05:43,298 - INFO - ==================================================
2025-05-07 07:05:43,454 - INFO - ==================================================
2025-05-07 07:05:43,455 - INFO - 开始生成并发送昨天的日报（测试环境）...
2025-05-07 07:05:43,455 - INFO - 开始执行昨天的日报发送流程（测试环境）...
2025-05-07 07:05:43,472 - INFO - 昨天日期: 2025-05-06
2025-05-07 07:05:43,472 - INFO - 开始获取昨天的订单数据（下单时间维度）...
2025-05-07 07:05:43,472 - INFO - 获取 2025-05-06 的订单数据（下单时间维度）...
2025-05-07 07:05:43,505 - INFO - 成功获取到 301 条去重后的订单数据（下单时间维度）
2025-05-07 07:05:43,505 - INFO - 开始获取昨天的订单数据（激活时间维度）...
2025-05-07 07:05:43,505 - INFO - 获取 2025-05-06 的订单数据（激活时间维度）...
2025-05-07 07:05:43,530 - INFO - 成功获取到 183 条去重后的订单数据（激活时间维度）
2025-05-07 07:05:43,530 - INFO - 成功获取到订单数据，继续生成日报
2025-05-07 07:05:43,530 - INFO - 开始生成昨天的日报...
2025-05-07 07:05:43,530 - INFO - 开始生成日报...
2025-05-07 07:05:43,530 - INFO - 开始分类并过滤订单数据...
2025-05-07 07:05:43,530 - INFO - 订单分类完成: 有效订单 301 单，其中骑手卡 262 单，大流量卡 39 单
2025-05-07 07:05:43,530 - INFO - 统计各类订单按渠道分类...
2025-05-07 07:05:43,531 - INFO - 开始分类并过滤订单数据...
2025-05-07 07:05:43,531 - INFO - 订单分类完成: 有效订单 183 单，其中骑手卡 169 单，大流量卡 14 单
2025-05-07 07:05:43,531 - INFO - 统计各类订单按渠道分类...
2025-05-07 07:05:43,531 - INFO - 日报生成完成
2025-05-07 07:05:43,531 - INFO - 开始分类并过滤订单数据...
2025-05-07 07:05:43,532 - INFO - 订单分类完成: 有效订单 301 单，其中骑手卡 262 单，大流量卡 39 单
2025-05-07 07:05:43,532 - INFO - 统计各类订单按渠道分类...
2025-05-07 07:05:43,532 - INFO - 开始分类并过滤订单数据...
2025-05-07 07:05:43,532 - INFO - 订单分类完成: 有效订单 183 单，其中骑手卡 169 单，大流量卡 14 单
2025-05-07 07:05:43,532 - INFO - 统计各类订单按渠道分类...
2025-05-07 07:05:43,534 - INFO - 开始生成 2025-05-06 的报表数据...
2025-05-07 07:05:43,534 - INFO - 获取 2025-05-06 的订单数据（下单时间维度）...
2025-05-07 07:05:43,563 - INFO - 成功获取到 301 条去重后的订单数据（下单时间维度）
2025-05-07 07:05:43,564 - INFO - 开始分类并过滤订单数据...
2025-05-07 07:05:43,564 - INFO - 订单分类完成: 有效订单 301 单，其中骑手卡 262 单，流量卡 39 单
2025-05-07 07:05:43,564 - INFO - 计算各渠道的D0下单和D1激活数据...
2025-05-07 07:05:43,586 - INFO - 成功获取到 183 条当日激活订单
2025-05-07 07:05:43,587 - INFO - 样本订单日期检查 (共5个):
2025-05-07 07:05:43,588 - INFO - 订单 20250505122889525354: 下单时间=2025-05-05, 激活时间=2025-05-06, 相差=1天
2025-05-07 07:05:43,588 - INFO - 订单 20250503153937001317: 下单时间=2025-05-03, 激活时间=2025-05-06, 相差=3天
2025-05-07 07:05:43,588 - INFO - 订单 20250429232683660885: 下单时间=2025-04-29, 激活时间=2025-05-06, 相差=7天
2025-05-07 07:05:43,588 - INFO - 订单 20250505125099532418: 下单时间=2025-05-05, 激活时间=2025-05-06, 相差=1天
2025-05-07 07:05:43,588 - INFO - 订单 20250505122268494460: 下单时间=2025-05-05, 激活时间=2025-05-06, 相差=1天
2025-05-07 07:05:43,588 - INFO - 熟龄度处理统计: 总订单=183, 成功处理=183, 处理失败=0
2025-05-07 07:05:43,588 - INFO - 熟龄度分布: 当天=13, 昨天=74, 前天=35, 更早=61
2025-05-07 07:05:43,588 - INFO - 骑手卡熟龄度分布: 当天=13, 昨天=74, 前天=35, 更早=47
2025-05-07 07:05:43,588 - INFO - 流量卡熟龄度分布: 当天=0, 昨天=0, 前天=0, 更早=14
2025-05-07 07:05:43,589 - INFO - 熟龄度数据: {'same_day': 13, 'one_day': 74, 'two_days': 35, 'more_days': 61}
2025-05-07 07:05:43,589 - INFO - 骑手卡熟龄度数据: {'same_day': 13, 'one_day': 74, 'two_days': 35, 'more_days': 47}, 总数: 169
2025-05-07 07:05:43,589 - INFO - 流量卡熟龄度数据: {'same_day': 0, 'one_day': 0, 'two_days': 0, 'more_days': 14}, 总数: 14
2025-05-07 07:05:43,589 - INFO - 当日下单当日激活比例: 7.1%（13/183）
2025-05-07 07:05:43,589 - INFO - 样本订单日期检查 (共5个): 183，熟龄度分布: 当天=13, 昨天=74, 前天=35, 更早=61
2025-05-07 07:05:43,589 - INFO - 开始发送模板卡片消息到企业微信...
2025-05-07 07:05:43,589 - INFO - 开始发送消息到企业微信（测试环境）...
2025-05-07 07:05:43,868 - INFO - 企业微信API响应状态码: 200
2025-05-07 07:05:43,869 - INFO - 企业微信消息发送成功
2025-05-07 07:05:43,869 - INFO - 昨天的日报发送成功
2025-05-07 07:05:43,869 - INFO - 昨天的日报处理完成。
2025-05-07 07:05:43,869 - INFO - ==================================================
2025-05-07 08:00:01,981 - INFO - ==================================================
2025-05-07 08:00:01,981 - INFO - 开始生成日报统计数据...
2025-05-07 08:00:02,000 - INFO - 开始生成 2025-05-06 的报表数据...
2025-05-07 08:00:02,000 - INFO - 获取 2025-05-06 的订单数据（下单时间维度）...
2025-05-07 08:00:02,032 - INFO - 成功获取到 301 条去重后的订单数据（下单时间维度）
2025-05-07 08:00:02,033 - INFO - 开始分类并过滤订单数据...
2025-05-07 08:00:02,033 - INFO - 订单分类完成: 有效订单 301 单，其中骑手卡 262 单，流量卡 39 单
2025-05-07 08:00:02,033 - INFO - 计算各渠道的D0下单和D1激活数据...
2025-05-07 08:00:02,057 - INFO - 成功获取到 183 条当日激活订单
2025-05-07 08:00:02,060 - INFO - 样本订单日期检查 (共5个):
2025-05-07 08:00:02,060 - INFO - 订单 20250505122889525354: 下单时间=2025-05-05, 激活时间=2025-05-06, 相差=1天
2025-05-07 08:00:02,060 - INFO - 订单 20250503153937001317: 下单时间=2025-05-03, 激活时间=2025-05-06, 相差=3天
2025-05-07 08:00:02,060 - INFO - 订单 20250429232683660885: 下单时间=2025-04-29, 激活时间=2025-05-06, 相差=7天
2025-05-07 08:00:02,060 - INFO - 订单 20250505125099532418: 下单时间=2025-05-05, 激活时间=2025-05-06, 相差=1天
2025-05-07 08:00:02,060 - INFO - 订单 20250505122268494460: 下单时间=2025-05-05, 激活时间=2025-05-06, 相差=1天
2025-05-07 08:00:02,061 - INFO - 熟龄度处理统计: 总订单=183, 成功处理=183, 处理失败=0
2025-05-07 08:00:02,061 - INFO - 熟龄度分布: 当天=13, 昨天=74, 前天=35, 更早=61
2025-05-07 08:00:02,061 - INFO - 骑手卡熟龄度分布: 当天=13, 昨天=74, 前天=35, 更早=47
2025-05-07 08:00:02,061 - INFO - 流量卡熟龄度分布: 当天=0, 昨天=0, 前天=0, 更早=14
2025-05-07 08:00:02,061 - INFO - 熟龄度数据: {'same_day': 13, 'one_day': 74, 'two_days': 35, 'more_days': 61}
2025-05-07 08:00:02,061 - INFO - 骑手卡熟龄度数据: {'same_day': 13, 'one_day': 74, 'two_days': 35, 'more_days': 47}, 总数: 169
2025-05-07 08:00:02,061 - INFO - 流量卡熟龄度数据: {'same_day': 0, 'one_day': 0, 'two_days': 0, 'more_days': 14}, 总数: 14
2025-05-07 08:00:02,061 - INFO - 当日下单当日激活比例: 7.1%（13/183）
2025-05-07 08:00:02,061 - INFO - 样本订单日期检查 (共5个): 183，熟龄度分布: 当天=13, 昨天=74, 前天=35, 更早=61
2025-05-07 08:00:02,062 - INFO - 生成HTML报表模板，加载数据: 总活跃=183, 骑手卡=169, 流量卡=14
2025-05-07 08:00:02,062 - INFO - 骑手卡数据: [13, 74, 35, 47]
2025-05-07 08:00:02,062 - INFO - 流量卡数据: [0, 0, 0, 14]
2025-05-07 08:00:02,062 - INFO - HTML报表已生成: reports/report_20250506.html
2025-05-07 08:00:02,062 - INFO - 准备上传文件到FTP服务器: report_20250506.html
2025-05-07 08:00:02,062 - INFO - FTP配置: 主机=211.149.249.234, 端口=21, 目录=reports
2025-05-07 08:00:02,062 - INFO - 本地文件: reports/report_20250506.html, 大小: 33597 字节
2025-05-07 08:00:02,063 - INFO - 正在连接到FTP服务器: 211.149.249.234:21
2025-05-07 08:00:02,140 - INFO - 正在登录FTP服务器: 用户名=reports
2025-05-07 08:00:02,237 - INFO - FTP登录成功
2025-05-07 08:00:02,275 - INFO - 当前FTP目录: /
2025-05-07 08:00:02,275 - INFO - 尝试切换到目录: reports
2025-05-07 08:00:02,351 - INFO - 成功切换到目录: /reports
2025-05-07 08:00:02,499 - INFO - 上传前目录内容: ['report_20250506.html', 'report_20250503.html', 'report_20250502.html', '..', 'report_20250501.html', 'report_20250505.html', 'report_20250504.html', '.']
2025-05-07 08:00:02,499 - INFO - 开始上传文件: reports/report_20250506.html -> report_20250506.html
2025-05-07 08:00:02,718 - INFO - FTP上传结果: 226-File successfully transferred
226 0.072 seconds (measured here), 455.02 Kbytes per second
2025-05-07 08:00:02,876 - INFO - 上传后目录内容: ['report_20250506.html', 'report_20250503.html', 'report_20250502.html', '..', 'report_20250501.html', 'report_20250505.html', 'report_20250504.html', '.']
2025-05-07 08:00:02,876 - INFO - 文件已成功上传并验证: report_20250506.html
2025-05-07 08:00:02,876 - INFO - 文件访问URL: https://rep.331126.com/reports/report_20250506.html
2025-05-07 08:00:02,914 - INFO - FTP连接已关闭
2025-05-07 08:00:02,914 - INFO - 报表已上传到服务器，URL: https://rep.331126.com/reports/report_20250506.html
2025-05-07 08:00:02,914 - INFO - 日报生成完成
2025-05-07 08:00:02,914 - INFO - ==================================================
2025-05-07 08:00:03,080 - INFO - ==================================================
2025-05-07 08:00:03,080 - INFO - 开始生成并发送昨天的日报（全渠道生产环境）...
2025-05-07 08:00:03,080 - INFO - 开始执行昨天的日报发送流程（全渠道生产环境）...
2025-05-07 08:00:03,098 - INFO - 昨天日期: 2025-05-06
2025-05-07 08:00:03,098 - INFO - 开始获取昨天的订单数据（下单时间维度）...
2025-05-07 08:00:03,098 - INFO - 获取 2025-05-06 的订单数据（下单时间维度）...
2025-05-07 08:00:03,130 - INFO - 成功获取到 301 条去重后的订单数据（下单时间维度）
2025-05-07 08:00:03,130 - INFO - 开始获取昨天的订单数据（激活时间维度）...
2025-05-07 08:00:03,130 - INFO - 获取 2025-05-06 的订单数据（激活时间维度）...
2025-05-07 08:00:03,155 - INFO - 成功获取到 183 条去重后的订单数据（激活时间维度）
2025-05-07 08:00:03,155 - INFO - 成功获取到订单数据，继续生成日报
2025-05-07 08:00:03,155 - INFO - 开始生成昨天的日报...
2025-05-07 08:00:03,155 - INFO - 开始生成日报...
2025-05-07 08:00:03,155 - INFO - 开始分类并过滤订单数据...
2025-05-07 08:00:03,156 - INFO - 订单分类完成: 有效订单 301 单，其中骑手卡 262 单，大流量卡 39 单
2025-05-07 08:00:03,156 - INFO - 统计各类订单按渠道分类...
2025-05-07 08:00:03,156 - INFO - 开始分类并过滤订单数据...
2025-05-07 08:00:03,156 - INFO - 订单分类完成: 有效订单 183 单，其中骑手卡 169 单，大流量卡 14 单
2025-05-07 08:00:03,156 - INFO - 统计各类订单按渠道分类...
2025-05-07 08:00:03,157 - INFO - 日报生成完成
2025-05-07 08:00:03,157 - INFO - 开始分类并过滤订单数据...
2025-05-07 08:00:03,157 - INFO - 订单分类完成: 有效订单 301 单，其中骑手卡 262 单，大流量卡 39 单
2025-05-07 08:00:03,157 - INFO - 统计各类订单按渠道分类...
2025-05-07 08:00:03,157 - INFO - 开始分类并过滤订单数据...
2025-05-07 08:00:03,157 - INFO - 订单分类完成: 有效订单 183 单，其中骑手卡 169 单，大流量卡 14 单
2025-05-07 08:00:03,158 - INFO - 统计各类订单按渠道分类...
2025-05-07 08:00:03,159 - INFO - 开始生成 2025-05-06 的报表数据...
2025-05-07 08:00:03,159 - INFO - 获取 2025-05-06 的订单数据（下单时间维度）...
2025-05-07 08:00:03,188 - INFO - 成功获取到 301 条去重后的订单数据（下单时间维度）
2025-05-07 08:00:03,188 - INFO - 开始分类并过滤订单数据...
2025-05-07 08:00:03,188 - INFO - 订单分类完成: 有效订单 301 单，其中骑手卡 262 单，流量卡 39 单
2025-05-07 08:00:03,188 - INFO - 计算各渠道的D0下单和D1激活数据...
2025-05-07 08:00:03,210 - INFO - 成功获取到 183 条当日激活订单
2025-05-07 08:00:03,212 - INFO - 样本订单日期检查 (共5个):
2025-05-07 08:00:03,212 - INFO - 订单 20250505122889525354: 下单时间=2025-05-05, 激活时间=2025-05-06, 相差=1天
2025-05-07 08:00:03,212 - INFO - 订单 20250503153937001317: 下单时间=2025-05-03, 激活时间=2025-05-06, 相差=3天
2025-05-07 08:00:03,212 - INFO - 订单 20250429232683660885: 下单时间=2025-04-29, 激活时间=2025-05-06, 相差=7天
2025-05-07 08:00:03,212 - INFO - 订单 20250505125099532418: 下单时间=2025-05-05, 激活时间=2025-05-06, 相差=1天
2025-05-07 08:00:03,212 - INFO - 订单 20250505122268494460: 下单时间=2025-05-05, 激活时间=2025-05-06, 相差=1天
2025-05-07 08:00:03,212 - INFO - 熟龄度处理统计: 总订单=183, 成功处理=183, 处理失败=0
2025-05-07 08:00:03,213 - INFO - 熟龄度分布: 当天=13, 昨天=74, 前天=35, 更早=61
2025-05-07 08:00:03,213 - INFO - 骑手卡熟龄度分布: 当天=13, 昨天=74, 前天=35, 更早=47
2025-05-07 08:00:03,213 - INFO - 流量卡熟龄度分布: 当天=0, 昨天=0, 前天=0, 更早=14
2025-05-07 08:00:03,213 - INFO - 熟龄度数据: {'same_day': 13, 'one_day': 74, 'two_days': 35, 'more_days': 61}
2025-05-07 08:00:03,213 - INFO - 骑手卡熟龄度数据: {'same_day': 13, 'one_day': 74, 'two_days': 35, 'more_days': 47}, 总数: 169
2025-05-07 08:00:03,213 - INFO - 流量卡熟龄度数据: {'same_day': 0, 'one_day': 0, 'two_days': 0, 'more_days': 14}, 总数: 14
2025-05-07 08:00:03,213 - INFO - 当日下单当日激活比例: 7.1%（13/183）
2025-05-07 08:00:03,213 - INFO - 样本订单日期检查 (共5个): 183，熟龄度分布: 当天=13, 昨天=74, 前天=35, 更早=61
2025-05-07 08:00:03,213 - INFO - 开始发送模板卡片消息到企业微信...
2025-05-07 08:00:03,213 - INFO - 开始发送消息到企业微信（全渠道生产环境）...
2025-05-07 08:00:03,463 - INFO - 企业微信API响应状态码: 200
2025-05-07 08:00:03,463 - INFO - 企业微信消息发送成功
2025-05-07 08:00:03,463 - INFO - 昨天的日报发送成功
2025-05-07 08:00:03,464 - INFO - 昨天的日报处理完成。
2025-05-07 08:00:03,464 - INFO - ==================================================
