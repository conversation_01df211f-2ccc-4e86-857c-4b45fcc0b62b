[2025-05-22 07:21:50] 开始执行日报脚本
[2025-05-22 07:21:50] 工作目录: /volume3/SSD-soft/fzsrb
[2025-05-22 07:21:50] 检查Python依赖...
WARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager, possibly rendering your system unusable. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv. Use the --root-user-action option if you know what you are doing and want to suppress this warning.
[2025-05-22 07:21:50] 开始执行简单日报脚本...
2025-05-22 07:21:51,665 - INFO - ==================================================
2025-05-22 07:21:51,665 - INFO - 开始生成日报统计数据...
2025-05-22 07:21:51,700 - INFO - 开始生成 2025-05-21 的报表数据...
2025-05-22 07:21:51,700 - INFO - 获取 2025-05-21 的订单数据（下单时间维度）...
2025-05-22 07:21:51,736 - INFO - 成功获取到 246 条去重后的订单数据（下单时间维度）
2025-05-22 07:21:51,737 - INFO - 开始分类并过滤订单数据...
2025-05-22 07:21:51,737 - INFO - 订单分类完成: 有效订单 246 单，其中骑手卡 244 单，流量卡 2 单
2025-05-22 07:21:51,737 - INFO - 计算各渠道的D0下单和D1激活数据...
2025-05-22 07:21:51,766 - INFO - 成功获取到 207 条当日激活订单
2025-05-22 07:21:51,790 - INFO - 样本订单日期检查 (共5个):
2025-05-22 07:21:51,791 - INFO - 订单 20250521132159486839: 下单时间=2025-05-21, 激活时间=2025-05-21, 相差=0天
2025-05-22 07:21:51,791 - INFO - 订单 20250520141417134197: 下单时间=2025-05-20, 激活时间=2025-05-21, 相差=1天
2025-05-22 07:21:51,791 - INFO - 订单 20250520174530552480: 下单时间=2025-05-20, 激活时间=2025-05-21, 相差=1天
2025-05-22 07:21:51,791 - INFO - 订单 20250518102205589843: 下单时间=2025-05-18, 激活时间=2025-05-21, 相差=3天
2025-05-22 07:21:51,791 - INFO - 订单 20250519063839941895: 下单时间=2025-05-19, 激活时间=2025-05-21, 相差=2天
2025-05-22 07:21:51,791 - INFO - 熟龄度处理统计: 总订单=207, 成功处理=207, 处理失败=0
2025-05-22 07:21:51,791 - INFO - 熟龄度分布: 当天=13, 昨天=104, 前天=53, 更早=37
2025-05-22 07:21:51,791 - INFO - 骑手卡熟龄度分布: 当天=13, 昨天=103, 前天=53, 更早=34
2025-05-22 07:21:51,791 - INFO - 流量卡熟龄度分布: 当天=0, 昨天=1, 前天=0, 更早=3
2025-05-22 07:21:51,792 - INFO - 获取报表日期与前一日订单数据对比...
2025-05-22 07:21:51,792 - INFO - 对比日期: 报表日期=2025-05-21, 前一日=2025-05-20
2025-05-22 07:21:51,792 - INFO - 获取 2025-05-21 的订单数据（下单时间维度）...
2025-05-22 07:21:51,822 - INFO - 成功获取到 246 条去重后的订单数据（下单时间维度）
2025-05-22 07:21:51,823 - INFO - 获取 2025-05-20 的订单数据（下单时间维度）...
2025-05-22 07:21:51,852 - INFO - 成功获取到 223 条去重后的订单数据（下单时间维度）
2025-05-22 07:21:51,852 - INFO - 获取周订单趋势数据...
2025-05-22 07:21:51,853 - INFO - 查询日期范围: 2025-05-15 00:00:00 至 2025-05-21 23:59:59
2025-05-22 07:21:51,879 - INFO - 成功获取到 7 天的订单趋势数据
2025-05-22 07:21:51,879 - INFO - 熟龄度数据: {'same_day': 13, 'one_day': 104, 'two_days': 53, 'more_days': 37}
2025-05-22 07:21:51,879 - INFO - 骑手卡熟龄度数据: {'same_day': 13, 'one_day': 103, 'two_days': 53, 'more_days': 34}, 总数: 203
2025-05-22 07:21:51,879 - INFO - 流量卡熟龄度数据: {'same_day': 0, 'one_day': 1, 'two_days': 0, 'more_days': 3}, 总数: 4
2025-05-22 07:21:51,879 - INFO - 当日下单当日激活比例: 6.3%（13/207）
2025-05-22 07:21:51,879 - INFO - 样本订单日期检查 (共5个): 207，熟龄度分布: 当天=13, 昨天=104, 前天=53, 更早=37
2025-05-22 07:21:51,879 - INFO - 环比分析: 当前=246单, 前一日=223单, 变化=10.3%
2025-05-22 07:21:51,879 - INFO - 周趋势数据获取成功，共7天数据
2025-05-22 07:21:51,879 - INFO - 生成HTML报表模板，加载数据: 总活跃=207, 骑手卡=203, 流量卡=4
2025-05-22 07:21:51,880 - INFO - 骑手卡数据: [13, 103, 53, 34]
2025-05-22 07:21:51,880 - INFO - 流量卡数据: [0, 1, 0, 3]
2025-05-22 07:21:51,880 - INFO - HTML报表已生成: reports/report_20250521.html
2025-05-22 07:21:51,880 - INFO - 准备上传文件到FTP服务器: report_20250521.html
2025-05-22 07:21:51,880 - INFO - FTP配置: 主机=211.149.249.234, 端口=21, 目录=reports
2025-05-22 07:21:51,880 - INFO - 本地文件: reports/report_20250521.html, 大小: 53838 字节
2025-05-22 07:21:51,880 - INFO - 正在连接到FTP服务器: 211.149.249.234:21
2025-05-22 07:21:51,951 - INFO - 正在登录FTP服务器: 用户名=reports
2025-05-22 07:21:52,040 - INFO - FTP登录成功
2025-05-22 07:21:52,074 - INFO - 当前FTP目录: /
2025-05-22 07:21:52,074 - INFO - 尝试切换到目录: reports
2025-05-22 07:21:52,143 - INFO - 成功切换到目录: /reports
2025-05-22 07:21:52,282 - INFO - 上传前目录内容: ['report_20250506.html', 'report_20250511.html', 'report_20250515.html', 'report_20250518.html', 'report_20250516.html', 'report_20250512.html', 'report_20250503.html', 'report_20250508.html', 'report_20250502.html', 'report_20250519.html', '..', 'report_20250513.html', 'report_20250509.html', 'report_20250514.html', 'report_20250510.html', 'report_20250520.html', 'report_20250501.html', 'report_20250505.html', 'report_20250504.html', 'report_20250507.html', 'report_20250517.html', '.']
2025-05-22 07:21:52,282 - INFO - 开始上传文件: reports/report_20250521.html -> report_20250521.html
2025-05-22 07:21:52,526 - INFO - FTP上传结果: 226-File successfully transferred
226 0.106 seconds (measured here), 495.97 Kbytes per second
2025-05-22 07:21:52,663 - INFO - 上传后目录内容: ['report_20250506.html', 'report_20250511.html', 'report_20250515.html', 'report_20250518.html', 'report_20250516.html', 'report_20250512.html', 'report_20250503.html', 'report_20250508.html', 'report_20250502.html', 'report_20250519.html', '..', 'report_20250513.html', 'report_20250509.html', 'report_20250514.html', 'report_20250510.html', 'report_20250520.html', 'report_20250521.html', 'report_20250501.html', 'report_20250505.html', 'report_20250504.html', 'report_20250507.html', 'report_20250517.html', '.']
2025-05-22 07:21:52,663 - INFO - 文件已成功上传并验证: report_20250521.html
2025-05-22 07:21:52,663 - INFO - 文件访问URL: https://rep.331126.com/reports/report_20250521.html
2025-05-22 07:21:52,696 - INFO - FTP连接已关闭
2025-05-22 07:21:52,697 - INFO - 报表已上传到服务器，URL: https://rep.331126.com/reports/report_20250521.html
2025-05-22 07:21:52,697 - INFO - 日报生成完成
2025-05-22 07:21:52,697 - INFO - ==================================================
*get* '220---------- Welcome to Pure-FTPd [privsep] [TLS] ----------\n'
*get* '220-You are user number 1 of 50 allowed.\n'
*get* '220-Local time is now 07:21. Server port: 21.\n'
*get* '220-This is a private system - No anonymous login\n'
*get* '220-IPv6 connections are also welcome on this server.\n'
*get* '220 You will be disconnected after 15 minutes of inactivity.\n'
*resp* '220---------- Welcome to Pure-FTPd [privsep] [TLS] ----------\n220-You are user number 1 of 50 allowed.\n220-Local time is now 07:21. Server port: 21.\n220-This is a private system - No anonymous login\n220-IPv6 connections are also welcome on this server.\n220 You will be disconnected after 15 minutes of inactivity.'
*cmd* 'USER reports'
*put* 'USER reports\r\n'
*get* '331 User reports OK. Password required\n'
*resp* '331 User reports OK. Password required'
*cmd* 'PASS ****************'
*put* 'PASS ****************\r\n'
*get* '230 OK. Current directory is /\n'
*resp* '230 OK. Current directory is /'
*cmd* 'PWD'
*put* 'PWD\r\n'
*get* '257 "/" is your current location\n'
*resp* '257 "/" is your current location'
*cmd* 'CWD reports'
*put* 'CWD reports\r\n'
*get* '250 OK. Current directory is /reports\n'
*resp* '250 OK. Current directory is /reports'
*cmd* 'PWD'
*put* 'PWD\r\n'
*get* '257 "/reports" is your current location\n'
*resp* '257 "/reports" is your current location'
*cmd* 'TYPE A'
*put* 'TYPE A\r\n'
*get* '200 TYPE is now ASCII\n'
*resp* '200 TYPE is now ASCII'
*cmd* 'PASV'
*put* 'PASV\r\n'
*get* '227 Entering Passive Mode (211,149,249,234,153,212)\n'
*resp* '227 Entering Passive Mode (211,149,249,234,153,212)'
*cmd* 'NLST'
*put* 'NLST\r\n'
*get* '150 Accepted data connection\n'
*resp* '150 Accepted data connection'
*get* '226 22 matches total\n'
*resp* '226 22 matches total'
*cmd* 'TYPE I'
*put* 'TYPE I\r\n'
*get* '200 TYPE is now 8-bit binary\n'
*resp* '200 TYPE is now 8-bit binary'
*cmd* 'PASV'
*put* 'PASV\r\n'
*get* '227 Entering Passive Mode (211,149,249,234,154,44)\n'
*resp* '227 Entering Passive Mode (211,149,249,234,154,44)'
*cmd* 'STOR report_20250521.html'
*put* 'STOR report_20250521.html\r\n'
*get* '150 Accepted data connection\n'
*resp* '150 Accepted data connection'
*get* '226-File successfully transferred\n'
*get* '226 0.106 seconds (measured here), 495.97 Kbytes per second\n'
*resp* '226-File successfully transferred\n226 0.106 seconds (measured here), 495.97 Kbytes per second'
*cmd* 'TYPE A'
*put* 'TYPE A\r\n'
*get* '200 TYPE is now ASCII\n'
*resp* '200 TYPE is now ASCII'
*cmd* 'PASV'
*put* 'PASV\r\n'
*get* '227 Entering Passive Mode (211,149,249,234,155,55)\n'
*resp* '227 Entering Passive Mode (211,149,249,234,155,55)'
*cmd* 'NLST'
*put* 'NLST\r\n'
*get* '150 Accepted data connection\n'
*resp* '150 Accepted data connection'
*get* '226 23 matches total\n'
*resp* '226 23 matches total'
*cmd* 'QUIT'
*put* 'QUIT\r\n'
*get* '221-Goodbye. You uploaded 53 and downloaded 0 kbytes.\n'
*get* '221 Logout.\n'
*resp* '221-Goodbye. You uploaded 53 and downloaded 0 kbytes.\n221 Logout.'
[2025-05-22 07:21:50] 简单日报脚本执行成功
[2025-05-22 07:21:50] 开始执行全渠道日报脚本...
2025-05-22 07:21:52,857 - INFO - ==================================================
2025-05-22 07:21:52,857 - INFO - 开始生成并发送昨天的日报（测试环境）...
2025-05-22 07:21:52,857 - INFO - 开始执行昨天的日报发送流程（测试环境）...
2025-05-22 07:21:52,874 - INFO - 昨天日期: 2025-05-21
2025-05-22 07:21:52,874 - INFO - 开始获取昨天的订单数据（下单时间维度）...
2025-05-22 07:21:52,874 - INFO - 获取 2025-05-21 的订单数据（下单时间维度）...
2025-05-22 07:21:52,906 - INFO - 成功获取到 246 条去重后的订单数据（下单时间维度）
2025-05-22 07:21:52,906 - INFO - 开始获取昨天的订单数据（激活时间维度）...
2025-05-22 07:21:52,906 - INFO - 获取 2025-05-21 的订单数据（激活时间维度）...
2025-05-22 07:21:52,935 - INFO - 成功获取到 207 条去重后的订单数据（激活时间维度）
2025-05-22 07:21:52,936 - INFO - 成功获取到订单数据，继续生成日报
2025-05-22 07:21:52,936 - INFO - 开始生成昨天的日报...
2025-05-22 07:21:52,936 - INFO - 开始生成日报...
2025-05-22 07:21:52,936 - INFO - 开始分类并过滤订单数据...
2025-05-22 07:21:52,936 - INFO - 订单分类完成: 有效订单 246 单，其中骑手卡 244 单，大流量卡 2 单
2025-05-22 07:21:52,936 - INFO - 统计各类订单按渠道分类...
2025-05-22 07:21:52,936 - INFO - 开始分类并过滤订单数据...
2025-05-22 07:21:52,936 - INFO - 订单分类完成: 有效订单 207 单，其中骑手卡 203 单，大流量卡 4 单
2025-05-22 07:21:52,936 - INFO - 统计各类订单按渠道分类...
2025-05-22 07:21:52,937 - INFO - 日报生成完成
2025-05-22 07:21:52,937 - INFO - 开始分类并过滤订单数据...
2025-05-22 07:21:52,937 - INFO - 订单分类完成: 有效订单 246 单，其中骑手卡 244 单，大流量卡 2 单
2025-05-22 07:21:52,937 - INFO - 统计各类订单按渠道分类...
2025-05-22 07:21:52,937 - INFO - 开始分类并过滤订单数据...
2025-05-22 07:21:52,937 - INFO - 订单分类完成: 有效订单 207 单，其中骑手卡 203 单，大流量卡 4 单
2025-05-22 07:21:52,937 - INFO - 统计各类订单按渠道分类...
2025-05-22 07:21:52,949 - INFO - 开始生成 2025-05-21 的报表数据...
2025-05-22 07:21:52,949 - INFO - 获取 2025-05-21 的订单数据（下单时间维度）...
2025-05-22 07:21:52,982 - INFO - 成功获取到 246 条去重后的订单数据（下单时间维度）
2025-05-22 07:21:52,982 - INFO - 开始分类并过滤订单数据...
2025-05-22 07:21:52,983 - INFO - 订单分类完成: 有效订单 246 单，其中骑手卡 244 单，流量卡 2 单
2025-05-22 07:21:52,983 - INFO - 计算各渠道的D0下单和D1激活数据...
2025-05-22 07:21:53,013 - INFO - 成功获取到 207 条当日激活订单
2025-05-22 07:21:53,015 - INFO - 样本订单日期检查 (共5个):
2025-05-22 07:21:53,015 - INFO - 订单 20250521132159486839: 下单时间=2025-05-21, 激活时间=2025-05-21, 相差=0天
2025-05-22 07:21:53,015 - INFO - 订单 20250520141417134197: 下单时间=2025-05-20, 激活时间=2025-05-21, 相差=1天
2025-05-22 07:21:53,015 - INFO - 订单 20250520174530552480: 下单时间=2025-05-20, 激活时间=2025-05-21, 相差=1天
2025-05-22 07:21:53,015 - INFO - 订单 20250518102205589843: 下单时间=2025-05-18, 激活时间=2025-05-21, 相差=3天
2025-05-22 07:21:53,015 - INFO - 订单 20250519063839941895: 下单时间=2025-05-19, 激活时间=2025-05-21, 相差=2天
2025-05-22 07:21:53,015 - INFO - 熟龄度处理统计: 总订单=207, 成功处理=207, 处理失败=0
2025-05-22 07:21:53,015 - INFO - 熟龄度分布: 当天=13, 昨天=104, 前天=53, 更早=37
2025-05-22 07:21:53,015 - INFO - 骑手卡熟龄度分布: 当天=13, 昨天=103, 前天=53, 更早=34
2025-05-22 07:21:53,015 - INFO - 流量卡熟龄度分布: 当天=0, 昨天=1, 前天=0, 更早=3
2025-05-22 07:21:53,016 - INFO - 获取报表日期与前一日订单数据对比...
2025-05-22 07:21:53,016 - INFO - 对比日期: 报表日期=2025-05-21, 前一日=2025-05-20
2025-05-22 07:21:53,016 - INFO - 获取 2025-05-21 的订单数据（下单时间维度）...
2025-05-22 07:21:53,049 - INFO - 成功获取到 246 条去重后的订单数据（下单时间维度）
2025-05-22 07:21:53,049 - INFO - 获取 2025-05-20 的订单数据（下单时间维度）...
2025-05-22 07:21:53,081 - INFO - 成功获取到 223 条去重后的订单数据（下单时间维度）
2025-05-22 07:21:53,081 - INFO - 获取周订单趋势数据...
2025-05-22 07:21:53,081 - INFO - 查询日期范围: 2025-05-15 00:00:00 至 2025-05-21 23:59:59
2025-05-22 07:21:53,107 - INFO - 成功获取到 7 天的订单趋势数据
2025-05-22 07:21:53,107 - INFO - 熟龄度数据: {'same_day': 13, 'one_day': 104, 'two_days': 53, 'more_days': 37}
2025-05-22 07:21:53,107 - INFO - 骑手卡熟龄度数据: {'same_day': 13, 'one_day': 103, 'two_days': 53, 'more_days': 34}, 总数: 203
2025-05-22 07:21:53,107 - INFO - 流量卡熟龄度数据: {'same_day': 0, 'one_day': 1, 'two_days': 0, 'more_days': 3}, 总数: 4
2025-05-22 07:21:53,107 - INFO - 当日下单当日激活比例: 6.3%（13/207）
2025-05-22 07:21:53,107 - INFO - 样本订单日期检查 (共5个): 207，熟龄度分布: 当天=13, 昨天=104, 前天=53, 更早=37
2025-05-22 07:21:53,107 - INFO - 环比分析: 当前=246单, 前一日=223单, 变化=10.3%
2025-05-22 07:21:53,107 - INFO - 周趋势数据获取成功，共7天数据
2025-05-22 07:21:53,126 - INFO - 开始发送模板卡片消息到企业微信...
2025-05-22 07:21:53,126 - INFO - 开始发送消息到企业微信（测试环境）...
2025-05-22 07:21:53,444 - INFO - 企业微信API响应状态码: 200
2025-05-22 07:21:53,444 - INFO - 企业微信消息发送成功
2025-05-22 07:21:53,444 - INFO - 昨天的日报发送成功
2025-05-22 07:21:53,445 - INFO - 昨天的日报处理完成。
2025-05-22 07:21:53,445 - INFO - ==================================================
[2025-05-22 07:21:50] 全渠道日报脚本执行成功
[2025-05-22 07:21:50] 开始执行OPPO渠道日报脚本...
2025-05-22 07:21:53,610 - INFO - ==================================================
2025-05-22 07:21:53,610 - INFO - 开始生成并发送OPPO和荣耀渠道日报（测试环境）...
2025-05-22 07:21:53,610 - INFO - 开始执行OPPO和荣耀渠道日报发送流程（测试环境）...
2025-05-22 07:21:53,626 - INFO - 查询日期: 2025-05-21
2025-05-22 07:21:53,626 - INFO - 获取 2025-05-21 的OPPO和荣耀渠道订单数据（下单时间维度）...
2025-05-22 07:21:53,645 - INFO - 查询到 0 条去重后的订单数据
2025-05-22 07:21:53,646 - WARNING - 未获取到OPPO和荣耀渠道订单数据，取消发送日报
2025-05-22 07:21:53,646 - WARNING - 由于没有数据，日报未发送
2025-05-22 07:21:53,646 - INFO - ==================================================
[2025-05-22 07:21:50] OPPO渠道日报脚本执行成功
[2025-05-22 07:21:53] 所有日报脚本执行完成
----------------------------------------
[2025-05-22 08:00:02] 开始执行日报脚本
[2025-05-22 08:00:02] 工作目录: /volume3/SSD-soft/fzsrb
[2025-05-22 08:00:02] 检查Python依赖...
WARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager, possibly rendering your system unusable. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv. Use the --root-user-action option if you know what you are doing and want to suppress this warning.
[2025-05-22 08:00:02] 开始执行简单日报脚本...
2025-05-22 08:00:02,605 - INFO - ==================================================
2025-05-22 08:00:02,606 - INFO - 开始生成日报统计数据...
2025-05-22 08:00:02,622 - INFO - 开始生成 2025-05-21 的报表数据...
2025-05-22 08:00:02,622 - INFO - 获取 2025-05-21 的订单数据（下单时间维度）...
2025-05-22 08:00:02,655 - INFO - 成功获取到 246 条去重后的订单数据（下单时间维度）
2025-05-22 08:00:02,656 - INFO - 开始分类并过滤订单数据...
2025-05-22 08:00:02,656 - INFO - 订单分类完成: 有效订单 246 单，其中骑手卡 244 单，流量卡 2 单
2025-05-22 08:00:02,656 - INFO - 计算各渠道的D0下单和D1激活数据...
2025-05-22 08:00:02,684 - INFO - 成功获取到 207 条当日激活订单
2025-05-22 08:00:02,687 - INFO - 样本订单日期检查 (共5个):
2025-05-22 08:00:02,688 - INFO - 订单 20250521132159486839: 下单时间=2025-05-21, 激活时间=2025-05-21, 相差=0天
2025-05-22 08:00:02,688 - INFO - 订单 20250520141417134197: 下单时间=2025-05-20, 激活时间=2025-05-21, 相差=1天
2025-05-22 08:00:02,688 - INFO - 订单 20250520174530552480: 下单时间=2025-05-20, 激活时间=2025-05-21, 相差=1天
2025-05-22 08:00:02,688 - INFO - 订单 20250518102205589843: 下单时间=2025-05-18, 激活时间=2025-05-21, 相差=3天
2025-05-22 08:00:02,688 - INFO - 订单 20250519063839941895: 下单时间=2025-05-19, 激活时间=2025-05-21, 相差=2天
2025-05-22 08:00:02,688 - INFO - 熟龄度处理统计: 总订单=207, 成功处理=207, 处理失败=0
2025-05-22 08:00:02,688 - INFO - 熟龄度分布: 当天=13, 昨天=104, 前天=53, 更早=37
2025-05-22 08:00:02,688 - INFO - 骑手卡熟龄度分布: 当天=13, 昨天=103, 前天=53, 更早=34
2025-05-22 08:00:02,688 - INFO - 流量卡熟龄度分布: 当天=0, 昨天=1, 前天=0, 更早=3
2025-05-22 08:00:02,689 - INFO - 获取报表日期与前一日订单数据对比...
2025-05-22 08:00:02,689 - INFO - 对比日期: 报表日期=2025-05-21, 前一日=2025-05-20
2025-05-22 08:00:02,689 - INFO - 获取 2025-05-21 的订单数据（下单时间维度）...
2025-05-22 08:00:02,719 - INFO - 成功获取到 246 条去重后的订单数据（下单时间维度）
2025-05-22 08:00:02,719 - INFO - 获取 2025-05-20 的订单数据（下单时间维度）...
2025-05-22 08:00:02,750 - INFO - 成功获取到 223 条去重后的订单数据（下单时间维度）
2025-05-22 08:00:02,750 - INFO - 获取周订单趋势数据...
2025-05-22 08:00:02,750 - INFO - 查询日期范围: 2025-05-15 00:00:00 至 2025-05-21 23:59:59
2025-05-22 08:00:02,775 - INFO - 成功获取到 7 天的订单趋势数据
2025-05-22 08:00:02,775 - INFO - 熟龄度数据: {'same_day': 13, 'one_day': 104, 'two_days': 53, 'more_days': 37}
2025-05-22 08:00:02,775 - INFO - 骑手卡熟龄度数据: {'same_day': 13, 'one_day': 103, 'two_days': 53, 'more_days': 34}, 总数: 203
2025-05-22 08:00:02,775 - INFO - 流量卡熟龄度数据: {'same_day': 0, 'one_day': 1, 'two_days': 0, 'more_days': 3}, 总数: 4
2025-05-22 08:00:02,775 - INFO - 当日下单当日激活比例: 6.3%（13/207）
2025-05-22 08:00:02,775 - INFO - 样本订单日期检查 (共5个): 207，熟龄度分布: 当天=13, 昨天=104, 前天=53, 更早=37
2025-05-22 08:00:02,775 - INFO - 环比分析: 当前=246单, 前一日=223单, 变化=10.3%
2025-05-22 08:00:02,775 - INFO - 周趋势数据获取成功，共7天数据
2025-05-22 08:00:02,776 - INFO - 生成HTML报表模板，加载数据: 总活跃=207, 骑手卡=203, 流量卡=4
2025-05-22 08:00:02,776 - INFO - 骑手卡数据: [13, 103, 53, 34]
2025-05-22 08:00:02,776 - INFO - 流量卡数据: [0, 1, 0, 3]
2025-05-22 08:00:02,776 - INFO - HTML报表已生成: reports/report_20250521.html
2025-05-22 08:00:02,777 - INFO - 准备上传文件到FTP服务器: report_20250521.html
2025-05-22 08:00:02,777 - INFO - FTP配置: 主机=211.149.249.234, 端口=21, 目录=reports
2025-05-22 08:00:02,777 - INFO - 本地文件: reports/report_20250521.html, 大小: 53838 字节
2025-05-22 08:00:02,777 - INFO - 正在连接到FTP服务器: 211.149.249.234:21
2025-05-22 08:00:02,849 - INFO - 正在登录FTP服务器: 用户名=reports
2025-05-22 08:00:02,952 - INFO - FTP登录成功
2025-05-22 08:00:02,988 - INFO - 当前FTP目录: /
2025-05-22 08:00:02,988 - INFO - 尝试切换到目录: reports
2025-05-22 08:00:03,058 - INFO - 成功切换到目录: /reports
2025-05-22 08:00:03,204 - INFO - 上传前目录内容: ['report_20250506.html', 'report_20250511.html', 'report_20250515.html', 'report_20250518.html', 'report_20250516.html', 'report_20250512.html', 'report_20250503.html', 'report_20250508.html', 'report_20250502.html', 'report_20250519.html', '..', 'report_20250513.html', 'report_20250509.html', 'report_20250514.html', 'report_20250510.html', 'report_20250520.html', 'report_20250521.html', 'report_20250501.html', 'report_20250505.html', 'report_20250504.html', 'report_20250507.html', 'report_20250517.html', '.']
2025-05-22 08:00:03,204 - INFO - 开始上传文件: reports/report_20250521.html -> report_20250521.html
2025-05-22 08:00:03,444 - INFO - FTP上传结果: 226-File successfully transferred
226 0.101 seconds (measured here), 0.51 Mbytes per second
2025-05-22 08:00:03,581 - INFO - 上传后目录内容: ['report_20250506.html', 'report_20250511.html', 'report_20250515.html', 'report_20250518.html', 'report_20250516.html', 'report_20250512.html', 'report_20250503.html', 'report_20250508.html', 'report_20250502.html', 'report_20250519.html', '..', 'report_20250513.html', 'report_20250509.html', 'report_20250514.html', 'report_20250510.html', 'report_20250520.html', 'report_20250521.html', 'report_20250501.html', 'report_20250505.html', 'report_20250504.html', 'report_20250507.html', 'report_20250517.html', '.']
2025-05-22 08:00:03,582 - INFO - 文件已成功上传并验证: report_20250521.html
2025-05-22 08:00:03,582 - INFO - 文件访问URL: https://rep.331126.com/reports/report_20250521.html
2025-05-22 08:00:03,617 - INFO - FTP连接已关闭
2025-05-22 08:00:03,617 - INFO - 报表已上传到服务器，URL: https://rep.331126.com/reports/report_20250521.html
2025-05-22 08:00:03,617 - INFO - 日报生成完成
2025-05-22 08:00:03,617 - INFO - ==================================================
*get* '220---------- Welcome to Pure-FTPd [privsep] [TLS] ----------\n'
*get* '220-You are user number 1 of 50 allowed.\n'
*get* '220-Local time is now 08:00. Server port: 21.\n'
*get* '220-This is a private system - No anonymous login\n'
*get* '220-IPv6 connections are also welcome on this server.\n'
*get* '220 You will be disconnected after 15 minutes of inactivity.\n'
*resp* '220---------- Welcome to Pure-FTPd [privsep] [TLS] ----------\n220-You are user number 1 of 50 allowed.\n220-Local time is now 08:00. Server port: 21.\n220-This is a private system - No anonymous login\n220-IPv6 connections are also welcome on this server.\n220 You will be disconnected after 15 minutes of inactivity.'
*cmd* 'USER reports'
*put* 'USER reports\r\n'
*get* '331 User reports OK. Password required\n'
*resp* '331 User reports OK. Password required'
*cmd* 'PASS ****************'
*put* 'PASS ****************\r\n'
*get* '230 OK. Current directory is /\n'
*resp* '230 OK. Current directory is /'
*cmd* 'PWD'
*put* 'PWD\r\n'
*get* '257 "/" is your current location\n'
*resp* '257 "/" is your current location'
*cmd* 'CWD reports'
*put* 'CWD reports\r\n'
*get* '250 OK. Current directory is /reports\n'
*resp* '250 OK. Current directory is /reports'
*cmd* 'PWD'
*put* 'PWD\r\n'
*get* '257 "/reports" is your current location\n'
*resp* '257 "/reports" is your current location'
*cmd* 'TYPE A'
*put* 'TYPE A\r\n'
*get* '200 TYPE is now ASCII\n'
*resp* '200 TYPE is now ASCII'
*cmd* 'PASV'
*put* 'PASV\r\n'
*get* '227 Entering Passive Mode (211,149,249,234,152,157)\n'
*resp* '227 Entering Passive Mode (211,149,249,234,152,157)'
*cmd* 'NLST'
*put* 'NLST\r\n'
*get* '150 Accepted data connection\n'
*resp* '150 Accepted data connection'
*get* '226 23 matches total\n'
*resp* '226 23 matches total'
*cmd* 'TYPE I'
*put* 'TYPE I\r\n'
*get* '200 TYPE is now 8-bit binary\n'
*resp* '200 TYPE is now 8-bit binary'
*cmd* 'PASV'
*put* 'PASV\r\n'
*get* '227 Entering Passive Mode (211,149,249,234,152,181)\n'
*resp* '227 Entering Passive Mode (211,149,249,234,152,181)'
*cmd* 'STOR report_20250521.html'
*put* 'STOR report_20250521.html\r\n'
*get* '150 Accepted data connection\n'
*resp* '150 Accepted data connection'
*get* '226-File successfully transferred\n'
*get* '226 0.101 seconds (measured here), 0.51 Mbytes per second\n'
*resp* '226-File successfully transferred\n226 0.101 seconds (measured here), 0.51 Mbytes per second'
*cmd* 'TYPE A'
*put* 'TYPE A\r\n'
*get* '200 TYPE is now ASCII\n'
*resp* '200 TYPE is now ASCII'
*cmd* 'PASV'
*put* 'PASV\r\n'
*get* '227 Entering Passive Mode (211,149,249,234,153,163)\n'
*resp* '227 Entering Passive Mode (211,149,249,234,153,163)'
*cmd* 'NLST'
*put* 'NLST\r\n'
*get* '150 Accepted data connection\n'
*resp* '150 Accepted data connection'
*get* '226 23 matches total\n'
*resp* '226 23 matches total'
*cmd* 'QUIT'
*put* 'QUIT\r\n'
*get* '221-Goodbye. You uploaded 53 and downloaded 0 kbytes.\n'
*get* '221 Logout.\n'
*resp* '221-Goodbye. You uploaded 53 and downloaded 0 kbytes.\n221 Logout.'
[2025-05-22 08:00:02] 简单日报脚本执行成功
[2025-05-22 08:00:02] 开始执行全渠道日报脚本...
2025-05-22 08:00:03,792 - INFO - ==================================================
2025-05-22 08:00:03,793 - INFO - 开始生成并发送昨天的日报（全渠道生产环境）...
2025-05-22 08:00:03,793 - INFO - 开始执行昨天的日报发送流程（全渠道生产环境）...
2025-05-22 08:00:03,812 - INFO - 昨天日期: 2025-05-21
2025-05-22 08:00:03,812 - INFO - 开始获取昨天的订单数据（下单时间维度）...
2025-05-22 08:00:03,812 - INFO - 获取 2025-05-21 的订单数据（下单时间维度）...
2025-05-22 08:00:03,848 - INFO - 成功获取到 246 条去重后的订单数据（下单时间维度）
2025-05-22 08:00:03,848 - INFO - 开始获取昨天的订单数据（激活时间维度）...
2025-05-22 08:00:03,848 - INFO - 获取 2025-05-21 的订单数据（激活时间维度）...
2025-05-22 08:00:03,881 - INFO - 成功获取到 207 条去重后的订单数据（激活时间维度）
2025-05-22 08:00:03,881 - INFO - 成功获取到订单数据，继续生成日报
2025-05-22 08:00:03,881 - INFO - 开始生成昨天的日报...
2025-05-22 08:00:03,881 - INFO - 开始生成日报...
2025-05-22 08:00:03,881 - INFO - 开始分类并过滤订单数据...
2025-05-22 08:00:03,881 - INFO - 订单分类完成: 有效订单 246 单，其中骑手卡 244 单，大流量卡 2 单
2025-05-22 08:00:03,881 - INFO - 统计各类订单按渠道分类...
2025-05-22 08:00:03,882 - INFO - 开始分类并过滤订单数据...
2025-05-22 08:00:03,882 - INFO - 订单分类完成: 有效订单 207 单，其中骑手卡 203 单，大流量卡 4 单
2025-05-22 08:00:03,882 - INFO - 统计各类订单按渠道分类...
2025-05-22 08:00:03,882 - INFO - 日报生成完成
2025-05-22 08:00:03,882 - INFO - 开始分类并过滤订单数据...
2025-05-22 08:00:03,883 - INFO - 订单分类完成: 有效订单 246 单，其中骑手卡 244 单，大流量卡 2 单
2025-05-22 08:00:03,883 - INFO - 统计各类订单按渠道分类...
2025-05-22 08:00:03,883 - INFO - 开始分类并过滤订单数据...
2025-05-22 08:00:03,883 - INFO - 订单分类完成: 有效订单 207 单，其中骑手卡 203 单，大流量卡 4 单
2025-05-22 08:00:03,883 - INFO - 统计各类订单按渠道分类...
2025-05-22 08:00:03,885 - INFO - 开始生成 2025-05-21 的报表数据...
2025-05-22 08:00:03,885 - INFO - 获取 2025-05-21 的订单数据（下单时间维度）...
2025-05-22 08:00:03,920 - INFO - 成功获取到 246 条去重后的订单数据（下单时间维度）
2025-05-22 08:00:03,920 - INFO - 开始分类并过滤订单数据...
2025-05-22 08:00:03,920 - INFO - 订单分类完成: 有效订单 246 单，其中骑手卡 244 单，流量卡 2 单
2025-05-22 08:00:03,920 - INFO - 计算各渠道的D0下单和D1激活数据...
2025-05-22 08:00:03,952 - INFO - 成功获取到 207 条当日激活订单
2025-05-22 08:00:03,954 - INFO - 样本订单日期检查 (共5个):
2025-05-22 08:00:03,955 - INFO - 订单 20250521132159486839: 下单时间=2025-05-21, 激活时间=2025-05-21, 相差=0天
2025-05-22 08:00:03,955 - INFO - 订单 20250520141417134197: 下单时间=2025-05-20, 激活时间=2025-05-21, 相差=1天
2025-05-22 08:00:03,955 - INFO - 订单 20250520174530552480: 下单时间=2025-05-20, 激活时间=2025-05-21, 相差=1天
2025-05-22 08:00:03,955 - INFO - 订单 20250518102205589843: 下单时间=2025-05-18, 激活时间=2025-05-21, 相差=3天
2025-05-22 08:00:03,955 - INFO - 订单 20250519063839941895: 下单时间=2025-05-19, 激活时间=2025-05-21, 相差=2天
2025-05-22 08:00:03,955 - INFO - 熟龄度处理统计: 总订单=207, 成功处理=207, 处理失败=0
2025-05-22 08:00:03,955 - INFO - 熟龄度分布: 当天=13, 昨天=104, 前天=53, 更早=37
2025-05-22 08:00:03,955 - INFO - 骑手卡熟龄度分布: 当天=13, 昨天=103, 前天=53, 更早=34
2025-05-22 08:00:03,955 - INFO - 流量卡熟龄度分布: 当天=0, 昨天=1, 前天=0, 更早=3
2025-05-22 08:00:03,956 - INFO - 获取报表日期与前一日订单数据对比...
2025-05-22 08:00:03,956 - INFO - 对比日期: 报表日期=2025-05-21, 前一日=2025-05-20
2025-05-22 08:00:03,956 - INFO - 获取 2025-05-21 的订单数据（下单时间维度）...
2025-05-22 08:00:03,990 - INFO - 成功获取到 246 条去重后的订单数据（下单时间维度）
2025-05-22 08:00:03,990 - INFO - 获取 2025-05-20 的订单数据（下单时间维度）...
2025-05-22 08:00:04,024 - INFO - 成功获取到 223 条去重后的订单数据（下单时间维度）
2025-05-22 08:00:04,025 - INFO - 获取周订单趋势数据...
2025-05-22 08:00:04,025 - INFO - 查询日期范围: 2025-05-15 00:00:00 至 2025-05-21 23:59:59
2025-05-22 08:00:04,051 - INFO - 成功获取到 7 天的订单趋势数据
2025-05-22 08:00:04,051 - INFO - 熟龄度数据: {'same_day': 13, 'one_day': 104, 'two_days': 53, 'more_days': 37}
2025-05-22 08:00:04,051 - INFO - 骑手卡熟龄度数据: {'same_day': 13, 'one_day': 103, 'two_days': 53, 'more_days': 34}, 总数: 203
2025-05-22 08:00:04,052 - INFO - 流量卡熟龄度数据: {'same_day': 0, 'one_day': 1, 'two_days': 0, 'more_days': 3}, 总数: 4
2025-05-22 08:00:04,052 - INFO - 当日下单当日激活比例: 6.3%（13/207）
2025-05-22 08:00:04,052 - INFO - 样本订单日期检查 (共5个): 207，熟龄度分布: 当天=13, 昨天=104, 前天=53, 更早=37
2025-05-22 08:00:04,052 - INFO - 环比分析: 当前=246单, 前一日=223单, 变化=10.3%
2025-05-22 08:00:04,052 - INFO - 周趋势数据获取成功，共7天数据
2025-05-22 08:00:04,066 - INFO - 开始发送模板卡片消息到企业微信...
2025-05-22 08:00:04,066 - INFO - 开始发送消息到企业微信（全渠道生产环境）...
2025-05-22 08:00:04,317 - INFO - 企业微信API响应状态码: 200
2025-05-22 08:00:04,318 - INFO - 企业微信消息发送成功
2025-05-22 08:00:04,318 - INFO - 昨天的日报发送成功
2025-05-22 08:00:04,318 - INFO - 昨天的日报处理完成。
2025-05-22 08:00:04,318 - INFO - ==================================================
[2025-05-22 08:00:02] 全渠道日报脚本执行成功
[2025-05-22 08:00:02] 开始执行OPPO渠道日报脚本...
2025-05-22 08:00:04,497 - INFO - ==================================================
2025-05-22 08:00:04,497 - INFO - 开始生成并发送OPPO和荣耀渠道日报（OPPO生产环境）...
2025-05-22 08:00:04,497 - INFO - 开始执行OPPO和荣耀渠道日报发送流程（OPPO生产环境）...
2025-05-22 08:00:04,514 - INFO - 查询日期: 2025-05-21
2025-05-22 08:00:04,514 - INFO - 获取 2025-05-21 的OPPO和荣耀渠道订单数据（下单时间维度）...
2025-05-22 08:00:04,534 - INFO - 查询到 0 条去重后的订单数据
2025-05-22 08:00:04,535 - WARNING - 未获取到OPPO和荣耀渠道订单数据，取消发送日报
2025-05-22 08:00:04,535 - WARNING - 由于没有数据，日报未发送
2025-05-22 08:00:04,535 - INFO - ==================================================
[2025-05-22 08:00:02] OPPO渠道日报脚本执行成功
[2025-05-22 08:00:04] 所有日报脚本执行完成
----------------------------------------
