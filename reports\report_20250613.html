<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>6月13日报表</title>
    <script src="https://b.zhoumeiren.cn/chart.min.js"></script>
    <style>
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 15px;
            background-color: #f5f5f5;
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        h1, h2, h3 {
            color: #333;
            margin-top: 0;
        }
        h1 {
            font-size: 1.8rem;
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        h2 {
            font-size: 1.5rem;
            margin-top: 25px;
            padding-bottom: 8px;
            border-bottom: 1px solid #eee;
        }
        .card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            padding: 15px;
            margin-bottom: 25px;
            border: 1px solid #eee;
        }
        .row {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 25px;
        }
        .col {
            flex: 1;
            min-width: 300px;
        }
        .summary {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 25px;
        }
        .summary-card {
            flex: 1 1 200px;
            background: linear-gradient(135deg, #6A82FB, #FC5C7D);
            color: white;
            border-radius: 8px;
            padding: 18px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.15);
            transition: transform 0.3s ease;
        }
        .summary-card:hover {
            transform: translateY(-3px);
        }
        .summary-card:nth-child(2) {
            background: linear-gradient(135deg, #11998e, #38ef7d);
        }
        .summary-card:nth-child(3) {
            background: linear-gradient(135deg, #FF8008, #FFC837);
        }
        .summary-card h3 {
            margin: 0;
            font-size: 1.1rem;
            color: rgba(255,255,255,0.9);
        }
        .summary-card p {
            margin: 12px 0 0;
            font-size: 2rem;
            font-weight: bold;
        }
        .summary-card .subtitle {
            font-size: 0.9rem;
            margin-top: 8px;
            opacity: 0.9;
        }
        
        /* 序号样式 */
        .num-cell {
            background-color: #f0f2f5;
            color: #666;
            font-weight: bold;
            border-radius: 50%;
            width: 28px;
            height: 28px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-right: 5px;
        }
        
        /* 数字标记样式 */
        .num-badge {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 30px;
            height: 30px;
            margin-right: 10px;
            background-color: #6A82FB;
            color: white;
            border-radius: 50%;
            font-size: 18px;
            font-weight: bold;
            line-height: 1;
        }
        .chart-container {
            position: relative;
            height: 400px;
            margin: 20px 0;
        }
        .chart-container.half-height {
            height: 300px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            font-size: 0.9rem;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            border-radius: 4px;
            overflow: hidden;
        }
        th, td {
            padding: 10px 8px;
            text-align: left;
            border-bottom: 1px solid #e0e0e0;
        }
        th {
            background-color: #f0f2f5;
            font-weight: 600;
            color: #444;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        tr:hover {
            background-color: #f0f4ff;
        }
        tfoot tr {
            background-color: #f0f2f5;
            font-weight: bold;
        }
        .positive {
            background-color: rgba(40, 167, 69, 0.1);
            color: #28a745;
            border-radius: 3px;
            padding: 2px 6px;
            font-weight: bold;
        }
        .negative {
            background-color: rgba(220, 53, 69, 0.1);
            color: #dc3545;
            border-radius: 3px;
            padding: 2px 6px;
            font-weight: bold;
        }
        .age-stat {
            display: flex;
            margin: 10px 0;
            border-radius: 4px;
            overflow: hidden;
            min-height: 30px;
        }
        .age-stat-item {
            padding: 8px 4px;
            text-align: center;
            color: white;
            font-size: 0.9rem;
            min-width: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .age-stat-labels {
            display: flex;
            margin-top: 5px;
            font-size: 0.85rem;
        }
        .age-stat-label {
            text-align: center;
            padding: 0 2px;
            white-space: nowrap;
            overflow: hidden;
            min-width: 30px;
        }
        .explanation {
            margin: 15px 0;
            padding: 12px;
            background-color: #f8f9fa;
            border-left: 3px solid #6A82FB;
            font-size: 0.9rem;
            line-height: 1.5;
        }
        .footnote {
            font-size: 0.8rem;
            color: #777;
            text-align: center;
            margin-top: 30px;
            padding-top: 10px;
            border-top: 1px solid #eee;
        }
        .detail-btn {
            background-color: #6A82FB;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 5px 10px;
            font-size: 0.8rem;
            cursor: pointer;
        }
        .detail-btn:hover {
            background-color: #5A72EB;
        }
        .modal {
            display: none;
            position: fixed;
            z-index: 100;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.4);
        }
        .modal-content {
            background-color: #fff;
            margin: 15% auto;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            width: 80%;
            max-width: 500px;
        }
        .modal-title {
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        .product-category {
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #f0f0f0;
        }
        .product-category:last-child {
            border-bottom: none;
            padding-bottom: 0;
        }
        .product-category-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .product-list {
            line-height: 1.6;
        }
        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        .close:hover {
            color: #000;
        }
        
        /* 手机端适配 */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            .container {
                padding: 15px;
            }
            h1 {
                font-size: 1.5rem;
            }
            h2 {
                font-size: 1.3rem;
            }
            .summary-card {
                flex: 1 1 100%;
            }
            .chart-container {
                height: 300px;
            }
            .col {
                flex: 1 1 100%;
            }
            table {
                font-size: 0.8rem;
            }
            th, td {
                padding: 8px 3px;
            }
            .age-stat {
                height: 40px;
            }
            .age-stat-item {
                font-size: 0.8rem;
                min-width: 30px;
            }
            .age-stat-labels {
                display: flex;
                margin-top: 10px;
            }
            .age-stat-label {
                text-align: center;
                font-size: 0.8rem;
                padding: 0;
            }
            .modal-content {
                width: 90%;
                margin: 30% auto;
            }
            .row{
                margin-bottom: 10px;
            }
        }
        .product-section {
            margin-bottom: 20px;
        }
        .product-section h3 {
            font-size: 1rem;
            margin-bottom: 10px;
            color: #333;
            font-weight: bold;
        }
        .mt-20 {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }
        .chart-filter .filter-btn {
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            color: #333;
            padding: 6px 12px;
            margin-right: 5px;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .chart-filter .filter-btn.active {
            background-color: #4bc0c0;
            color: white;
            border-color: #4bc0c0;
        }
        
        .chart-filter .filter-btn:hover {
            background-color: #e9ecef;
        }
        
        .chart-filter .filter-btn.active:hover {
            background-color: #3aafaf;
        }
        
        /* 环比分析样式 */
        .comparison-summary {
            margin: 15px 0;
        }
        .comparison-card {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .comparison-title {
            font-weight: bold;
            font-size: 1rem;
            margin-bottom: 10px;
            color: #333;
        }
        .comparison-values {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        .current-value {
            font-size: 1.6rem;
            font-weight: bold;
            color: #4bc0c0;
        }
        .previous-value {
            font-size: 1.2rem;
            color: #777;
            position: relative;
        }
        .previous-value:after {
            content: "";
            position: absolute;
            left: -5px;
            right: -5px;
            top: 50%;
            height: 1px;
            background-color: #ddd;
        }
        .change-value {
            font-size: 1rem;
            padding: 2px 8px;
            border-radius: 4px;
        }
        .positive {
            color: #28a745;
            background-color: rgba(40, 167, 69, 0.1);
        }
        .negative {
            color: #dc3545;
            background-color: rgba(220, 53, 69, 0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>6月13日报:订单与激活一览</h1>
        
        <div class="summary">
            <div class="summary-card">
                <h3>总订单量</h3>
                <p>192</p>
                <div class="subtitle">环比昨日：-11 (-5.4%)</div>
            </div>
            <div class="summary-card">
                <h3>骑手卡</h3>
                <p>189</p>
                <div class="subtitle">环比昨日：-9 (-4.5%)</div>
            </div>
            <div class="summary-card">
                <h3>流量卡</h3>
                <p>3</p>
                <div class="subtitle">环比昨日：-2 (-40.0%)</div>
            </div>
        </div>
        
        <!-- 环比分析部分 -->
        <div class="card" style="margin-top: 20px;">
            <h2><span class="num-badge">1</span> 订单环比（当日 VS 昨日）</h2>
            <div class="chart-container" style="height: 300px; margin-top: 20px;">
                <canvas id="comparisonChart"></canvas>
            </div>
            
            <div style="margin-top: 20px;">
                <h3>各渠道环比变化</h3>
                <table>
                    <thead>
                        <tr>
                            <th>渠道</th>
                            <th style="text-align:center">当日订单</th>
                            <th style="text-align:center">昨日订单</th>
                            <th style="text-align:center">变化</th>
                            <th style="text-align:center">变化率</th>
                        </tr>
                    </thead>
                    <tbody>
                        
            <tr>
                <td>顺丰同城</td>
                <td style="text-align:center">137</td>
                <td style="text-align:center">150</td>
                <td style="text-align:center">-13</td>
                <td class="negative" style="text-align:center">-8.7%</td>
            </tr>
        
            <tr>
                <td>驿舟</td>
                <td style="text-align:center">10</td>
                <td style="text-align:center">15</td>
                <td style="text-align:center">-5</td>
                <td class="negative" style="text-align:center">-33.3%</td>
            </tr>
        
            <tr>
                <td>公众号</td>
                <td style="text-align:center">6</td>
                <td style="text-align:center">2</td>
                <td style="text-align:center">+4</td>
                <td class="positive" style="text-align:center">+200.0%</td>
            </tr>
        
            <tr>
                <td>驿收发</td>
                <td style="text-align:center">15</td>
                <td style="text-align:center">12</td>
                <td style="text-align:center">+3</td>
                <td class="positive" style="text-align:center">+25.0%</td>
            </tr>
        
            <tr>
                <td>丰享</td>
                <td style="text-align:center">0</td>
                <td style="text-align:center">2</td>
                <td style="text-align:center">-2</td>
                <td class="negative" style="text-align:center">-100.0%</td>
            </tr>
        
            <tr>
                <td>丰巢</td>
                <td style="text-align:center">5</td>
                <td style="text-align:center">3</td>
                <td style="text-align:center">+2</td>
                <td class="positive" style="text-align:center">+66.7%</td>
            </tr>
        
            <tr>
                <td>驿收发切换</td>
                <td style="text-align:center">1</td>
                <td style="text-align:center">2</td>
                <td style="text-align:center">-1</td>
                <td class="negative" style="text-align:center">-50.0%</td>
            </tr>
        
            <tr>
                <td>湛江移动</td>
                <td style="text-align:center">1</td>
                <td style="text-align:center">0</td>
                <td style="text-align:center">+1</td>
                <td class="positive" style="text-align:center">+100%</td>
            </tr>
        
            <tr>
                <td>驿站公众号</td>
                <td style="text-align:center">17</td>
                <td style="text-align:center">17</td>
                <td style="text-align:center">+0</td>
                <td class="positive" style="text-align:center">+0.0%</td>
            </tr>
        
                    </tbody>
                </table>
            </div>
        </div>
        
        <div class="row">
            <div class="col">
                <div class="card">
                    <h2><span class="num-badge">2</span> 渠道订单与激活数据</h2>
                    <div class="chart-container">
                        <canvas id="channelChart"></canvas>
                    </div>
                    <table>
                        <thead>
                            <tr>
                                <th>渠道</th>
                                <th style="text-align:center">当日下单</th>
                                <th style="text-align:center">当日激活</th>
                                <th style="text-align:center">产品明细</th>
                            </tr>
                        </thead>
                        <tbody>
                            
        <tr>
            <td>顺丰同城</td>
            <td style="text-align:center">137</td>
            <td style="text-align:center">86</td>
            <td style="text-align:center"><button class="detail-btn" onclick="showDetails('channel-0')">明细</button></td>
        </tr>
        
        <tr>
            <td>驿站公众号</td>
            <td style="text-align:center">17</td>
            <td style="text-align:center">14</td>
            <td style="text-align:center"><button class="detail-btn" onclick="showDetails('channel-1')">明细</button></td>
        </tr>
        
        <tr>
            <td>驿收发</td>
            <td style="text-align:center">15</td>
            <td style="text-align:center">10</td>
            <td style="text-align:center"><button class="detail-btn" onclick="showDetails('channel-2')">明细</button></td>
        </tr>
        
        <tr>
            <td>驿舟</td>
            <td style="text-align:center">10</td>
            <td style="text-align:center">17</td>
            <td style="text-align:center"><button class="detail-btn" onclick="showDetails('channel-3')">明细</button></td>
        </tr>
        
        <tr>
            <td>公众号</td>
            <td style="text-align:center">6</td>
            <td style="text-align:center">4</td>
            <td style="text-align:center"><button class="detail-btn" onclick="showDetails('channel-4')">明细</button></td>
        </tr>
        
        <tr>
            <td>丰巢</td>
            <td style="text-align:center">5</td>
            <td style="text-align:center">4</td>
            <td style="text-align:center"><button class="detail-btn" onclick="showDetails('channel-5')">明细</button></td>
        </tr>
        
        <tr>
            <td>驿收发切换</td>
            <td style="text-align:center">1</td>
            <td style="text-align:center">3</td>
            <td style="text-align:center"><button class="detail-btn" onclick="showDetails('channel-6')">明细</button></td>
        </tr>
        
        <tr>
            <td>湛江移动</td>
            <td style="text-align:center">1</td>
            <td style="text-align:center">0</td>
            <td style="text-align:center"><button class="detail-btn" onclick="showDetails('channel-7')">明细</button></td>
        </tr>
        
        <tr>
            <td>丰享</td>
            <td style="text-align:center">0</td>
            <td style="text-align:center">1</td>
            <td style="text-align:center"><button class="detail-btn" onclick="showDetails('channel-8')">明细</button></td>
        </tr>
        
        <tr>
            <td>拍小租</td>
            <td style="text-align:center">0</td>
            <td style="text-align:center">9</td>
            <td style="text-align:center"><button class="detail-btn" onclick="showDetails('channel-9')">明细</button></td>
        </tr>
        
                        </tbody>
                        <tfoot>
                            <tr>
                                <th style="text-align:center">合计</th>
                                <th style="text-align:center">192</th>
                                <th style="text-align:center">148</th>
                                <th></th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
            
            <div class="col">
                <div class="card">
                    <h2><span class="num-badge">3</span> 当日激活订单的熟龄度分布</h2>
                    <div class="chart-filter" style="margin-bottom: 15px; text-align: center;">
                        <button class="filter-btn active" data-type="all" onclick="switchAgeChart('all')">全部</button>
                        <button class="filter-btn" data-type="rider" onclick="switchAgeChart('rider')">骑手卡</button>
                        <button class="filter-btn" data-type="data" onclick="switchAgeChart('data')">流量卡</button>
                    </div>
                    <div class="chart-container half-height">
                        <canvas id="ageDistributionChart"></canvas>
                    </div>
                    
                    <div>
                        <h3>当日激活订单的下单时间分布 <span id="ageDistributionTitle">(全部)</span></h3>
                        <div class="age-stat" id="ageStatBar">
                            <div class="age-stat-item" style="background-color: #36a2eb; width: 4.7%">
                                4.7%
                            </div>
                            <div class="age-stat-item" style="background-color: #4bc0c0; width: 54.1%">
                                54.1%
                            </div>
                            <div class="age-stat-item" style="background-color: #ffcd56; width: 25.7%">
                                25.7%
                            </div>
                            <div class="age-stat-item" style="background-color: #ff9f40; width: 15.5%">
                                15.5%
                            </div>
                        </div>
                        <div class="age-stat-labels">
                            <div class="age-stat-label" style="width: 4.7%">当天</div>
                            <div class="age-stat-label" style="width: 54.1%">昨天</div>
                            <div class="age-stat-label" style="width: 25.7%">前天</div>
                            <div class="age-stat-label" style="width: 15.5%">更早</div>
                        </div>
                        
                        <table id="ageDistributionTable" style="margin-top: 20px;">
                            <thead>
                                <tr>
                                    <th>下单时间</th>
                                    <th style="text-align:center">订单数</th>
                                    <th style="text-align:center">占比</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>当天</td>
                                    <td style="text-align:center">7</td>
                                    <td style="text-align:center">4.7%</td>
                                </tr>
                                <tr>
                                    <td>昨天</td>
                                    <td style="text-align:center">80</td>
                                    <td style="text-align:center">54.1%</td>
                                </tr>
                                <tr>
                                    <td>前天</td>
                                    <td style="text-align:center">38</td>
                                    <td style="text-align:center">25.7%</td>
                                </tr>
                                <tr>
                                    <td>更早</td>
                                    <td style="text-align:center">23</td>
                                    <td style="text-align:center">15.5%</td>
                                </tr>
                            </tbody>
                            <tfoot>
                                <tr>
                                    <th style="text-align:center">合计</th>
                                    <th style="text-align:center">148</th>
                                    <th style="text-align:center">100%</th>
                                </tr>
                            </tfoot>
                        </table>
                            <div>
            <div class="card" style="margin-top: 20px; padding: 15px;">
                <h3>熟龄度分析总结</h3>
                <p style="font-size: 14px; line-height: 1.6;">数据表明骑手卡激活速度显著快于流量卡，尤其当天激活率达4.7%（流量卡为0%），且近三日激活量占比84.5%（流量卡0%）</p>
            </div>
        </div>    
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 周订单趋势图 -->
        <div class="row">
            <div class="col">
                <div class="card" style="margin-top: 0px; padding: 15px;">
                    <h2><span class="num-badge">4</span> 最近七天订单趋势</h2>
                    <div class="chart-filter" style="margin-bottom: 15px; text-align: center;">
                        <button class="filter-btn active" data-type="all" onclick="switchTrendChart('all')">全部</button>
                        <button class="filter-btn" data-type="rider" onclick="switchTrendChart('rider')">骑手卡</button>
                        <button class="filter-btn" data-type="data" onclick="switchTrendChart('data')">流量卡</button>
                    </div>
                    <div class="chart-container" style="height: 350px;">
                        <canvas id="weeklyTrendChart"></canvas>
                    </div>
                    
                    <div style="margin-top: 20px;">
                        <table>
                            <thead>
                                <tr>
                                    <th>日期</th>
                                    <th style="text-align:center">总订单</th>
                                    <th style="text-align:center">骑手卡</th>
                                    <th style="text-align:center">流量卡</th>
                                </tr>
                            </thead>
                            <tbody>
                                
            <tr>
                <td>2025-06-07</td>
                <td style="text-align:center">174</td>
                <td style="text-align:center">172</td>
                <td style="text-align:center">2</td>
            </tr>
        
            <tr>
                <td>2025-06-08</td>
                <td style="text-align:center">153</td>
                <td style="text-align:center">152</td>
                <td style="text-align:center">1</td>
            </tr>
        
            <tr>
                <td>2025-06-09</td>
                <td style="text-align:center">213</td>
                <td style="text-align:center">210</td>
                <td style="text-align:center">3</td>
            </tr>
        
            <tr>
                <td>2025-06-10</td>
                <td style="text-align:center">218</td>
                <td style="text-align:center">211</td>
                <td style="text-align:center">7</td>
            </tr>
        
            <tr>
                <td>2025-06-11</td>
                <td style="text-align:center">236</td>
                <td style="text-align:center">233</td>
                <td style="text-align:center">3</td>
            </tr>
        
            <tr>
                <td>2025-06-12</td>
                <td style="text-align:center">203</td>
                <td style="text-align:center">198</td>
                <td style="text-align:center">5</td>
            </tr>
        
            <tr>
                <td>2025-06-13</td>
                <td style="text-align:center">192</td>
                <td style="text-align:center">189</td>
                <td style="text-align:center">3</td>
            </tr>
        
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <p class="footnote">数据生成时间: 2025-06-14 08:00:04</p>
    </div>
    
    <!-- 产品明细弹窗 -->
    <div id="detailsModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal()">&times;</span>
            <div id="modalContent"></div>
        </div>
    </div>

    <script>
        // 渠道产品明细数据
        const channelsProducts = {"\u987a\u4e30\u540c\u57ce": {"d0": {"\u9a91\u624b\u5361": ["\u96c6\u8fd059\u5143\u00d7118", "\u96c6\u8fd039\u5143\u00d716"], "\u6d41\u91cf\u5361": ["\u6df1\u5733\u8054\u901a59\u5143\u00d73"]}, "d1": {"\u9a91\u624b\u5361": ["\u96c6\u8fd059\u5143\u00d772", "\u96c6\u8fd039\u5143\u00d714"], "\u6d41\u91cf\u5361": []}}, "\u9a7f\u7ad9\u516c\u4f17\u53f7": {"d0": {"\u9a91\u624b\u5361": ["\u96c6\u8fd059\u5143\u00d717"], "\u6d41\u91cf\u5361": []}, "d1": {"\u9a91\u624b\u5361": ["\u96c6\u8fd059\u5143\u00d714"], "\u6d41\u91cf\u5361": []}}, "\u9a7f\u6536\u53d1": {"d0": {"\u9a91\u624b\u5361": ["\u96c6\u8fd059\u5143\u00d715"], "\u6d41\u91cf\u5361": []}, "d1": {"\u9a91\u624b\u5361": ["\u96c6\u8fd059\u5143\u00d710"], "\u6d41\u91cf\u5361": []}}, "\u9a7f\u821f": {"d0": {"\u9a91\u624b\u5361": ["\u96c6\u8fd059\u5143\u00d710"], "\u6d41\u91cf\u5361": []}, "d1": {"\u9a91\u624b\u5361": ["\u96c6\u8fd059\u5143\u00d717"], "\u6d41\u91cf\u5361": []}}, "\u516c\u4f17\u53f7": {"d0": {"\u9a91\u624b\u5361": ["\u96c6\u8fd059\u5143\u00d76"], "\u6d41\u91cf\u5361": []}, "d1": {"\u9a91\u624b\u5361": ["\u96c6\u8fd059\u5143\u00d74"], "\u6d41\u91cf\u5361": []}}, "\u4e30\u5de2": {"d0": {"\u9a91\u624b\u5361": ["\u96c6\u8fd059\u5143\u00d75"], "\u6d41\u91cf\u5361": []}, "d1": {"\u9a91\u624b\u5361": ["\u96c6\u8fd059\u5143\u00d74"], "\u6d41\u91cf\u5361": []}}, "\u9a7f\u6536\u53d1\u5207\u6362": {"d0": {"\u9a91\u624b\u5361": ["\u96c6\u8fd059\u5143\u00d71"], "\u6d41\u91cf\u5361": []}, "d1": {"\u9a91\u624b\u5361": ["\u96c6\u8fd059\u5143\u00d73"], "\u6d41\u91cf\u5361": []}}, "\u6e5b\u6c5f\u79fb\u52a8": {"d0": {"\u9a91\u624b\u5361": ["\u96c6\u8fd059\u5143\u00d71"], "\u6d41\u91cf\u5361": []}, "d1": {"\u9a91\u624b\u5361": [], "\u6d41\u91cf\u5361": []}}, "\u4e30\u4eab": {"d0": {"\u9a91\u624b\u5361": [], "\u6d41\u91cf\u5361": []}, "d1": {"\u9a91\u624b\u5361": ["\u96c6\u8fd059\u5143\u00d71"], "\u6d41\u91cf\u5361": []}}, "\u62cd\u5c0f\u79df": {"d0": {"\u9a91\u624b\u5361": [], "\u6d41\u91cf\u5361": []}, "d1": {"\u9a91\u624b\u5361": ["\u96c6\u8fd039\u5143\u00d75", "\u96c6\u8fd059\u5143\u00d74"], "\u6d41\u91cf\u5361": []}}};
        const channelNames = ["\u987a\u4e30\u540c\u57ce", "\u9a7f\u7ad9\u516c\u4f17\u53f7", "\u9a7f\u6536\u53d1", "\u9a7f\u821f", "\u516c\u4f17\u53f7", "\u4e30\u5de2", "\u9a7f\u6536\u53d1\u5207\u6362", "\u6e5b\u6c5f\u79fb\u52a8", "\u4e30\u4eab", "\u62cd\u5c0f\u79df"];
        
        // 显示明细弹窗
        function showDetails(channelId) {
            const index = parseInt(channelId.split('-')[1]);
            const channelName = channelNames[index];
            const products = channelsProducts[channelName];
            
            let modalHtml = '<div class="modal-title">' + channelName + ' 产品明细</div>';
            
            // 当日订单明细
            modalHtml += '<div class="product-section"><h3>当日订单明细：</h3>';
            
            // 骑手卡
            if (products.d0.骑手卡 && products.d0.骑手卡.length > 0) {
                modalHtml += '<div class="product-category">';
                modalHtml += '<div class="product-category-title">骑手卡：</div>';
                modalHtml += '<div class="product-list">';
                modalHtml += products.d0.骑手卡.join('，');
                modalHtml += '</div></div>';
            }
            
            // 流量卡
            if (products.d0.流量卡 && products.d0.流量卡.length > 0) {
                modalHtml += '<div class="product-category">';
                modalHtml += '<div class="product-category-title">流量卡：</div>';
                modalHtml += '<div class="product-list">';
                modalHtml += products.d0.流量卡.join('，');
                modalHtml += '</div></div>';
            }
            
            modalHtml += '</div>';
            
            // 当日激活明细
            modalHtml += '<div class="product-section mt-20"><h3>当日激活明细：</h3>';
            
            // 骑手卡
            if (products.d1.骑手卡 && products.d1.骑手卡.length > 0) {
                modalHtml += '<div class="product-category">';
                modalHtml += '<div class="product-category-title">骑手卡：</div>';
                modalHtml += '<div class="product-list">';
                modalHtml += products.d1.骑手卡.join('，');
                modalHtml += '</div></div>';
            }
            
            // 流量卡
            if (products.d1.流量卡 && products.d1.流量卡.length > 0) {
                modalHtml += '<div class="product-category">';
                modalHtml += '<div class="product-category-title">流量卡：</div>';
                modalHtml += '<div class="product-list">';
                modalHtml += products.d1.流量卡.join('，');
                modalHtml += '</div></div>';
            }
            
            modalHtml += '</div>';
            
            document.getElementById('modalContent').innerHTML = modalHtml;
            document.getElementById('detailsModal').style.display = 'block';
        }
        
        // 关闭弹窗
        function closeModal() {
            document.getElementById('detailsModal').style.display = 'none';
        }
        
        // 点击弹窗外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('detailsModal');
            if (event.target == modal) {
                modal.style.display = 'none';
            }
        };
        
        // 渠道图表数据准备
        const d0OrdersData = [137, 17, 15, 10, 6, 5, 1, 1, 0, 0];
        const d1ActivationsData = [86, 14, 10, 17, 4, 4, 3, 0, 1, 9];
        
        // 渠道图表渲染
        const channelCtx = document.getElementById('channelChart').getContext('2d');
        new Chart(channelCtx, {
            type: 'bar',
            data: {
                labels: channelNames,
                datasets: [
                    {
                        label: '当日下单',
                        data: d0OrdersData,
                        backgroundColor: 'rgba(54, 162, 235, 0.7)',
                        borderColor: 'rgba(54, 162, 235, 1)',
                        borderWidth: 1
                    },
                    {
                        label: '当日激活',
                        data: d1ActivationsData,
                        backgroundColor: 'rgba(75, 192, 192, 0.7)',
                        borderColor: 'rgba(75, 192, 192, 1)',
                        borderWidth: 1
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '订单数量'
                        }
                    }
                },
                plugins: {
                    title: {
                        display: true,
                        text: '各渠道订单与激活数据'
                    },
                    tooltip: {
                        callbacks: {
                            footer: function(tooltipItems) {
                                return ''; // 不显示激活率
                            }
                        }
                    }
                }
            }
        });
        
        // 激活熟龄度分布图
        const ageDistributionCtx = document.getElementById('ageDistributionChart').getContext('2d');
        let ageChart;
        
        // 环比分析图表
        const comparisonCtx = document.getElementById('comparisonChart').getContext('2d');
        
        // 周趋势图表
        const weeklyTrendCtx = document.getElementById('weeklyTrendChart').getContext('2d');
        let trendChart;
        
        // 定义三种不同类型的数据集
        const all_data = {
            labels: ["\u5f53\u5929", "\u6628\u5929", "\u524d\u5929", "\u66f4\u65e9"],
            counts: [7, 80, 38, 23],
            percentages: [4.7, 54.1, 25.7, 15.5],
            total: 148
        };
        
        const rider_data = {
            labels: ["\u5f53\u5929", "\u6628\u5929", "\u524d\u5929", "\u66f4\u65e9"],
            counts: [7, 80, 38, 23],
            percentages: [4.7, 54.1, 25.7, 15.5],
            total: 148
        };
        
        const data_card_data = {
            labels: ["\u5f53\u5929", "\u6628\u5929", "\u524d\u5929", "\u66f4\u65e9"],
            counts: [0, 0, 0, 0],
            percentages: [0, 0, 0, 0],
            total: 0
        };
        
        // 初始化图表
        function initAgeChart(type = 'all') {
            const chartData = type === 'rider' ? rider_data : 
                            type === 'data' ? data_card_data : all_data;
            
            // 如果图表已存在，先销毁
            if (ageChart) {
                ageChart.destroy();
            }
            
            ageChart = new Chart(ageDistributionCtx, {
                type: 'doughnut',
                data: {
                    labels: chartData.labels,
                    datasets: [{
                        data: chartData.counts,
                        backgroundColor: [
                            'rgba(54, 162, 235, 0.7)',  // 当天下单
                            'rgba(75, 192, 192, 0.7)',  // 昨天下单
                            'rgba(255, 205, 86, 0.7)',  // 前天下单
                            'rgba(255, 159, 64, 0.7)'   // 更早下单
                        ],
                        borderColor: [
                            'rgb(54, 162, 235)',
                            'rgb(75, 192, 192)',
                            'rgb(255, 205, 86)',
                            'rgb(255, 159, 64)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: '当日激活订单熟龄度分布'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.raw;
                                    const percentage = chartData.percentages[context.dataIndex];
                                    return `${label}: ${value} (${percentage}%)`;
                                }
                            }
                        },
                        legend: {
                            position: 'right',
                            labels: {
                                boxWidth: 15,
                                padding: 10,
                                font: {
                                    size: window.innerWidth < 768 ? 10 : 12
                                }
                            }
                        }
                    }
                }
            });
        }
        
        // 更新进度条
        function updateAgeStatBar(percentages) {
            const ageStatBar = document.getElementById('ageStatBar');
            ageStatBar.innerHTML = '';
            
            const colors = ['#36a2eb', '#4bc0c0', '#ffcd56', '#ff9f40'];
            const labels = ['当天', '昨天', '前天', '更早'];
            
            // 更新进度条
            percentages.forEach((percent, index) => {
                const width = Math.max(percent, 2);
                const div = document.createElement('div');
                div.className = 'age-stat-item';
                div.style.backgroundColor = colors[index];
                div.style.width = `${width}%`;
                div.textContent = `${percent}%`;
                ageStatBar.appendChild(div);
            });
            
            // 更新标签
            const labelsContainer = document.querySelector('.age-stat-labels');
            labelsContainer.innerHTML = '';
            
            percentages.forEach((percent, index) => {
                const width = Math.max(percent, 2);
                const div = document.createElement('div');
                div.className = 'age-stat-label';
                div.style.width = `${width}%`;
                div.textContent = labels[index];
                labelsContainer.appendChild(div);
            });
        }
        
        // 更新表格数据
        function updateAgeTable(data) {
            const table = document.getElementById('ageDistributionTable');
            const tbody = table.querySelector('tbody');
            const tfoot = table.querySelector('tfoot');
            
            // 清空表格内容
            tbody.innerHTML = '';
            
            // 添加新数据
            const labels = ['当天', '昨天', '前天', '更早'];
            data.counts.forEach((count, index) => {
                const tr = document.createElement('tr');
                
                const tdLabel = document.createElement('td');
                tdLabel.textContent = labels[index];
                
                const tdCount = document.createElement('td');
                tdCount.style.textAlign = 'center';
                tdCount.textContent = count;
                
                const tdPercent = document.createElement('td');
                tdPercent.style.textAlign = 'center';
                tdPercent.textContent = `${data.percentages[index]}%`;
                
                tr.appendChild(tdLabel);
                tr.appendChild(tdCount);
                tr.appendChild(tdPercent);
                
                tbody.appendChild(tr);
            });
            
            // 更新合计行
            tfoot.innerHTML = '';
            const tr = document.createElement('tr');
            
            const thLabel = document.createElement('th');
            thLabel.style.textAlign = 'center';
            thLabel.textContent = '合计';
            
            const thCount = document.createElement('th');
            thCount.style.textAlign = 'center';
            thCount.textContent = data.total;
            
            const thPercent = document.createElement('th');
            thPercent.style.textAlign = 'center';
            thPercent.textContent = '100%';
            
            tr.appendChild(thLabel);
            tr.appendChild(thCount);
            tr.appendChild(thPercent);
            
            tfoot.appendChild(tr);
        }
        
        // 切换卡类型的函数
        function switchAgeChart(type) {
            // 更新按钮样式
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`.filter-btn[data-type="${type}"]`).classList.add('active');
            
            // 设置标题
            const title = type === 'rider' ? '(骑手卡)' : 
                        type === 'data' ? '(流量卡)' : '(全部)';
            document.getElementById('ageDistributionTitle').textContent = title;
            
            // 获取相应数据
            const chartData = type === 'rider' ? rider_data : 
                            type === 'data' ? data_card_data : all_data;
            
            // 更新图表
            if (ageChart) {
                ageChart.data.datasets[0].data = chartData.counts;
                ageChart.update();
            } else {
                initAgeChart(type);
            }
            
            // 更新进度条
            updateAgeStatBar(chartData.percentages);
            
            // 更新表格
            updateAgeTable(chartData);
        }
        
        // 初始化图表
        document.addEventListener('DOMContentLoaded', function() {
            initAgeChart('all');
            
            // 绑定按钮点击事件
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const type = this.getAttribute('data-type');
                    switchAgeChart(type);
                });
            });
        });
        
        // 环比分析图表数据
        const comparisonLabels = ["\u603b\u8ba2\u5355", "\u9a91\u624b\u5361", "\u6d41\u91cf\u5361"];
        const currentData = [192, 189, 3];
        const previousData = [203, 198, 5];
        
        // 渲染环比分析图表
        new Chart(comparisonCtx, {
            type: 'bar',
            data: {
                labels: comparisonLabels,
                datasets: [
                    {
                        label: '当日',
                        data: currentData,
                        backgroundColor: 'rgba(75, 192, 192, 0.7)',
                        borderColor: 'rgba(75, 192, 192, 1)',
                        borderWidth: 1
                    },
                    {
                        label: '昨日',
                        data: previousData,
                        backgroundColor: 'rgba(54, 162, 235, 0.7)',
                        borderColor: 'rgba(54, 162, 235, 1)',
                        borderWidth: 1
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '订单数量'
                        }
                    }
                },
                plugins: {
                    title: {
                        display: true,
                        text: '订单环比分析（当日 VS 昨日）'
                    },
                    tooltip: {
                        callbacks: {
                            footer: function(tooltipItems) {
                                return ''; // 不显示激活率
                            }
                        }
                    }
                }
            }
        });
        
        // 周趋势图表数据
        const weeklyDates = ["2025-06-07", "2025-06-08", "2025-06-09", "2025-06-10", "2025-06-11", "2025-06-12", "2025-06-13"];
        const weeklyTotalData = [174, 153, 213, 218, 236, 203, 192];
        const weeklyRiderData = [172, 152, 210, 211, 233, 198, 189];
        const weeklyDataCardData = [2, 1, 3, 7, 3, 5, 3];
        
        // 初始化周趋势图表
        function initTrendChart(type = 'all') {
            // 如果图表已存在，先销毁
            if (trendChart) {
                trendChart.destroy();
            }
            
            trendChart = new Chart(weeklyTrendCtx, {
                type: 'line',
                data: {
                    labels: weeklyDates,
                    datasets: [
                        {
                            label: '总订单',
                            data: weeklyTotalData,
                            borderColor: 'rgba(54, 162, 235, 1)',
                            borderWidth: 2,
                            fill: false
                        },
                        {
                            label: '骑手卡',
                            data: weeklyRiderData,
                            borderColor: 'rgba(75, 192, 192, 1)',
                            borderWidth: 2,
                            fill: false
                        },
                        {
                            label: '流量卡',
                            data: weeklyDataCardData,
                            borderColor: 'rgba(255, 205, 86, 1)',
                            borderWidth: 2,
                            fill: false
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '订单数量'
                            }
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: '周订单趋势'
                        },
                        legend: {
                            position: 'top',
                            labels: {
                                boxWidth: 15,
                                padding: 10,
                                font: {
                                    size: window.innerWidth < 768 ? 10 : 12
                                }
                            }
                        }
                    }
                }
            });
        }
        
        // 切换卡类型的函数
        function switchTrendChart(type) {
            // 更新按钮样式
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`.filter-btn[data-type="${type}"]`).classList.add('active');
            
            if (type === 'all') {
                trendChart.data.datasets[0].hidden = false;
                trendChart.data.datasets[1].hidden = false;
                trendChart.data.datasets[2].hidden = false;
            } else if (type === 'rider') {
                trendChart.data.datasets[0].hidden = true;
                trendChart.data.datasets[1].hidden = false;
                trendChart.data.datasets[2].hidden = true;
            } else if (type === 'data') {
                trendChart.data.datasets[0].hidden = true;
                trendChart.data.datasets[1].hidden = true;
                trendChart.data.datasets[2].hidden = false;
            }
            
            trendChart.update();
        }
        
        // 初始化图表
        document.addEventListener('DOMContentLoaded', function() {
            initAgeChart('all');
            initTrendChart();
            
            // 绑定按钮点击事件 - 年龄分布图
            document.querySelectorAll('.filter-btn[data-type]').forEach(btn => {
                btn.addEventListener('click', function() {
                    const type = this.getAttribute('data-type');
                    if (this.closest('.chart-filter').previousElementSibling.id === 'ageDistributionChart') {
                        switchAgeChart(type);
                    } else {
                        switchTrendChart(type);
                    }
                });
            });
        });
    </script>
</body>
</html>