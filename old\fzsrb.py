import requests
import json
import datetime
import pytz
import logging
import os
import platform
import pymysql
from typing import Dict, List, Any
import config  # 导入配置文件

# 配置日志
log_dir = "logs"
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

log_file = os.path.join(log_dir, f"daily_report_{datetime.datetime.now().strftime('%Y%m%d')}.log")
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file, encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 从配置文件获取当前环境的配置
WEBHOOK_URL = config.get_current_webhook_url()
MYSQL_CONFIG = {**config.get_current_db_config(), "cursorclass": pymysql.cursors.DictCursor}

def send_to_wechat(content, msgtype="text"):
    """
    发送消息到企业微信群聊机器人
    支持text、markdown和template_card类型
    """
    logger.info(f"开始发送消息到企业微信（{config.get_current_env_name()}）...")
    
    headers = {'Content-Type': 'application/json'}
    
    # 根据消息类型构建不同的数据结构
    if msgtype == "markdown":
        data = {
            "msgtype": "markdown",
            "markdown": {
                "content": content
            }
        }
    elif msgtype == "template_card":
        # 直接使用传入的卡片数据
        data = content
    else:
        data = {
            "msgtype": "text",
            "text": {
                "content": content
            }
        }
    
    try:
        response = requests.post(WEBHOOK_URL, headers=headers, data=json.dumps(data))
        logger.info(f"企业微信API响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('errcode') == 0:
                logger.info("企业微信消息发送成功")
                return True
            else:
                logger.error(f"企业微信消息发送失败: {result.get('errmsg')}")
                return False
        else:
            logger.error(f"HTTP请求失败: {response.status_code}")
            return False
    except Exception as e:
        logger.exception(f"发送消息到企业微信时出错: {e}")
        return False

def get_order_data_by_createtime(date):
    """
    获取指定日期的订单数据（使用下单时间createTime维度）
    """
    logger.info(f"获取 {date} 的订单数据（下单时间维度）...")
    
    start_time = f"{date} 00:00:00"
    end_time = f"{date} 23:59:59"
    
    query = """
    WITH RankedOrders AS (
    SELECT 
        f.orderNo,
            f.target,
        cc.description AS channel_description,
        pc.card_typ,
        pc.productName_alias,
        f.businessStatus,
        f.outStatus,
        f.send_goods_result_Alias,
        f.createTime,
        f.updateTime,
            f.logisticsStatus,
            ROW_NUMBER() OVER (PARTITION BY f.target, pc.productName_alias ORDER BY f.createTime) as rn
    FROM 
        forders f
    LEFT JOIN 
        channel_config cc ON f.channel_config = cc.id
    LEFT JOIN 
        phone_card pc ON f.Phone_Card = pc.skuCode
    WHERE
        f.createTime BETWEEN %s AND %s
            AND f.logisticsStatus IN ('发货中', '发货成功')
    )
    SELECT * FROM RankedOrders WHERE rn = 1
    """
    
    try:
        conn = pymysql.connect(**MYSQL_CONFIG)
        with conn.cursor() as cursor:
            cursor.execute(query, (start_time, end_time))
            data = cursor.fetchall()
        conn.close()
        
        formatted_data = []
        for row in data:
            formatted_row = {
                "orderNo": row["orderNo"],
                "target": row["target"],
                "channel_config": {"description": row["channel_description"]},
                "Phone_Card": {
                    "card_typ": row["card_typ"],
                    "productName_alias": row["productName_alias"]
                },
                "businessStatus": row["businessStatus"],
                "outStatus": row["outStatus"],
                "send_goods_result_Alias": row["send_goods_result_Alias"],
                "logisticsStatus": row["logisticsStatus"],
                "createTime": row["createTime"].isoformat() if isinstance(row["createTime"], datetime.datetime) else row["createTime"],
                "updateTime": row["updateTime"].isoformat() if isinstance(row["updateTime"], datetime.datetime) else row["updateTime"]
            }
            formatted_data.append(formatted_row)
        
        logger.info(f"成功获取到 {len(formatted_data)} 条去重后的订单数据（下单时间维度）")
        return formatted_data
    except Exception as e:
        logger.exception(f"获取订单数据（下单时间维度）时出错: {e}")
        return []

def get_order_data_by_updatetime(date):
    """
    获取指定日期的订单数据（使用激活时间updateTime维度）
    """
    logger.info(f"获取 {date} 的订单数据（激活时间维度）...")
    
    start_time = f"{date} 00:00:00"
    end_time = f"{date} 23:59:59"
    
    query = """
    WITH RankedOrders AS (
    SELECT 
        f.orderNo,
            f.target,
        cc.description AS channel_description,
        pc.card_typ,
        pc.productName_alias,
        f.businessStatus,
        f.outStatus,
        f.send_goods_result_Alias,
        f.createTime,
        f.updateTime,
            f.logisticsStatus,
            ROW_NUMBER() OVER (PARTITION BY f.target, pc.productName_alias ORDER BY f.updateTime) as rn
    FROM 
        forders f
    LEFT JOIN 
        channel_config cc ON f.channel_config = cc.id
    LEFT JOIN 
        phone_card pc ON f.Phone_Card = pc.skuCode
    WHERE
        f.updateTime BETWEEN %s AND %s
            AND f.logisticsStatus = '发货成功'
    )
    SELECT * FROM RankedOrders WHERE rn = 1
    """
    
    try:
        conn = pymysql.connect(**MYSQL_CONFIG)
        with conn.cursor() as cursor:
            cursor.execute(query, (start_time, end_time))
            data = cursor.fetchall()
        conn.close()
        
        formatted_data = []
        for row in data:
            formatted_row = {
                "orderNo": row["orderNo"],
                "target": row["target"],
                "channel_config": {"description": row["channel_description"]},
                "Phone_Card": {
                    "card_typ": row["card_typ"],
                    "productName_alias": row["productName_alias"]
                },
                "businessStatus": row["businessStatus"],
                "outStatus": row["outStatus"],
                "send_goods_result_Alias": row["send_goods_result_Alias"],
                "logisticsStatus": row["logisticsStatus"],
                "createTime": row["createTime"].isoformat() if isinstance(row["createTime"], datetime.datetime) else row["createTime"],
                "updateTime": row["updateTime"].isoformat() if isinstance(row["updateTime"], datetime.datetime) else row["updateTime"]
            }
            formatted_data.append(formatted_row)
        
        logger.info(f"成功获取到 {len(formatted_data)} 条去重后的订单数据（激活时间维度）")
        return formatted_data
    except Exception as e:
        logger.exception(f"获取订单数据（激活时间维度）时出错: {e}")
        return []

def format_date_no_padding(date_obj):
    """
    跨平台兼容的日期格式化函数，返回无前导零的月和日
    """
    month = date_obj.month
    day = date_obj.day
    return f"{month}月{day}日"

def process_order_data(data, is_activation_dimension=False):
    """
    处理订单数据，提取统计信息
    """
    # 统计骑手卡和其他订单
    rider_card_orders = []
    other_orders = []
    
    # 记录有效订单
    valid_orders = []
    
    logger.info("开始分类并过滤订单数据...")
    for order in data:
        # 记录订单详情和状态用于调试
        logistics_status = order.get("logisticsStatus", "Unknown")
        
        # 针对激活维度，只统计"发货成功"的订单
        if is_activation_dimension:
            if logistics_status == "发货成功":
                valid_orders.append(order)
                # 分类骑手卡和其他
                if order.get("Phone_Card") and order["Phone_Card"].get("card_typ") == "骑手卡":
                    rider_card_orders.append(order)
                else:
                    other_orders.append(order)
        else:
            # 下单时间维度，统计"发货中"和"发货成功"的订单
            if logistics_status in ["发货中", "发货成功"]:
                valid_orders.append(order)
                # 分类骑手卡和其他
                if order.get("Phone_Card") and order["Phone_Card"].get("card_typ") == "骑手卡":
                    rider_card_orders.append(order)
                else:
                    other_orders.append(order)
    
    logger.info(f"订单分类完成: 有效订单 {len(valid_orders)} 单，其中骑手卡 {len(rider_card_orders)} 单，大流量卡 {len(other_orders)} 单")
    
    # 使用字典存储不同类型的订单、名称和统计信息
    order_types = {
        "骑手卡": {"orders": rider_card_orders, "stats": {}},
        "大流量卡": {"orders": other_orders, "stats": {}}
    }
    
    # 统计各类订单按渠道分类
    logger.info("统计各类订单按渠道分类...")
    for type_name, type_data in order_types.items():
        orders = type_data["orders"]
        channel_stats = {}
        
        for order in orders:
            channel = order.get("channel_config", {}).get("description", "未知渠道")
            product = order.get("Phone_Card", {}).get("productName_alias", "未知产品")
            
            logger.debug(f"{type_name}订单: 渠道={channel}, 产品={product}")
            
            if channel not in channel_stats:
                channel_stats[channel] = {}
            
            if product not in channel_stats[channel]:
                channel_stats[channel][product] = 0
                
            channel_stats[channel][product] += 1
        
        order_types[type_name]["stats"] = channel_stats
    
    return {
        "total_valid_orders": len(valid_orders),
        "order_types": order_types
    }

def generate_daily_report(order_data, activation_data, report_date):
    """
    根据订单数据生成日报，包含下单时间和激活时间两个维度
    """
    logger.info("开始生成日报...")
    
    date_str = format_date_no_padding(report_date)
    
    # 处理数据
    order_stats = process_order_data(order_data, is_activation_dimension=False)
    activation_stats = process_order_data(activation_data, is_activation_dimension=True)
    
    # 生成报告
    report = f"{date_str}报：\n\n"
    
    # 下单时间维度部分
    report += "ߓ妌餸륍妗橗䧻郞ᜮ"
    total_orders = order_stats["total_valid_orders"]
    order_types = order_stats["order_types"]
    
    # 构建总体概况 - 使用括号内列表格式
    type_counts = []
    for type_name, type_data in order_types.items():
        count = len(type_data["orders"])
        if count > 0:
            type_counts.append(f"{type_name}{count}")
    
    report += f"当天总订单数：{total_orders}单（{'；'.join(type_counts)}）\n"
    
    # 中文数字映射
    chinese_nums = ["一", "二", "三", "四", "五", "六", "七", "八", "九", "十"]
    
    # 生成各类型订单详情
    for i, (type_name, type_data) in enumerate(order_types.items()):
        orders = type_data["orders"]
        count = len(orders)
        
        if count > 0:
            # 使用中文数字作为序号
            chinese_num = chinese_nums[i] if i < len(chinese_nums) else str(i+1)
            report += f"{chinese_num}、{type_name}{count}单\n"
            
            # 按订单量从多到少排序渠道
            channel_stats = type_data["stats"]
            sorted_channels = sorted(
                channel_stats.items(), 
                key=lambda x: sum(x[1].values()), 
                reverse=True
            )
            
            # 按渠道生成详细报告
            for j, (channel, products) in enumerate(sorted_channels, 1):
                channel_total = sum(products.values())
                
                # 产品详情使用括号和×符号
                product_details = []
                sorted_products = sorted(products.items(), key=lambda x: x[1], reverse=True)
                for product, count in sorted_products:
                    product_details.append(f"{product}×{count}")
                
                # 使用括号格式和缩进
                report += f"{j}、{channel} {channel_total}单（{('，'.join(product_details))}）  \n"
    
    # 激活时间维度部分
    report += "\nߚঌ馿঴릗橗䧻郞ᜮ"
    total_activations = activation_stats["total_valid_orders"]
    activation_types = activation_stats["order_types"]
    
    # 构建总体概况 - 使用括号内列表格式
    type_counts = []
    for type_name, type_data in activation_types.items():
        count = len(type_data["orders"])
        if count > 0:
            type_counts.append(f"{type_name}{count}")
    
    report += f"当天总激活数：{total_activations}单（{('；'.join(type_counts))}）\n"
    
    # 生成各类型订单详情
    for i, (type_name, type_data) in enumerate(activation_types.items()):
        orders = type_data["orders"]
        count = len(orders)
        
        if count > 0:
            # 使用中文数字作为序号
            chinese_num = chinese_nums[i] if i < len(chinese_nums) else str(i+1)
            report += f"{chinese_num}、{type_name}{count}单\n"
            
            # 按订单量从多到少排序渠道
            channel_stats = type_data["stats"]
            sorted_channels = sorted(
                channel_stats.items(), 
                key=lambda x: sum(x[1].values()), 
                reverse=True
            )
            
            # 按渠道生成详细报告
            for j, (channel, products) in enumerate(sorted_channels, 1):
                channel_total = sum(products.values())
                
                # 产品详情使用括号和×符号
                product_details = []
                sorted_products = sorted(products.items(), key=lambda x: x[1], reverse=True)
                for product, count in sorted_products:
                    product_details.append(f"{product}×{count}")
                
                # 使用括号格式和缩进
                report += f"{j}、{channel} {channel_total}单（{('，'.join(product_details))}）  \n"
  
    logger.info("日报生成完成")
    return report.rstrip()

def send_daily_report():
    """
    发送昨天的日报
    """
    logger.info(f"开始执行昨天的日报发送流程（{config.get_current_env_name()}）...")
    
    try:
        # 获取昨天的日期（东八区）
        china_tz = pytz.timezone('Asia/Shanghai')
        now = datetime.datetime.now(china_tz)
        yesterday = (now - datetime.timedelta(days=1)).replace(hour=0, minute=0, second=0, microsecond=0)
        yesterday_str = yesterday.strftime("%Y-%m-%d")
        logger.info(f"昨天日期: {yesterday_str}")
        
        # 获取订单数据（下单时间维度）
        logger.info("开始获取昨天的订单数据（下单时间维度）...")
        order_data = get_order_data_by_createtime(yesterday_str)
        
        # 获取订单数据（激活时间维度）
        logger.info("开始获取昨天的订单数据（激活时间维度）...")
        activation_data = get_order_data_by_updatetime(yesterday_str)
        
        # 检查是否获取到数据，如果没有数据则不发送报表
        if not order_data and not activation_data:
            logger.warning("未获取到订单数据，取消发送日报")
            return "未获取到订单数据，未发送日报"
        
        logger.info(f"成功获取到订单数据，继续生成日报")
        
        # 生成标准日报文本（仍然可以保留，以便记录）
        logger.info("开始生成昨天的日报...")
        report_text = generate_daily_report(order_data, activation_data, yesterday)
        
        # 处理订单数据，用于MD格式消息
        order_stats = process_order_data(order_data, is_activation_dimension=False)
        activation_stats = process_order_data(activation_data, is_activation_dimension=True)
        
        # 准备数据
        month = yesterday.month
        day = yesterday.day
        total_orders = order_stats["total_valid_orders"]
        total_activations = activation_stats["total_valid_orders"]
        rider_card_orders = order_stats.get("rider_card_orders", len(order_stats["order_types"].get("骑手卡", {}).get("orders", [])))
        other_orders = order_stats.get("other_orders", len(order_stats["order_types"].get("大流量卡", {}).get("orders", [])))
        rider_card_activations = activation_stats.get("rider_card_orders", len(activation_stats["order_types"].get("骑手卡", {}).get("orders", [])))
        other_activations = activation_stats.get("other_orders", len(activation_stats["order_types"].get("大流量卡", {}).get("orders", [])))
        
        # 从daily_report_simple中获取熟龄度数据
        try:
            # 生成报表数据
            reports_dir = "reports"
            if not os.path.exists(reports_dir):
                os.makedirs(reports_dir)
            report_date = yesterday.strftime("%Y%m%d")
            report_url = f"https://b.zhoumeiren.cn/reports/report_{report_date}.html"
            
            # 尝试获取熟龄度数据
            import daily_report_simple
            report_data = daily_report_simple.generate_report_data(yesterday_str)
            
            if report_data and "activation_age" in report_data:
                age_data = report_data["activation_age"]
                total_age = sum(age_data.values())
                same_day_percent = round((age_data["same_day"] / total_age * 100), 1) if total_age > 0 else 0
                one_day_percent = round((age_data["one_day"] / total_age * 100), 1) if total_age > 0 else 0
                two_days_percent = round((age_data["two_days"] / total_age * 100), 1) if total_age > 0 else 0
            else:
                same_day_percent = 0
                one_day_percent = 0
                two_days_percent = 0
        except Exception as e:
            logger.warning(f"获取熟龄度数据失败: {e}")
            same_day_percent = 0
            one_day_percent = 0
            two_days_percent = 0
            report_url = ""
        
        # 统计本月总激活数
        try:
            month_start = yesterday.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            month_start_str = month_start.strftime("%Y-%m-%d 00:00:00")
            month_end_str = yesterday.strftime("%Y-%m-%d 23:59:59")
            month_query = """
            SELECT COUNT(DISTINCT f.orderNo) as total
            FROM forders f
            LEFT JOIN phone_card pc ON f.Phone_Card = pc.skuCode
            WHERE f.updateTime BETWEEN %s AND %s
              AND f.logisticsStatus = '发货成功'
            """
            conn = pymysql.connect(**MYSQL_CONFIG)
            with conn.cursor() as cursor:
                cursor.execute(month_query, (month_start_str, month_end_str))
                month_result = cursor.fetchone()
            conn.close()
            month_total_activations = month_result["total"] if month_result and "total" in month_result else 0
        except Exception as e:
            logger.warning(f"获取本月总激活数失败: {e}")
            month_total_activations = 0
        
        # 统计本月总订单数
        try:
            month_order_query = """
            WITH RankedOrders AS (
                SELECT 
                    f.orderNo,
                    f.target,
                    pc.productName_alias,
                    ROW_NUMBER() OVER (PARTITION BY f.target, pc.productName_alias ORDER BY f.createTime) as rn
                FROM 
                    forders f
                LEFT JOIN 
                    phone_card pc ON f.Phone_Card = pc.skuCode
                WHERE
                    f.createTime BETWEEN %s AND %s
                    AND f.logisticsStatus IN ('发货中', '发货成功')
            )
            SELECT COUNT(*) as total FROM RankedOrders WHERE rn = 1
            """
            conn = pymysql.connect(**MYSQL_CONFIG)
            with conn.cursor() as cursor:
                cursor.execute(month_order_query, (month_start_str, month_end_str))
                month_order_result = cursor.fetchone()
            conn.close()
            month_total_orders = month_order_result["total"] if month_order_result and "total" in month_order_result else 0
        except Exception as e:
            logger.warning(f"获取本月总订单数失败: {e}")
            month_total_orders = 0
        
        # 构建模板卡片数据
        template_card_data = {
            "msgtype": "template_card",
            "template_card": {
                "card_type": "text_notice",
                "source": {
                    "icon_url": "",  # 可以添加一个图标URL
                    "desc": "运营日报",
                    "desc_color": 0
                },
                "main_title": {
                    "title": f"{month}月{day}日运营日报",
                    "desc": "数据统计周期: 00:00-24:00"
                },
                "emphasis_content": {
                    "title": f"{month_total_activations}",
                    "desc": "本月总激活"
                },
                "horizontal_content_list": [
                    {
                        "keyname": "当月订单",
                        "value": f"{month_total_orders}单"
                    },
                    {
                        "keyname": "当日订单",
                        "value": f"{total_orders}单 (骑手卡{rider_card_orders}单,流量卡{other_orders}单)"
                    },
                    {
                        "keyname": "当日激活",
                        "value": f"{total_activations}单 (骑手卡{rider_card_activations}单,流量卡{other_activations}单)"
                    },
                    {
                        "keyname": "激活熟龄",
                        "value": f"当天{same_day_percent}%,昨天{one_day_percent}%,前天{two_days_percent}%"
                    }
                ],
                "jump_list": [
                    {
                        "type": 1,
                        "url": report_url,
                        "title": "详细数据分析"
                    }
                ],
                "card_action": {
                    "type": 1,
                    "url": report_url
                }
            }
        }
        
        # 发送到企业微信
        logger.info("开始发送模板卡片消息到企业微信...")
        send_result = send_to_wechat(template_card_data, msgtype="template_card")
        
        if send_result:
            logger.info("昨天的日报发送成功")
        else:
            logger.error("模板卡片发送失败，尝试使用Markdown格式")
            
            # 构建MD格式消息作为备用
            markdown_content = f"""
<font color=\"info\">{month}月{day}日运营日报</font>
<font color=\"comment\">数据统计周期：</font>   00:00-24:00

<font color=\"comment\">总订单</font>         {total_orders}单 (骑手卡{rider_card_orders}单，
                    流量卡{other_orders}单)

<font color=\"comment\">总激活量</font>       {total_activations}单 (骑手卡{rider_card_activations}单，
                    流量卡{other_activations}单)

<font color=\"comment\">订单熟龄分布</font>   当天{same_day_percent}%，昨天{one_day_percent}%，
                    前天{two_days_percent}%

<font color=\"comment\">了解数据详情</font>   [点击查看 >]({report_url})
"""
            # 尝试使用Markdown格式发送
            send_result = send_to_wechat(markdown_content, msgtype="markdown")
            
            if send_result:
                logger.info("使用Markdown格式发送成功")
            else:
                logger.error("Markdown格式发送也失败，尝试使用文本格式")
                # 尝试使用文本格式发送
                send_result = send_to_wechat(report_text)
                if send_result:
                    logger.info("使用文本格式发送成功")
                else:
                    logger.error("所有格式发送均失败")
        
        return report_text
    except Exception as e:
        logger.exception(f"发送昨天的日报时发生错误: {e}")
        return f"发送昨天的日报时发生错误: {e}"

if __name__ == "__main__":
    logger.info("=" * 50)
    logger.info(f"开始生成并发送昨天的日报（{config.get_current_env_name()}）...")
    result = send_daily_report()
    if "未获取到订单数据" in result:
        logger.warning("由于没有数据，日报未发送")
    else:
        logger.info("昨天的日报处理完成。")
    logger.info("=" * 50)
