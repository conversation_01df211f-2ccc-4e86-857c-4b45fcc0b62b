[2025-05-12 08:00:02] 开始执行日报脚本
[2025-05-12 08:00:02] 工作目录: /volume3/SSD-soft/fzsrb
[2025-05-12 08:00:02] 检查Python依赖...
WARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager, possibly rendering your system unusable. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv. Use the --root-user-action option if you know what you are doing and want to suppress this warning.
[2025-05-12 08:00:02] 开始执行简单日报脚本...
2025-05-12 08:00:03,412 - INFO - ==================================================
2025-05-12 08:00:03,412 - INFO - 开始生成日报统计数据...
2025-05-12 08:00:03,433 - INFO - 开始生成 2025-05-11 的报表数据...
2025-05-12 08:00:03,433 - INFO - 获取 2025-05-11 的订单数据（下单时间维度）...
2025-05-12 08:00:03,464 - INFO - 成功获取到 236 条去重后的订单数据（下单时间维度）
2025-05-12 08:00:03,464 - INFO - 开始分类并过滤订单数据...
2025-05-12 08:00:03,464 - INFO - 订单分类完成: 有效订单 236 单，其中骑手卡 190 单，流量卡 46 单
2025-05-12 08:00:03,464 - INFO - 计算各渠道的D0下单和D1激活数据...
2025-05-12 08:00:03,490 - INFO - 成功获取到 171 条当日激活订单
2025-05-12 08:00:03,493 - INFO - 样本订单日期检查 (共5个):
2025-05-12 08:00:03,493 - INFO - 订单 20250510073537366928: 下单时间=2025-05-10, 激活时间=2025-05-11, 相差=1天
2025-05-12 08:00:03,493 - INFO - 订单 20250508080054116214: 下单时间=2025-05-08, 激活时间=2025-05-11, 相差=3天
2025-05-12 08:00:03,493 - INFO - 订单 20250509223204766191: 下单时间=2025-05-09, 激活时间=2025-05-11, 相差=2天
2025-05-12 08:00:03,493 - INFO - 订单 20250510184770138726: 下单时间=2025-05-10, 激活时间=2025-05-11, 相差=1天
2025-05-12 08:00:03,494 - INFO - 订单 20250510070826993615: 下单时间=2025-05-10, 激活时间=2025-05-11, 相差=1天
2025-05-12 08:00:03,494 - INFO - 熟龄度处理统计: 总订单=171, 成功处理=171, 处理失败=0
2025-05-12 08:00:03,494 - INFO - 熟龄度分布: 当天=8, 昨天=77, 前天=41, 更早=45
2025-05-12 08:00:03,494 - INFO - 骑手卡熟龄度分布: 当天=6, 昨天=74, 前天=37, 更早=41
2025-05-12 08:00:03,494 - INFO - 流量卡熟龄度分布: 当天=2, 昨天=3, 前天=4, 更早=4
2025-05-12 08:00:03,494 - INFO - 熟龄度数据: {'same_day': 8, 'one_day': 77, 'two_days': 41, 'more_days': 45}
2025-05-12 08:00:03,494 - INFO - 骑手卡熟龄度数据: {'same_day': 6, 'one_day': 74, 'two_days': 37, 'more_days': 41}, 总数: 158
2025-05-12 08:00:03,494 - INFO - 流量卡熟龄度数据: {'same_day': 2, 'one_day': 3, 'two_days': 4, 'more_days': 4}, 总数: 13
2025-05-12 08:00:03,494 - INFO - 当日下单当日激活比例: 4.7%（8/171）
2025-05-12 08:00:03,495 - INFO - 样本订单日期检查 (共5个): 171，熟龄度分布: 当天=8, 昨天=77, 前天=41, 更早=45
2025-05-12 08:00:03,495 - INFO - 生成HTML报表模板，加载数据: 总活跃=171, 骑手卡=158, 流量卡=13
2025-05-12 08:00:03,495 - INFO - 骑手卡数据: [6, 74, 37, 41]
2025-05-12 08:00:03,495 - INFO - 流量卡数据: [2, 3, 4, 4]
2025-05-12 08:00:03,495 - INFO - HTML报表已生成: reports/report_20250511.html
2025-05-12 08:00:03,495 - INFO - 准备上传文件到FTP服务器: report_20250511.html
2025-05-12 08:00:03,495 - INFO - FTP配置: 主机=211.149.249.234, 端口=21, 目录=reports
2025-05-12 08:00:03,496 - INFO - 本地文件: reports/report_20250511.html, 大小: 33231 字节
2025-05-12 08:00:03,496 - INFO - 正在连接到FTP服务器: 211.149.249.234:21
2025-05-12 08:00:03,572 - INFO - 正在登录FTP服务器: 用户名=reports
2025-05-12 08:00:03,670 - INFO - FTP登录成功
2025-05-12 08:00:03,706 - INFO - 当前FTP目录: /
2025-05-12 08:00:03,706 - INFO - 尝试切换到目录: reports
2025-05-12 08:00:03,778 - INFO - 成功切换到目录: /reports
2025-05-12 08:00:03,920 - INFO - 上传前目录内容: ['report_20250506.html', 'report_20250503.html', 'report_20250508.html', 'report_20250502.html', '..', 'report_20250509.html', 'report_20250510.html', 'report_20250501.html', 'report_20250505.html', 'report_20250504.html', 'report_20250507.html', '.']
2025-05-12 08:00:03,920 - INFO - 开始上传文件: reports/report_20250511.html -> report_20250511.html
2025-05-12 08:00:04,133 - INFO - FTP上传结果: 226-File successfully transferred
226 0.070 seconds (measured here), 465.31 Kbytes per second
2025-05-12 08:00:04,279 - INFO - 上传后目录内容: ['report_20250506.html', 'report_20250511.html', 'report_20250503.html', 'report_20250508.html', 'report_20250502.html', '..', 'report_20250509.html', 'report_20250510.html', 'report_20250501.html', 'report_20250505.html', 'report_20250504.html', 'report_20250507.html', '.']
2025-05-12 08:00:04,279 - INFO - 文件已成功上传并验证: report_20250511.html
2025-05-12 08:00:04,279 - INFO - 文件访问URL: https://rep.331126.com/reports/report_20250511.html
2025-05-12 08:00:04,315 - INFO - FTP连接已关闭
2025-05-12 08:00:04,315 - INFO - 报表已上传到服务器，URL: https://rep.331126.com/reports/report_20250511.html
2025-05-12 08:00:04,315 - INFO - 日报生成完成
2025-05-12 08:00:04,315 - INFO - ==================================================
*get* '220---------- Welcome to Pure-FTPd [privsep] [TLS] ----------\n'
*get* '220-You are user number 1 of 50 allowed.\n'
*get* '220-Local time is now 08:00. Server port: 21.\n'
*get* '220-This is a private system - No anonymous login\n'
*get* '220-IPv6 connections are also welcome on this server.\n'
*get* '220 You will be disconnected after 15 minutes of inactivity.\n'
*resp* '220---------- Welcome to Pure-FTPd [privsep] [TLS] ----------\n220-You are user number 1 of 50 allowed.\n220-Local time is now 08:00. Server port: 21.\n220-This is a private system - No anonymous login\n220-IPv6 connections are also welcome on this server.\n220 You will be disconnected after 15 minutes of inactivity.'
*cmd* 'USER reports'
*put* 'USER reports\r\n'
*get* '331 User reports OK. Password required\n'
*resp* '331 User reports OK. Password required'
*cmd* 'PASS ****************'
*put* 'PASS ****************\r\n'
*get* '230 OK. Current directory is /\n'
*resp* '230 OK. Current directory is /'
*cmd* 'PWD'
*put* 'PWD\r\n'
*get* '257 "/" is your current location\n'
*resp* '257 "/" is your current location'
*cmd* 'CWD reports'
*put* 'CWD reports\r\n'
*get* '250 OK. Current directory is /reports\n'
*resp* '250 OK. Current directory is /reports'
*cmd* 'PWD'
*put* 'PWD\r\n'
*get* '257 "/reports" is your current location\n'
*resp* '257 "/reports" is your current location'
*cmd* 'TYPE A'
*put* 'TYPE A\r\n'
*get* '200 TYPE is now ASCII\n'
*resp* '200 TYPE is now ASCII'
*cmd* 'PASV'
*put* 'PASV\r\n'
*get* '227 Entering Passive Mode (211,149,249,234,153,157)\n'
*resp* '227 Entering Passive Mode (211,149,249,234,153,157)'
*cmd* 'NLST'
*put* 'NLST\r\n'
*get* '150 Accepted data connection\n'
*resp* '150 Accepted data connection'
*get* '226 12 matches total\n'
*resp* '226 12 matches total'
*cmd* 'TYPE I'
*put* 'TYPE I\r\n'
*get* '200 TYPE is now 8-bit binary\n'
*resp* '200 TYPE is now 8-bit binary'
*cmd* 'PASV'
*put* 'PASV\r\n'
*get* '227 Entering Passive Mode (211,149,249,234,155,178)\n'
*resp* '227 Entering Passive Mode (211,149,249,234,155,178)'
*cmd* 'STOR report_20250511.html'
*put* 'STOR report_20250511.html\r\n'
*get* '150 Accepted data connection\n'
*resp* '150 Accepted data connection'
*get* '226-File successfully transferred\n'
*get* '226 0.070 seconds (measured here), 465.31 Kbytes per second\n'
*resp* '226-File successfully transferred\n226 0.070 seconds (measured here), 465.31 Kbytes per second'
*cmd* 'TYPE A'
*put* 'TYPE A\r\n'
*get* '200 TYPE is now ASCII\n'
*resp* '200 TYPE is now ASCII'
*cmd* 'PASV'
*put* 'PASV\r\n'
*get* '227 Entering Passive Mode (211,149,249,234,155,63)\n'
*resp* '227 Entering Passive Mode (211,149,249,234,155,63)'
*cmd* 'NLST'
*put* 'NLST\r\n'
*get* '150 Accepted data connection\n'
*resp* '150 Accepted data connection'
*get* '226 13 matches total\n'
*resp* '226 13 matches total'
*cmd* 'QUIT'
*put* 'QUIT\r\n'
*get* '221-Goodbye. You uploaded 33 and downloaded 0 kbytes.\n'
*get* '221 Logout.\n'
*resp* '221-Goodbye. You uploaded 33 and downloaded 0 kbytes.\n221 Logout.'
[2025-05-12 08:00:02] 简单日报脚本执行成功
[2025-05-12 08:00:02] 开始执行全渠道日报脚本...
2025-05-12 08:00:04,473 - INFO - ==================================================
2025-05-12 08:00:04,473 - INFO - 开始生成并发送昨天的日报（全渠道生产环境）...
2025-05-12 08:00:04,473 - INFO - 开始执行昨天的日报发送流程（全渠道生产环境）...
2025-05-12 08:00:04,491 - INFO - 昨天日期: 2025-05-11
2025-05-12 08:00:04,491 - INFO - 开始获取昨天的订单数据（下单时间维度）...
2025-05-12 08:00:04,491 - INFO - 获取 2025-05-11 的订单数据（下单时间维度）...
2025-05-12 08:00:04,521 - INFO - 成功获取到 236 条去重后的订单数据（下单时间维度）
2025-05-12 08:00:04,521 - INFO - 开始获取昨天的订单数据（激活时间维度）...
2025-05-12 08:00:04,521 - INFO - 获取 2025-05-11 的订单数据（激活时间维度）...
2025-05-12 08:00:04,547 - INFO - 成功获取到 171 条去重后的订单数据（激活时间维度）
2025-05-12 08:00:04,547 - INFO - 成功获取到订单数据，继续生成日报
2025-05-12 08:00:04,548 - INFO - 开始生成昨天的日报...
2025-05-12 08:00:04,548 - INFO - 开始生成日报...
2025-05-12 08:00:04,548 - INFO - 开始分类并过滤订单数据...
2025-05-12 08:00:04,548 - INFO - 订单分类完成: 有效订单 236 单，其中骑手卡 190 单，大流量卡 46 单
2025-05-12 08:00:04,548 - INFO - 统计各类订单按渠道分类...
2025-05-12 08:00:04,548 - INFO - 开始分类并过滤订单数据...
2025-05-12 08:00:04,548 - INFO - 订单分类完成: 有效订单 171 单，其中骑手卡 158 单，大流量卡 13 单
2025-05-12 08:00:04,548 - INFO - 统计各类订单按渠道分类...
2025-05-12 08:00:04,549 - INFO - 日报生成完成
2025-05-12 08:00:04,549 - INFO - 开始分类并过滤订单数据...
2025-05-12 08:00:04,549 - INFO - 订单分类完成: 有效订单 236 单，其中骑手卡 190 单，大流量卡 46 单
2025-05-12 08:00:04,549 - INFO - 统计各类订单按渠道分类...
2025-05-12 08:00:04,549 - INFO - 开始分类并过滤订单数据...
2025-05-12 08:00:04,549 - INFO - 订单分类完成: 有效订单 171 单，其中骑手卡 158 单，大流量卡 13 单
2025-05-12 08:00:04,549 - INFO - 统计各类订单按渠道分类...
2025-05-12 08:00:04,551 - INFO - 开始生成 2025-05-11 的报表数据...
2025-05-12 08:00:04,551 - INFO - 获取 2025-05-11 的订单数据（下单时间维度）...
2025-05-12 08:00:04,580 - INFO - 成功获取到 236 条去重后的订单数据（下单时间维度）
2025-05-12 08:00:04,580 - INFO - 开始分类并过滤订单数据...
2025-05-12 08:00:04,580 - INFO - 订单分类完成: 有效订单 236 单，其中骑手卡 190 单，流量卡 46 单
2025-05-12 08:00:04,580 - INFO - 计算各渠道的D0下单和D1激活数据...
2025-05-12 08:00:04,604 - INFO - 成功获取到 171 条当日激活订单
2025-05-12 08:00:04,606 - INFO - 样本订单日期检查 (共5个):
2025-05-12 08:00:04,606 - INFO - 订单 20250510073537366928: 下单时间=2025-05-10, 激活时间=2025-05-11, 相差=1天
2025-05-12 08:00:04,606 - INFO - 订单 20250508080054116214: 下单时间=2025-05-08, 激活时间=2025-05-11, 相差=3天
2025-05-12 08:00:04,606 - INFO - 订单 20250509223204766191: 下单时间=2025-05-09, 激活时间=2025-05-11, 相差=2天
2025-05-12 08:00:04,606 - INFO - 订单 20250510184770138726: 下单时间=2025-05-10, 激活时间=2025-05-11, 相差=1天
2025-05-12 08:00:04,607 - INFO - 订单 20250510070826993615: 下单时间=2025-05-10, 激活时间=2025-05-11, 相差=1天
2025-05-12 08:00:04,607 - INFO - 熟龄度处理统计: 总订单=171, 成功处理=171, 处理失败=0
2025-05-12 08:00:04,607 - INFO - 熟龄度分布: 当天=8, 昨天=77, 前天=41, 更早=45
2025-05-12 08:00:04,607 - INFO - 骑手卡熟龄度分布: 当天=6, 昨天=74, 前天=37, 更早=41
2025-05-12 08:00:04,607 - INFO - 流量卡熟龄度分布: 当天=2, 昨天=3, 前天=4, 更早=4
2025-05-12 08:00:04,607 - INFO - 熟龄度数据: {'same_day': 8, 'one_day': 77, 'two_days': 41, 'more_days': 45}
2025-05-12 08:00:04,607 - INFO - 骑手卡熟龄度数据: {'same_day': 6, 'one_day': 74, 'two_days': 37, 'more_days': 41}, 总数: 158
2025-05-12 08:00:04,607 - INFO - 流量卡熟龄度数据: {'same_day': 2, 'one_day': 3, 'two_days': 4, 'more_days': 4}, 总数: 13
2025-05-12 08:00:04,608 - INFO - 当日下单当日激活比例: 4.7%（8/171）
2025-05-12 08:00:04,608 - INFO - 样本订单日期检查 (共5个): 171，熟龄度分布: 当天=8, 昨天=77, 前天=41, 更早=45
2025-05-12 08:00:04,608 - INFO - 开始发送模板卡片消息到企业微信...
2025-05-12 08:00:04,608 - INFO - 开始发送消息到企业微信（全渠道生产环境）...
2025-05-12 08:00:04,868 - INFO - 企业微信API响应状态码: 200
2025-05-12 08:00:04,868 - INFO - 企业微信消息发送成功
2025-05-12 08:00:04,868 - INFO - 昨天的日报发送成功
2025-05-12 08:00:04,869 - INFO - 昨天的日报处理完成。
2025-05-12 08:00:04,869 - INFO - ==================================================
[2025-05-12 08:00:02] 全渠道日报脚本执行成功
[2025-05-12 08:00:02] 开始执行OPPO渠道日报脚本...
2025-05-12 08:00:05,038 - INFO - ==================================================
2025-05-12 08:00:05,038 - INFO - 开始生成并发送OPPO和荣耀渠道日报（OPPO生产环境）...
2025-05-12 08:00:05,038 - INFO - 开始执行OPPO和荣耀渠道日报发送流程（OPPO生产环境）...
2025-05-12 08:00:05,056 - INFO - 查询日期: 2025-05-11
2025-05-12 08:00:05,056 - INFO - 获取 2025-05-11 的OPPO和荣耀渠道订单数据（下单时间维度）...
2025-05-12 08:00:05,076 - INFO - 查询到 33 条去重后的订单数据
2025-05-12 08:00:05,077 - INFO - 开始生成OPPO和荣耀渠道Markdown格式日报...
2025-05-12 08:00:05,077 - INFO - 开始处理订单数据，总数据条数: 33
2025-05-12 08:00:05,077 - INFO - 统计结果: 总订单 33
2025-05-12 08:00:05,077 - INFO -   OPPO: 29单
2025-05-12 08:00:05,077 - INFO -   荣耀: 4单
2025-05-12 08:00:05,077 - INFO - Markdown格式日报生成完成
2025-05-12 08:00:05,077 - INFO - 开始发送消息到企业微信（OPPO生产环境）...
2025-05-12 08:00:05,338 - INFO - 企业微信API响应状态码: 200
2025-05-12 08:00:05,339 - INFO - 企业微信消息发送成功
2025-05-12 08:00:05,339 - INFO - OPPO和荣耀渠道日报发送成功
2025-05-12 08:00:05,339 - INFO - OPPO和荣耀渠道日报处理完成。
2025-05-12 08:00:05,339 - INFO - ==================================================
[2025-05-12 08:00:02] OPPO渠道日报脚本执行成功
[2025-05-12 08:00:05] 所有日报脚本执行完成
----------------------------------------
