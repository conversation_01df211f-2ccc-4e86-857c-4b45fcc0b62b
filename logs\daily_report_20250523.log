2025-05-23 00:30:40,743 - INFO - ==================================================
2025-05-23 00:30:40,743 - INFO - 开始生成日报统计数据...
2025-05-23 00:30:40,760 - INFO - 开始生成 2025-05-22 的报表数据...
2025-05-23 00:30:40,761 - INFO - 获取 2025-05-22 的订单数据（下单时间维度）...
2025-05-23 00:30:40,793 - INFO - 成功获取到 229 条去重后的订单数据（下单时间维度）
2025-05-23 00:30:40,793 - INFO - 开始分类并过滤订单数据...
2025-05-23 00:30:40,793 - INFO - 订单分类完成: 有效订单 229 单，其中骑手卡 228 单，流量卡 1 单
2025-05-23 00:30:40,793 - INFO - 计算各渠道的D0下单和D1激活数据...
2025-05-23 00:30:40,820 - INFO - 成功获取到 169 条当日激活订单
2025-05-23 00:30:40,823 - INFO - 样本订单日期检查 (共5个):
2025-05-23 00:30:40,823 - INFO - 订单 20250520184357132251: 下单时间=2025-05-20, 激活时间=2025-05-22, 相差=2天
2025-05-23 00:30:40,823 - INFO - 订单 20250520204716699536: 下单时间=2025-05-20, 激活时间=2025-05-22, 相差=2天
2025-05-23 00:30:40,824 - INFO - 订单 20250521102131667756: 下单时间=2025-05-21, 激活时间=2025-05-22, 相差=1天
2025-05-23 00:30:40,824 - INFO - 订单 20250520144858199369: 下单时间=2025-05-20, 激活时间=2025-05-22, 相差=2天
2025-05-23 00:30:40,824 - INFO - 订单 20250521135219409412: 下单时间=2025-05-21, 激活时间=2025-05-22, 相差=1天
2025-05-23 00:30:40,824 - INFO - 熟龄度处理统计: 总订单=169, 成功处理=169, 处理失败=0
2025-05-23 00:30:40,824 - INFO - 熟龄度分布: 当天=8, 昨天=100, 前天=42, 更早=19
2025-05-23 00:30:40,824 - INFO - 骑手卡熟龄度分布: 当天=7, 昨天=100, 前天=41, 更早=16
2025-05-23 00:30:40,824 - INFO - 流量卡熟龄度分布: 当天=1, 昨天=0, 前天=1, 更早=3
2025-05-23 00:30:40,824 - INFO - 获取报表日期与前一日订单数据对比...
2025-05-23 00:30:40,825 - INFO - 对比日期: 报表日期=2025-05-22, 前一日=2025-05-21
2025-05-23 00:30:40,825 - INFO - 获取 2025-05-22 的订单数据（下单时间维度）...
2025-05-23 00:30:40,857 - INFO - 成功获取到 229 条去重后的订单数据（下单时间维度）
2025-05-23 00:30:40,857 - INFO - 获取 2025-05-21 的订单数据（下单时间维度）...
2025-05-23 00:30:40,889 - INFO - 成功获取到 242 条去重后的订单数据（下单时间维度）
2025-05-23 00:30:40,890 - INFO - 获取周订单趋势数据...
2025-05-23 00:30:40,890 - INFO - 查询日期范围: 2025-05-16 00:00:00 至 2025-05-22 23:59:59
2025-05-23 00:30:40,914 - INFO - 成功获取到 7 天的订单趋势数据
2025-05-23 00:30:40,914 - INFO - 熟龄度数据: {'same_day': 8, 'one_day': 100, 'two_days': 42, 'more_days': 19}
2025-05-23 00:30:40,914 - INFO - 骑手卡熟龄度数据: {'same_day': 7, 'one_day': 100, 'two_days': 41, 'more_days': 16}, 总数: 164
2025-05-23 00:30:40,914 - INFO - 流量卡熟龄度数据: {'same_day': 1, 'one_day': 0, 'two_days': 1, 'more_days': 3}, 总数: 5
2025-05-23 00:30:40,914 - INFO - 当日下单当日激活比例: 4.7%（8/169）
2025-05-23 00:30:40,914 - INFO - 样本订单日期检查 (共5个): 169，熟龄度分布: 当天=8, 昨天=100, 前天=42, 更早=19
2025-05-23 00:30:40,914 - INFO - 环比分析: 当前=229单, 前一日=242单, 变化=-5.4%
2025-05-23 00:30:40,914 - INFO - 周趋势数据获取成功，共7天数据
2025-05-23 00:30:40,915 - INFO - 生成HTML报表模板，加载数据: 总活跃=169, 骑手卡=164, 流量卡=5
2025-05-23 00:30:40,915 - INFO - 骑手卡数据: [7, 100, 41, 16]
2025-05-23 00:30:40,915 - INFO - 流量卡数据: [1, 0, 1, 3]
2025-05-23 00:30:40,915 - INFO - HTML报表已生成: reports/report_20250522.html
2025-05-23 00:30:40,916 - INFO - 准备上传文件到FTP服务器: report_20250522.html
2025-05-23 00:30:40,916 - INFO - FTP配置: 主机=211.149.249.234, 端口=21, 目录=reports
2025-05-23 00:30:40,916 - INFO - 本地文件: reports/report_20250522.html, 大小: 53294 字节
2025-05-23 00:30:40,916 - INFO - 正在连接到FTP服务器: 211.149.249.234:21
2025-05-23 00:30:40,994 - INFO - 正在登录FTP服务器: 用户名=reports
2025-05-23 00:30:41,097 - INFO - FTP登录成功
2025-05-23 00:30:41,135 - INFO - 当前FTP目录: /
2025-05-23 00:30:41,135 - INFO - 尝试切换到目录: reports
2025-05-23 00:30:41,212 - INFO - 成功切换到目录: /reports
2025-05-23 00:30:41,364 - INFO - 上传前目录内容: ['report_20250506.html', 'report_20250511.html', 'report_20250515.html', 'report_20250518.html', 'report_20250516.html', 'report_20250512.html', 'report_20250503.html', 'report_20250508.html', 'report_20250502.html', 'report_20250519.html', '..', 'report_20250513.html', 'report_20250509.html', 'report_20250514.html', 'report_20250510.html', 'report_20250520.html', 'report_20250521.html', 'report_20250501.html', 'report_20250505.html', 'report_20250504.html', 'report_20250507.html', 'report_20250517.html', '.']
2025-05-23 00:30:41,364 - INFO - 开始上传文件: reports/report_20250522.html -> report_20250522.html
2025-05-23 00:30:41,609 - INFO - FTP上传结果: 226-File successfully transferred
226 0.100 seconds (measured here), 0.51 Mbytes per second
2025-05-23 00:30:41,757 - INFO - 上传后目录内容: ['report_20250506.html', 'report_20250522.html', 'report_20250511.html', 'report_20250515.html', 'report_20250518.html', 'report_20250516.html', 'report_20250512.html', 'report_20250503.html', 'report_20250508.html', 'report_20250502.html', 'report_20250519.html', '..', 'report_20250513.html', 'report_20250509.html', 'report_20250514.html', 'report_20250510.html', 'report_20250520.html', 'report_20250521.html', 'report_20250501.html', 'report_20250505.html', 'report_20250504.html', 'report_20250507.html', 'report_20250517.html', '.']
2025-05-23 00:30:41,757 - INFO - 文件已成功上传并验证: report_20250522.html
2025-05-23 00:30:41,757 - INFO - 文件访问URL: https://rep.331126.com/reports/report_20250522.html
2025-05-23 00:30:41,795 - INFO - FTP连接已关闭
2025-05-23 00:30:41,795 - INFO - 报表已上传到服务器，URL: https://rep.331126.com/reports/report_20250522.html
2025-05-23 00:30:41,795 - INFO - 日报生成完成
2025-05-23 00:30:41,795 - INFO - ==================================================
2025-05-23 00:30:41,954 - INFO - ==================================================
2025-05-23 00:30:41,954 - INFO - 开始生成并发送昨天的日报（测试环境）...
2025-05-23 00:30:41,954 - INFO - 开始执行昨天的日报发送流程（测试环境）...
2025-05-23 00:30:41,971 - INFO - 昨天日期: 2025-05-22
2025-05-23 00:30:41,971 - INFO - 开始获取昨天的订单数据（下单时间维度）...
2025-05-23 00:30:41,971 - INFO - 获取 2025-05-22 的订单数据（下单时间维度）...
2025-05-23 00:30:42,003 - INFO - 成功获取到 229 条去重后的订单数据（下单时间维度）
2025-05-23 00:30:42,004 - INFO - 开始获取昨天的订单数据（激活时间维度）...
2025-05-23 00:30:42,004 - INFO - 获取 2025-05-22 的订单数据（激活时间维度）...
2025-05-23 00:30:42,032 - INFO - 成功获取到 169 条去重后的订单数据（激活时间维度）
2025-05-23 00:30:42,033 - INFO - 成功获取到订单数据，继续生成日报
2025-05-23 00:30:42,033 - INFO - 开始生成昨天的日报...
2025-05-23 00:30:42,033 - INFO - 开始生成日报...
2025-05-23 00:30:42,033 - INFO - 开始分类并过滤订单数据...
2025-05-23 00:30:42,033 - INFO - 订单分类完成: 有效订单 229 单，其中骑手卡 228 单，大流量卡 1 单
2025-05-23 00:30:42,033 - INFO - 统计各类订单按渠道分类...
2025-05-23 00:30:42,033 - INFO - 开始分类并过滤订单数据...
2025-05-23 00:30:42,033 - INFO - 订单分类完成: 有效订单 169 单，其中骑手卡 164 单，大流量卡 5 单
2025-05-23 00:30:42,033 - INFO - 统计各类订单按渠道分类...
2025-05-23 00:30:42,034 - INFO - 日报生成完成
2025-05-23 00:30:42,034 - INFO - 开始分类并过滤订单数据...
2025-05-23 00:30:42,034 - INFO - 订单分类完成: 有效订单 229 单，其中骑手卡 228 单，大流量卡 1 单
2025-05-23 00:30:42,034 - INFO - 统计各类订单按渠道分类...
2025-05-23 00:30:42,034 - INFO - 开始分类并过滤订单数据...
2025-05-23 00:30:42,034 - INFO - 订单分类完成: 有效订单 169 单，其中骑手卡 164 单，大流量卡 5 单
2025-05-23 00:30:42,035 - INFO - 统计各类订单按渠道分类...
2025-05-23 00:30:42,036 - INFO - 开始生成 2025-05-22 的报表数据...
2025-05-23 00:30:42,036 - INFO - 获取 2025-05-22 的订单数据（下单时间维度）...
2025-05-23 00:30:42,069 - INFO - 成功获取到 229 条去重后的订单数据（下单时间维度）
2025-05-23 00:30:42,069 - INFO - 开始分类并过滤订单数据...
2025-05-23 00:30:42,069 - INFO - 订单分类完成: 有效订单 229 单，其中骑手卡 228 单，流量卡 1 单
2025-05-23 00:30:42,069 - INFO - 计算各渠道的D0下单和D1激活数据...
2025-05-23 00:30:42,098 - INFO - 成功获取到 169 条当日激活订单
2025-05-23 00:30:42,099 - INFO - 样本订单日期检查 (共5个):
2025-05-23 00:30:42,100 - INFO - 订单 20250520184357132251: 下单时间=2025-05-20, 激活时间=2025-05-22, 相差=2天
2025-05-23 00:30:42,100 - INFO - 订单 20250520204716699536: 下单时间=2025-05-20, 激活时间=2025-05-22, 相差=2天
2025-05-23 00:30:42,100 - INFO - 订单 20250521102131667756: 下单时间=2025-05-21, 激活时间=2025-05-22, 相差=1天
2025-05-23 00:30:42,100 - INFO - 订单 20250520144858199369: 下单时间=2025-05-20, 激活时间=2025-05-22, 相差=2天
2025-05-23 00:30:42,100 - INFO - 订单 20250521135219409412: 下单时间=2025-05-21, 激活时间=2025-05-22, 相差=1天
2025-05-23 00:30:42,100 - INFO - 熟龄度处理统计: 总订单=169, 成功处理=169, 处理失败=0
2025-05-23 00:30:42,100 - INFO - 熟龄度分布: 当天=8, 昨天=100, 前天=42, 更早=19
2025-05-23 00:30:42,100 - INFO - 骑手卡熟龄度分布: 当天=7, 昨天=100, 前天=41, 更早=16
2025-05-23 00:30:42,100 - INFO - 流量卡熟龄度分布: 当天=1, 昨天=0, 前天=1, 更早=3
2025-05-23 00:30:42,101 - INFO - 获取报表日期与前一日订单数据对比...
2025-05-23 00:30:42,101 - INFO - 对比日期: 报表日期=2025-05-22, 前一日=2025-05-21
2025-05-23 00:30:42,101 - INFO - 获取 2025-05-22 的订单数据（下单时间维度）...
2025-05-23 00:30:42,133 - INFO - 成功获取到 229 条去重后的订单数据（下单时间维度）
2025-05-23 00:30:42,133 - INFO - 获取 2025-05-21 的订单数据（下单时间维度）...
2025-05-23 00:30:42,165 - INFO - 成功获取到 242 条去重后的订单数据（下单时间维度）
2025-05-23 00:30:42,166 - INFO - 获取周订单趋势数据...
2025-05-23 00:30:42,166 - INFO - 查询日期范围: 2025-05-16 00:00:00 至 2025-05-22 23:59:59
2025-05-23 00:30:42,189 - INFO - 成功获取到 7 天的订单趋势数据
2025-05-23 00:30:42,190 - INFO - 熟龄度数据: {'same_day': 8, 'one_day': 100, 'two_days': 42, 'more_days': 19}
2025-05-23 00:30:42,190 - INFO - 骑手卡熟龄度数据: {'same_day': 7, 'one_day': 100, 'two_days': 41, 'more_days': 16}, 总数: 164
2025-05-23 00:30:42,190 - INFO - 流量卡熟龄度数据: {'same_day': 1, 'one_day': 0, 'two_days': 1, 'more_days': 3}, 总数: 5
2025-05-23 00:30:42,190 - INFO - 当日下单当日激活比例: 4.7%（8/169）
2025-05-23 00:30:42,190 - INFO - 样本订单日期检查 (共5个): 169，熟龄度分布: 当天=8, 昨天=100, 前天=42, 更早=19
2025-05-23 00:30:42,190 - INFO - 环比分析: 当前=229单, 前一日=242单, 变化=-5.4%
2025-05-23 00:30:42,190 - INFO - 周趋势数据获取成功，共7天数据
2025-05-23 00:30:42,259 - INFO - 开始发送模板卡片消息到企业微信...
2025-05-23 00:30:42,260 - INFO - 开始发送消息到企业微信（测试环境）...
2025-05-23 00:30:42,563 - INFO - 企业微信API响应状态码: 200
2025-05-23 00:30:42,563 - INFO - 企业微信消息发送成功
2025-05-23 00:30:42,563 - INFO - 昨天的日报发送成功
2025-05-23 00:30:42,564 - INFO - 昨天的日报处理完成。
2025-05-23 00:30:42,564 - INFO - ==================================================
2025-05-23 08:00:02,219 - INFO - ==================================================
2025-05-23 08:00:02,219 - INFO - 开始生成日报统计数据...
2025-05-23 08:00:02,237 - INFO - 开始生成 2025-05-22 的报表数据...
2025-05-23 08:00:02,237 - INFO - 获取 2025-05-22 的订单数据（下单时间维度）...
2025-05-23 08:00:02,269 - INFO - 成功获取到 229 条去重后的订单数据（下单时间维度）
2025-05-23 08:00:02,269 - INFO - 开始分类并过滤订单数据...
2025-05-23 08:00:02,270 - INFO - 订单分类完成: 有效订单 229 单，其中骑手卡 228 单，流量卡 1 单
2025-05-23 08:00:02,270 - INFO - 计算各渠道的D0下单和D1激活数据...
2025-05-23 08:00:02,297 - INFO - 成功获取到 169 条当日激活订单
2025-05-23 08:00:02,300 - INFO - 样本订单日期检查 (共5个):
2025-05-23 08:00:02,300 - INFO - 订单 20250520184357132251: 下单时间=2025-05-20, 激活时间=2025-05-22, 相差=2天
2025-05-23 08:00:02,300 - INFO - 订单 20250520204716699536: 下单时间=2025-05-20, 激活时间=2025-05-22, 相差=2天
2025-05-23 08:00:02,300 - INFO - 订单 20250521102131667756: 下单时间=2025-05-21, 激活时间=2025-05-22, 相差=1天
2025-05-23 08:00:02,300 - INFO - 订单 20250520144858199369: 下单时间=2025-05-20, 激活时间=2025-05-22, 相差=2天
2025-05-23 08:00:02,300 - INFO - 订单 20250521135219409412: 下单时间=2025-05-21, 激活时间=2025-05-22, 相差=1天
2025-05-23 08:00:02,300 - INFO - 熟龄度处理统计: 总订单=169, 成功处理=169, 处理失败=0
2025-05-23 08:00:02,300 - INFO - 熟龄度分布: 当天=8, 昨天=100, 前天=42, 更早=19
2025-05-23 08:00:02,300 - INFO - 骑手卡熟龄度分布: 当天=7, 昨天=100, 前天=41, 更早=16
2025-05-23 08:00:02,301 - INFO - 流量卡熟龄度分布: 当天=1, 昨天=0, 前天=1, 更早=3
2025-05-23 08:00:02,301 - INFO - 获取报表日期与前一日订单数据对比...
2025-05-23 08:00:02,301 - INFO - 对比日期: 报表日期=2025-05-22, 前一日=2025-05-21
2025-05-23 08:00:02,301 - INFO - 获取 2025-05-22 的订单数据（下单时间维度）...
2025-05-23 08:00:02,333 - INFO - 成功获取到 229 条去重后的订单数据（下单时间维度）
2025-05-23 08:00:02,333 - INFO - 获取 2025-05-21 的订单数据（下单时间维度）...
2025-05-23 08:00:02,364 - INFO - 成功获取到 242 条去重后的订单数据（下单时间维度）
2025-05-23 08:00:02,364 - INFO - 获取周订单趋势数据...
2025-05-23 08:00:02,365 - INFO - 查询日期范围: 2025-05-16 00:00:00 至 2025-05-22 23:59:59
2025-05-23 08:00:02,388 - INFO - 成功获取到 7 天的订单趋势数据
2025-05-23 08:00:02,388 - INFO - 熟龄度数据: {'same_day': 8, 'one_day': 100, 'two_days': 42, 'more_days': 19}
2025-05-23 08:00:02,388 - INFO - 骑手卡熟龄度数据: {'same_day': 7, 'one_day': 100, 'two_days': 41, 'more_days': 16}, 总数: 164
2025-05-23 08:00:02,388 - INFO - 流量卡熟龄度数据: {'same_day': 1, 'one_day': 0, 'two_days': 1, 'more_days': 3}, 总数: 5
2025-05-23 08:00:02,388 - INFO - 当日下单当日激活比例: 4.7%（8/169）
2025-05-23 08:00:02,388 - INFO - 样本订单日期检查 (共5个): 169，熟龄度分布: 当天=8, 昨天=100, 前天=42, 更早=19
2025-05-23 08:00:02,388 - INFO - 环比分析: 当前=229单, 前一日=242单, 变化=-5.4%
2025-05-23 08:00:02,388 - INFO - 周趋势数据获取成功，共7天数据
2025-05-23 08:00:02,389 - INFO - 生成HTML报表模板，加载数据: 总活跃=169, 骑手卡=164, 流量卡=5
2025-05-23 08:00:02,389 - INFO - 骑手卡数据: [7, 100, 41, 16]
2025-05-23 08:00:02,389 - INFO - 流量卡数据: [1, 0, 1, 3]
2025-05-23 08:00:02,389 - INFO - HTML报表已生成: reports/report_20250522.html
2025-05-23 08:00:02,390 - INFO - 准备上传文件到FTP服务器: report_20250522.html
2025-05-23 08:00:02,390 - INFO - FTP配置: 主机=211.149.249.234, 端口=21, 目录=reports
2025-05-23 08:00:02,390 - INFO - 本地文件: reports/report_20250522.html, 大小: 53294 字节
2025-05-23 08:00:02,390 - INFO - 正在连接到FTP服务器: 211.149.249.234:21
2025-05-23 08:00:02,463 - INFO - 正在登录FTP服务器: 用户名=reports
2025-05-23 08:00:02,563 - INFO - FTP登录成功
2025-05-23 08:00:02,598 - INFO - 当前FTP目录: /
2025-05-23 08:00:02,598 - INFO - 尝试切换到目录: reports
2025-05-23 08:00:02,669 - INFO - 成功切换到目录: /reports
2025-05-23 08:00:02,819 - INFO - 上传前目录内容: ['report_20250506.html', 'report_20250522.html', 'report_20250511.html', 'report_20250515.html', 'report_20250518.html', 'report_20250516.html', 'report_20250512.html', 'report_20250503.html', 'report_20250508.html', 'report_20250502.html', 'report_20250519.html', '..', 'report_20250513.html', 'report_20250509.html', 'report_20250514.html', 'report_20250510.html', 'report_20250520.html', 'report_20250521.html', 'report_20250501.html', 'report_20250505.html', 'report_20250504.html', 'report_20250507.html', 'report_20250517.html', '.']
2025-05-23 08:00:02,819 - INFO - 开始上传文件: reports/report_20250522.html -> report_20250522.html
2025-05-23 08:00:03,062 - INFO - FTP上传结果: 226-File successfully transferred
226 0.100 seconds (measured here), 0.51 Mbytes per second
2025-05-23 08:00:03,198 - INFO - 上传后目录内容: ['report_20250506.html', 'report_20250522.html', 'report_20250511.html', 'report_20250515.html', 'report_20250518.html', 'report_20250516.html', 'report_20250512.html', 'report_20250503.html', 'report_20250508.html', 'report_20250502.html', 'report_20250519.html', '..', 'report_20250513.html', 'report_20250509.html', 'report_20250514.html', 'report_20250510.html', 'report_20250520.html', 'report_20250521.html', 'report_20250501.html', 'report_20250505.html', 'report_20250504.html', 'report_20250507.html', 'report_20250517.html', '.']
2025-05-23 08:00:03,198 - INFO - 文件已成功上传并验证: report_20250522.html
2025-05-23 08:00:03,198 - INFO - 文件访问URL: https://rep.331126.com/reports/report_20250522.html
2025-05-23 08:00:03,233 - INFO - FTP连接已关闭
2025-05-23 08:00:03,233 - INFO - 报表已上传到服务器，URL: https://rep.331126.com/reports/report_20250522.html
2025-05-23 08:00:03,233 - INFO - 日报生成完成
2025-05-23 08:00:03,233 - INFO - ==================================================
2025-05-23 08:00:03,393 - INFO - ==================================================
2025-05-23 08:00:03,394 - INFO - 开始生成并发送昨天的日报（全渠道生产环境）...
2025-05-23 08:00:03,394 - INFO - 开始执行昨天的日报发送流程（全渠道生产环境）...
2025-05-23 08:00:03,411 - INFO - 昨天日期: 2025-05-22
2025-05-23 08:00:03,412 - INFO - 开始获取昨天的订单数据（下单时间维度）...
2025-05-23 08:00:03,412 - INFO - 获取 2025-05-22 的订单数据（下单时间维度）...
2025-05-23 08:00:03,444 - INFO - 成功获取到 229 条去重后的订单数据（下单时间维度）
2025-05-23 08:00:03,444 - INFO - 开始获取昨天的订单数据（激活时间维度）...
2025-05-23 08:00:03,444 - INFO - 获取 2025-05-22 的订单数据（激活时间维度）...
2025-05-23 08:00:03,473 - INFO - 成功获取到 169 条去重后的订单数据（激活时间维度）
2025-05-23 08:00:03,474 - INFO - 成功获取到订单数据，继续生成日报
2025-05-23 08:00:03,474 - INFO - 开始生成昨天的日报...
2025-05-23 08:00:03,474 - INFO - 开始生成日报...
2025-05-23 08:00:03,474 - INFO - 开始分类并过滤订单数据...
2025-05-23 08:00:03,474 - INFO - 订单分类完成: 有效订单 229 单，其中骑手卡 228 单，大流量卡 1 单
2025-05-23 08:00:03,474 - INFO - 统计各类订单按渠道分类...
2025-05-23 08:00:03,474 - INFO - 开始分类并过滤订单数据...
2025-05-23 08:00:03,474 - INFO - 订单分类完成: 有效订单 169 单，其中骑手卡 164 单，大流量卡 5 单
2025-05-23 08:00:03,474 - INFO - 统计各类订单按渠道分类...
2025-05-23 08:00:03,475 - INFO - 日报生成完成
2025-05-23 08:00:03,475 - INFO - 开始分类并过滤订单数据...
2025-05-23 08:00:03,475 - INFO - 订单分类完成: 有效订单 229 单，其中骑手卡 228 单，大流量卡 1 单
2025-05-23 08:00:03,475 - INFO - 统计各类订单按渠道分类...
2025-05-23 08:00:03,475 - INFO - 开始分类并过滤订单数据...
2025-05-23 08:00:03,475 - INFO - 订单分类完成: 有效订单 169 单，其中骑手卡 164 单，大流量卡 5 单
2025-05-23 08:00:03,475 - INFO - 统计各类订单按渠道分类...
2025-05-23 08:00:03,477 - INFO - 开始生成 2025-05-22 的报表数据...
2025-05-23 08:00:03,477 - INFO - 获取 2025-05-22 的订单数据（下单时间维度）...
2025-05-23 08:00:03,509 - INFO - 成功获取到 229 条去重后的订单数据（下单时间维度）
2025-05-23 08:00:03,509 - INFO - 开始分类并过滤订单数据...
2025-05-23 08:00:03,509 - INFO - 订单分类完成: 有效订单 229 单，其中骑手卡 228 单，流量卡 1 单
2025-05-23 08:00:03,509 - INFO - 计算各渠道的D0下单和D1激活数据...
2025-05-23 08:00:03,539 - INFO - 成功获取到 169 条当日激活订单
2025-05-23 08:00:03,541 - INFO - 样本订单日期检查 (共5个):
2025-05-23 08:00:03,541 - INFO - 订单 20250520184357132251: 下单时间=2025-05-20, 激活时间=2025-05-22, 相差=2天
2025-05-23 08:00:03,541 - INFO - 订单 20250520204716699536: 下单时间=2025-05-20, 激活时间=2025-05-22, 相差=2天
2025-05-23 08:00:03,541 - INFO - 订单 20250521102131667756: 下单时间=2025-05-21, 激活时间=2025-05-22, 相差=1天
2025-05-23 08:00:03,541 - INFO - 订单 20250520144858199369: 下单时间=2025-05-20, 激活时间=2025-05-22, 相差=2天
2025-05-23 08:00:03,541 - INFO - 订单 20250521135219409412: 下单时间=2025-05-21, 激活时间=2025-05-22, 相差=1天
2025-05-23 08:00:03,541 - INFO - 熟龄度处理统计: 总订单=169, 成功处理=169, 处理失败=0
2025-05-23 08:00:03,541 - INFO - 熟龄度分布: 当天=8, 昨天=100, 前天=42, 更早=19
2025-05-23 08:00:03,541 - INFO - 骑手卡熟龄度分布: 当天=7, 昨天=100, 前天=41, 更早=16
2025-05-23 08:00:03,541 - INFO - 流量卡熟龄度分布: 当天=1, 昨天=0, 前天=1, 更早=3
2025-05-23 08:00:03,542 - INFO - 获取报表日期与前一日订单数据对比...
2025-05-23 08:00:03,542 - INFO - 对比日期: 报表日期=2025-05-22, 前一日=2025-05-21
2025-05-23 08:00:03,542 - INFO - 获取 2025-05-22 的订单数据（下单时间维度）...
2025-05-23 08:00:03,574 - INFO - 成功获取到 229 条去重后的订单数据（下单时间维度）
2025-05-23 08:00:03,575 - INFO - 获取 2025-05-21 的订单数据（下单时间维度）...
2025-05-23 08:00:03,608 - INFO - 成功获取到 242 条去重后的订单数据（下单时间维度）
2025-05-23 08:00:03,609 - INFO - 获取周订单趋势数据...
2025-05-23 08:00:03,609 - INFO - 查询日期范围: 2025-05-16 00:00:00 至 2025-05-22 23:59:59
2025-05-23 08:00:03,634 - INFO - 成功获取到 7 天的订单趋势数据
2025-05-23 08:00:03,634 - INFO - 熟龄度数据: {'same_day': 8, 'one_day': 100, 'two_days': 42, 'more_days': 19}
2025-05-23 08:00:03,634 - INFO - 骑手卡熟龄度数据: {'same_day': 7, 'one_day': 100, 'two_days': 41, 'more_days': 16}, 总数: 164
2025-05-23 08:00:03,634 - INFO - 流量卡熟龄度数据: {'same_day': 1, 'one_day': 0, 'two_days': 1, 'more_days': 3}, 总数: 5
2025-05-23 08:00:03,634 - INFO - 当日下单当日激活比例: 4.7%（8/169）
2025-05-23 08:00:03,634 - INFO - 样本订单日期检查 (共5个): 169，熟龄度分布: 当天=8, 昨天=100, 前天=42, 更早=19
2025-05-23 08:00:03,635 - INFO - 环比分析: 当前=229单, 前一日=242单, 变化=-5.4%
2025-05-23 08:00:03,635 - INFO - 周趋势数据获取成功，共7天数据
2025-05-23 08:00:03,697 - INFO - 开始发送模板卡片消息到企业微信...
2025-05-23 08:00:03,697 - INFO - 开始发送消息到企业微信（全渠道生产环境）...
2025-05-23 08:00:03,958 - INFO - 企业微信API响应状态码: 200
2025-05-23 08:00:03,959 - INFO - 企业微信消息发送成功
2025-05-23 08:00:03,959 - INFO - 昨天的日报发送成功
2025-05-23 08:00:03,959 - INFO - 昨天的日报处理完成。
2025-05-23 08:00:03,959 - INFO - ==================================================
