2025-06-09 08:00:04,993 - INFO - ==================================================
2025-06-09 08:00:04,993 - INFO - 开始生成日报统计数据...
2025-06-09 08:00:05,019 - INFO - 开始生成 2025-06-08 的报表数据...
2025-06-09 08:00:05,019 - INFO - 获取 2025-06-08 的订单数据（下单时间维度）...
2025-06-09 08:00:05,066 - INFO - 成功获取到 156 条去重后的订单数据（下单时间维度）
2025-06-09 08:00:05,067 - INFO - 开始分类并过滤订单数据...
2025-06-09 08:00:05,067 - INFO - 订单分类完成: 有效订单 156 单，其中骑手卡 155 单，流量卡 1 单
2025-06-09 08:00:05,067 - INFO - 计算各渠道的D0下单和D1激活数据...
2025-06-09 08:00:05,099 - INFO - 成功获取到 123 条当日激活订单
2025-06-09 08:00:05,103 - INFO - 样本订单日期检查 (共5个):
2025-06-09 08:00:05,103 - INFO - 订单 20250607101896810555: 下单时间=2025-06-07, 激活时间=2025-06-08, 相差=1天
2025-06-09 08:00:05,103 - INFO - 订单 20250605164670256437: 下单时间=2025-06-05, 激活时间=2025-06-08, 相差=3天
2025-06-09 08:00:05,103 - INFO - 订单 20250606214203285496: 下单时间=2025-06-06, 激活时间=2025-06-08, 相差=2天
2025-06-09 08:00:05,103 - INFO - 订单 20250607001651017936: 下单时间=2025-06-07, 激活时间=2025-06-08, 相差=1天
2025-06-09 08:00:05,103 - INFO - 订单 20250608093815628128: 下单时间=2025-06-08, 激活时间=2025-06-08, 相差=0天
2025-06-09 08:00:05,103 - INFO - 熟龄度处理统计: 总订单=123, 成功处理=123, 处理失败=0
2025-06-09 08:00:05,104 - INFO - 熟龄度分布: 当天=7, 昨天=54, 前天=34, 更早=28
2025-06-09 08:00:05,104 - INFO - 骑手卡熟龄度分布: 当天=7, 昨天=54, 前天=34, 更早=28
2025-06-09 08:00:05,104 - INFO - 流量卡熟龄度分布: 当天=0, 昨天=0, 前天=0, 更早=0
2025-06-09 08:00:05,104 - INFO - 获取报表日期与前一日订单数据对比...
2025-06-09 08:00:05,104 - INFO - 对比日期: 报表日期=2025-06-08, 前一日=2025-06-07
2025-06-09 08:00:05,104 - INFO - 获取 2025-06-08 的订单数据（下单时间维度）...
2025-06-09 08:00:05,137 - INFO - 成功获取到 156 条去重后的订单数据（下单时间维度）
2025-06-09 08:00:05,138 - INFO - 获取 2025-06-07 的订单数据（下单时间维度）...
2025-06-09 08:00:05,172 - INFO - 成功获取到 174 条去重后的订单数据（下单时间维度）
2025-06-09 08:00:05,172 - INFO - 获取周订单趋势数据...
2025-06-09 08:00:05,173 - INFO - 查询日期范围: 2025-06-02 00:00:00 至 2025-06-08 23:59:59
2025-06-09 08:00:05,199 - INFO - 成功获取到 7 天的订单趋势数据
2025-06-09 08:00:05,199 - INFO - 熟龄度数据: {'same_day': 7, 'one_day': 54, 'two_days': 34, 'more_days': 28}
2025-06-09 08:00:05,199 - INFO - 骑手卡熟龄度数据: {'same_day': 7, 'one_day': 54, 'two_days': 34, 'more_days': 28}, 总数: 123
2025-06-09 08:00:05,199 - INFO - 流量卡熟龄度数据: {'same_day': 0, 'one_day': 0, 'two_days': 0, 'more_days': 0}, 总数: 0
2025-06-09 08:00:05,199 - INFO - 当日下单当日激活比例: 5.7%（7/123）
2025-06-09 08:00:05,199 - INFO - 样本订单日期检查 (共5个): 123，熟龄度分布: 当天=7, 昨天=54, 前天=34, 更早=28
2025-06-09 08:00:05,199 - INFO - 环比分析: 当前=156单, 前一日=174单, 变化=-10.3%
2025-06-09 08:00:05,199 - INFO - 周趋势数据获取成功，共7天数据
2025-06-09 08:00:05,200 - INFO - 生成HTML报表模板，加载数据: 总活跃=123, 骑手卡=123, 流量卡=0
2025-06-09 08:00:05,200 - INFO - 骑手卡数据: [7, 54, 34, 28]
2025-06-09 08:00:05,200 - INFO - 流量卡数据: [0, 0, 0, 0]
2025-06-09 08:00:05,200 - INFO - HTML报表已生成: reports/report_20250608.html
2025-06-09 08:00:05,201 - INFO - 准备上传文件到FTP服务器: report_20250608.html
2025-06-09 08:00:05,201 - INFO - FTP配置: 主机=211.149.249.234, 端口=21, 目录=reports
2025-06-09 08:00:05,201 - INFO - 本地文件: reports/report_20250608.html, 大小: 53395 字节
2025-06-09 08:00:05,201 - INFO - 正在连接到FTP服务器: 211.149.249.234:21
2025-06-09 08:00:05,274 - INFO - 正在登录FTP服务器: 用户名=reports
2025-06-09 08:00:05,362 - INFO - FTP登录成功
2025-06-09 08:00:05,396 - INFO - 当前FTP目录: /
2025-06-09 08:00:05,396 - INFO - 尝试切换到目录: reports
2025-06-09 08:00:05,468 - INFO - 成功切换到目录: /reports
2025-06-09 08:00:05,608 - INFO - 上传前目录内容: ['report_20250506.html', 'report_20250522.html', 'report_20250511.html', 'report_20250515.html', 'report_20250605.html', 'report_20250518.html', 'report_20250516.html', 'report_20250512.html', 'report_20250503.html', 'report_20250602.html', 'report_20250508.html', 'report_20250502.html', 'report_20250601.html', 'report_20250531.html', 'report_20250519.html', 'report_20250525.html', '..', 'report_20250513.html', 'report_20250527.html', 'report_20250524.html', 'report_20250528.html', 'report_20250606.html', 'report_20250509.html', 'report_20250514.html', 'report_20250604.html', 'report_20250510.html', 'report_20250520.html', 'report_20250521.html', 'report_20250523.html', 'report_20250530.html', 'report_20250607.html', 'report_20250529.html', 'report_20250501.html', 'report_20250505.html', 'report_20250504.html', 'report_20250507.html', 'report_20250517.html', '.', 'report_20250526.html', 'report_20250603.html']
2025-06-09 08:00:05,608 - INFO - 开始上传文件: reports/report_20250608.html -> report_20250608.html
2025-06-09 08:00:05,860 - INFO - FTP上传结果: 226-File successfully transferred
226 0.111 seconds (measured here), 468.27 Kbytes per second
2025-06-09 08:00:05,999 - INFO - 上传后目录内容: ['report_20250506.html', 'report_20250522.html', 'report_20250511.html', 'report_20250515.html', 'report_20250605.html', 'report_20250518.html', 'report_20250516.html', 'report_20250512.html', 'report_20250503.html', 'report_20250602.html', 'report_20250508.html', 'report_20250502.html', 'report_20250601.html', 'report_20250531.html', 'report_20250519.html', 'report_20250608.html', 'report_20250525.html', '..', 'report_20250513.html', 'report_20250527.html', 'report_20250524.html', 'report_20250528.html', 'report_20250606.html', 'report_20250509.html', 'report_20250514.html', 'report_20250604.html', 'report_20250510.html', 'report_20250520.html', 'report_20250521.html', 'report_20250523.html', 'report_20250530.html', 'report_20250607.html', 'report_20250529.html', 'report_20250501.html', 'report_20250505.html', 'report_20250504.html', 'report_20250507.html', 'report_20250517.html', '.', 'report_20250526.html', 'report_20250603.html']
2025-06-09 08:00:05,999 - INFO - 文件已成功上传并验证: report_20250608.html
2025-06-09 08:00:06,000 - INFO - 文件访问URL: https://rep.331126.com/reports/report_20250608.html
2025-06-09 08:00:06,033 - INFO - FTP连接已关闭
2025-06-09 08:00:06,034 - INFO - 报表已上传到服务器，URL: https://rep.331126.com/reports/report_20250608.html
2025-06-09 08:00:06,034 - INFO - 日报生成完成
2025-06-09 08:00:06,034 - INFO - ==================================================
2025-06-09 08:00:06,237 - INFO - ==================================================
2025-06-09 08:00:06,237 - INFO - 开始生成并发送昨天的日报（全渠道生产环境）...
2025-06-09 08:00:06,237 - INFO - 开始执行昨天的日报发送流程（全渠道生产环境）...
2025-06-09 08:00:06,255 - INFO - 昨天日期: 2025-06-08
2025-06-09 08:00:06,255 - INFO - 开始获取昨天的订单数据（下单时间维度）...
2025-06-09 08:00:06,255 - INFO - 获取 2025-06-08 的订单数据（下单时间维度）...
2025-06-09 08:00:06,290 - INFO - 成功获取到 156 条去重后的订单数据（下单时间维度）
2025-06-09 08:00:06,290 - INFO - 开始获取昨天的订单数据（激活时间维度）...
2025-06-09 08:00:06,291 - INFO - 获取 2025-06-08 的订单数据（激活时间维度）...
2025-06-09 08:00:06,324 - INFO - 成功获取到 123 条去重后的订单数据（激活时间维度）
2025-06-09 08:00:06,325 - INFO - 成功获取到订单数据，继续生成日报
2025-06-09 08:00:06,325 - INFO - 开始生成昨天的日报...
2025-06-09 08:00:06,325 - INFO - 开始生成日报...
2025-06-09 08:00:06,325 - INFO - 开始分类并过滤订单数据...
2025-06-09 08:00:06,325 - INFO - 订单分类完成: 有效订单 156 单，其中骑手卡 155 单，大流量卡 1 单
2025-06-09 08:00:06,325 - INFO - 统计各类订单按渠道分类...
2025-06-09 08:00:06,325 - INFO - 开始分类并过滤订单数据...
2025-06-09 08:00:06,325 - INFO - 订单分类完成: 有效订单 123 单，其中骑手卡 123 单，大流量卡 0 单
2025-06-09 08:00:06,325 - INFO - 统计各类订单按渠道分类...
2025-06-09 08:00:06,326 - INFO - 日报生成完成
2025-06-09 08:00:06,326 - INFO - 开始分类并过滤订单数据...
2025-06-09 08:00:06,326 - INFO - 订单分类完成: 有效订单 156 单，其中骑手卡 155 单，大流量卡 1 单
2025-06-09 08:00:06,326 - INFO - 统计各类订单按渠道分类...
2025-06-09 08:00:06,326 - INFO - 开始分类并过滤订单数据...
2025-06-09 08:00:06,326 - INFO - 订单分类完成: 有效订单 123 单，其中骑手卡 123 单，大流量卡 0 单
2025-06-09 08:00:06,326 - INFO - 统计各类订单按渠道分类...
2025-06-09 08:00:06,328 - INFO - 开始生成 2025-06-08 的报表数据...
2025-06-09 08:00:06,329 - INFO - 获取 2025-06-08 的订单数据（下单时间维度）...
2025-06-09 08:00:06,362 - INFO - 成功获取到 156 条去重后的订单数据（下单时间维度）
2025-06-09 08:00:06,362 - INFO - 开始分类并过滤订单数据...
2025-06-09 08:00:06,362 - INFO - 订单分类完成: 有效订单 156 单，其中骑手卡 155 单，流量卡 1 单
2025-06-09 08:00:06,362 - INFO - 计算各渠道的D0下单和D1激活数据...
2025-06-09 08:00:06,393 - INFO - 成功获取到 123 条当日激活订单
2025-06-09 08:00:06,395 - INFO - 样本订单日期检查 (共5个):
2025-06-09 08:00:06,395 - INFO - 订单 20250607101896810555: 下单时间=2025-06-07, 激活时间=2025-06-08, 相差=1天
2025-06-09 08:00:06,395 - INFO - 订单 20250605164670256437: 下单时间=2025-06-05, 激活时间=2025-06-08, 相差=3天
2025-06-09 08:00:06,395 - INFO - 订单 20250606214203285496: 下单时间=2025-06-06, 激活时间=2025-06-08, 相差=2天
2025-06-09 08:00:06,395 - INFO - 订单 20250607001651017936: 下单时间=2025-06-07, 激活时间=2025-06-08, 相差=1天
2025-06-09 08:00:06,395 - INFO - 订单 20250608093815628128: 下单时间=2025-06-08, 激活时间=2025-06-08, 相差=0天
2025-06-09 08:00:06,395 - INFO - 熟龄度处理统计: 总订单=123, 成功处理=123, 处理失败=0
2025-06-09 08:00:06,395 - INFO - 熟龄度分布: 当天=7, 昨天=54, 前天=34, 更早=28
2025-06-09 08:00:06,395 - INFO - 骑手卡熟龄度分布: 当天=7, 昨天=54, 前天=34, 更早=28
2025-06-09 08:00:06,395 - INFO - 流量卡熟龄度分布: 当天=0, 昨天=0, 前天=0, 更早=0
2025-06-09 08:00:06,396 - INFO - 获取报表日期与前一日订单数据对比...
2025-06-09 08:00:06,396 - INFO - 对比日期: 报表日期=2025-06-08, 前一日=2025-06-07
2025-06-09 08:00:06,396 - INFO - 获取 2025-06-08 的订单数据（下单时间维度）...
2025-06-09 08:00:06,429 - INFO - 成功获取到 156 条去重后的订单数据（下单时间维度）
2025-06-09 08:00:06,429 - INFO - 获取 2025-06-07 的订单数据（下单时间维度）...
2025-06-09 08:00:06,462 - INFO - 成功获取到 174 条去重后的订单数据（下单时间维度）
2025-06-09 08:00:06,463 - INFO - 获取周订单趋势数据...
2025-06-09 08:00:06,463 - INFO - 查询日期范围: 2025-06-02 00:00:00 至 2025-06-08 23:59:59
2025-06-09 08:00:06,488 - INFO - 成功获取到 7 天的订单趋势数据
2025-06-09 08:00:06,488 - INFO - 熟龄度数据: {'same_day': 7, 'one_day': 54, 'two_days': 34, 'more_days': 28}
2025-06-09 08:00:06,488 - INFO - 骑手卡熟龄度数据: {'same_day': 7, 'one_day': 54, 'two_days': 34, 'more_days': 28}, 总数: 123
2025-06-09 08:00:06,488 - INFO - 流量卡熟龄度数据: {'same_day': 0, 'one_day': 0, 'two_days': 0, 'more_days': 0}, 总数: 0
2025-06-09 08:00:06,488 - INFO - 当日下单当日激活比例: 5.7%（7/123）
2025-06-09 08:00:06,488 - INFO - 样本订单日期检查 (共5个): 123，熟龄度分布: 当天=7, 昨天=54, 前天=34, 更早=28
2025-06-09 08:00:06,488 - INFO - 环比分析: 当前=156单, 前一日=174单, 变化=-10.3%
2025-06-09 08:00:06,489 - INFO - 周趋势数据获取成功，共7天数据
2025-06-09 08:00:06,534 - INFO - 开始发送模板卡片消息到企业微信...
2025-06-09 08:00:06,534 - INFO - 开始发送消息到企业微信（全渠道生产环境）...
2025-06-09 08:00:06,781 - INFO - 企业微信API响应状态码: 200
2025-06-09 08:00:06,781 - INFO - 企业微信消息发送成功
2025-06-09 08:00:06,781 - INFO - 昨天的日报发送成功
2025-06-09 08:00:06,781 - INFO - 昨天的日报处理完成。
2025-06-09 08:00:06,781 - INFO - ==================================================
