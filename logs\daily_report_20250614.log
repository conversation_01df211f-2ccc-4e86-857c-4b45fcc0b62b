2025-06-14 08:00:03,928 - INFO - ==================================================
2025-06-14 08:00:03,928 - INFO - 开始生成日报统计数据...
2025-06-14 08:00:03,960 - INFO - 开始生成 2025-06-13 的报表数据...
2025-06-14 08:00:03,961 - INFO - 获取 2025-06-13 的订单数据（下单时间维度）...
2025-06-14 08:00:04,013 - INFO - 成功获取到 192 条去重后的订单数据（下单时间维度）
2025-06-14 08:00:04,014 - INFO - 开始分类并过滤订单数据...
2025-06-14 08:00:04,014 - INFO - 订单分类完成: 有效订单 192 单，其中骑手卡 189 单，流量卡 3 单
2025-06-14 08:00:04,014 - INFO - 计算各渠道的D0下单和D1激活数据...
2025-06-14 08:00:04,050 - INFO - 成功获取到 148 条当日激活订单
2025-06-14 08:00:04,054 - INFO - 样本订单日期检查 (共5个):
2025-06-14 08:00:04,054 - INFO - 订单 20250612174365005136: 下单时间=2025-06-12, 激活时间=2025-06-13, 相差=1天
2025-06-14 08:00:04,054 - INFO - 订单 20250611210512699298: 下单时间=2025-06-11, 激活时间=2025-06-13, 相差=2天
2025-06-14 08:00:04,054 - INFO - 订单 20250612100966028140: 下单时间=2025-06-12, 激活时间=2025-06-13, 相差=1天
2025-06-14 08:00:04,055 - INFO - 订单 20250612224567776030: 下单时间=2025-06-12, 激活时间=2025-06-13, 相差=1天
2025-06-14 08:00:04,055 - INFO - 订单 20250612162863331217: 下单时间=2025-06-12, 激活时间=2025-06-13, 相差=1天
2025-06-14 08:00:04,055 - INFO - 熟龄度处理统计: 总订单=148, 成功处理=148, 处理失败=0
2025-06-14 08:00:04,055 - INFO - 熟龄度分布: 当天=7, 昨天=80, 前天=38, 更早=23
2025-06-14 08:00:04,055 - INFO - 骑手卡熟龄度分布: 当天=7, 昨天=80, 前天=38, 更早=23
2025-06-14 08:00:04,055 - INFO - 流量卡熟龄度分布: 当天=0, 昨天=0, 前天=0, 更早=0
2025-06-14 08:00:04,055 - INFO - 获取报表日期与前一日订单数据对比...
2025-06-14 08:00:04,055 - INFO - 对比日期: 报表日期=2025-06-13, 前一日=2025-06-12
2025-06-14 08:00:04,055 - INFO - 获取 2025-06-13 的订单数据（下单时间维度）...
2025-06-14 08:00:04,092 - INFO - 成功获取到 192 条去重后的订单数据（下单时间维度）
2025-06-14 08:00:04,092 - INFO - 获取 2025-06-12 的订单数据（下单时间维度）...
2025-06-14 08:00:04,128 - INFO - 成功获取到 203 条去重后的订单数据（下单时间维度）
2025-06-14 08:00:04,128 - INFO - 获取周订单趋势数据...
2025-06-14 08:00:04,128 - INFO - 查询日期范围: 2025-06-07 00:00:00 至 2025-06-13 23:59:59
2025-06-14 08:00:04,155 - INFO - 成功获取到 7 天的订单趋势数据
2025-06-14 08:00:04,155 - INFO - 熟龄度数据: {'same_day': 7, 'one_day': 80, 'two_days': 38, 'more_days': 23}
2025-06-14 08:00:04,155 - INFO - 骑手卡熟龄度数据: {'same_day': 7, 'one_day': 80, 'two_days': 38, 'more_days': 23}, 总数: 148
2025-06-14 08:00:04,155 - INFO - 流量卡熟龄度数据: {'same_day': 0, 'one_day': 0, 'two_days': 0, 'more_days': 0}, 总数: 0
2025-06-14 08:00:04,155 - INFO - 当日下单当日激活比例: 4.7%（7/148）
2025-06-14 08:00:04,155 - INFO - 样本订单日期检查 (共5个): 148，熟龄度分布: 当天=7, 昨天=80, 前天=38, 更早=23
2025-06-14 08:00:04,155 - INFO - 环比分析: 当前=192单, 前一日=203单, 变化=-5.4%
2025-06-14 08:00:04,156 - INFO - 周趋势数据获取成功，共7天数据
2025-06-14 08:00:04,156 - INFO - 生成HTML报表模板，加载数据: 总活跃=148, 骑手卡=148, 流量卡=0
2025-06-14 08:00:04,156 - INFO - 骑手卡数据: [7, 80, 38, 23]
2025-06-14 08:00:04,156 - INFO - 流量卡数据: [0, 0, 0, 0]
2025-06-14 08:00:04,157 - INFO - HTML报表已生成: reports/report_20250613.html
2025-06-14 08:00:04,157 - INFO - 准备上传文件到FTP服务器: report_20250613.html
2025-06-14 08:00:04,157 - INFO - FTP配置: 主机=211.149.249.234, 端口=21, 目录=reports
2025-06-14 08:00:04,157 - INFO - 本地文件: reports/report_20250613.html, 大小: 52352 字节
2025-06-14 08:00:04,157 - INFO - 正在连接到FTP服务器: 211.149.249.234:21
2025-06-14 08:00:04,232 - INFO - 正在登录FTP服务器: 用户名=reports
2025-06-14 08:00:04,341 - INFO - FTP登录成功
2025-06-14 08:00:04,378 - INFO - 当前FTP目录: /
2025-06-14 08:00:04,378 - INFO - 尝试切换到目录: reports
2025-06-14 08:00:04,451 - INFO - 成功切换到目录: /reports
2025-06-14 08:00:04,595 - INFO - 上传前目录内容: ['report_20250506.html', 'report_20250522.html', 'report_20250612.html', 'report_20250511.html', 'report_20250515.html', 'report_20250605.html', 'report_20250518.html', 'report_20250610.html', 'report_20250516.html', 'report_20250512.html', 'report_20250503.html', 'report_20250602.html', 'report_20250508.html', 'report_20250609.html', 'report_20250502.html', 'report_20250601.html', 'report_20250611.html', 'report_20250531.html', 'report_20250519.html', 'report_20250608.html', 'report_20250525.html', '..', 'report_20250513.html', 'report_20250527.html', 'report_20250524.html', 'report_20250528.html', 'report_20250606.html', 'report_20250509.html', 'report_20250514.html', 'report_20250604.html', 'report_20250510.html', 'report_20250520.html', 'report_20250521.html', 'report_20250523.html', 'report_20250530.html', 'report_20250607.html', 'report_20250529.html', 'report_20250501.html', 'report_20250505.html', 'report_20250504.html', 'report_20250507.html', 'report_20250517.html', '.', 'report_20250526.html', 'report_20250603.html']
2025-06-14 08:00:04,595 - INFO - 开始上传文件: reports/report_20250613.html -> report_20250613.html
2025-06-14 08:00:04,846 - INFO - FTP上传结果: 226-File successfully transferred
226 0.105 seconds (measured here), 485.60 Kbytes per second
2025-06-14 08:00:04,995 - INFO - 上传后目录内容: ['report_20250506.html', 'report_20250522.html', 'report_20250612.html', 'report_20250613.html', 'report_20250511.html', 'report_20250515.html', 'report_20250605.html', 'report_20250518.html', 'report_20250610.html', 'report_20250516.html', 'report_20250512.html', 'report_20250503.html', 'report_20250602.html', 'report_20250508.html', 'report_20250609.html', 'report_20250502.html', 'report_20250601.html', 'report_20250611.html', 'report_20250531.html', 'report_20250519.html', 'report_20250608.html', 'report_20250525.html', '..', 'report_20250513.html', 'report_20250527.html', 'report_20250524.html', 'report_20250528.html', 'report_20250606.html', 'report_20250509.html', 'report_20250514.html', 'report_20250604.html', 'report_20250510.html', 'report_20250520.html', 'report_20250521.html', 'report_20250523.html', 'report_20250530.html', 'report_20250607.html', 'report_20250529.html', 'report_20250501.html', 'report_20250505.html', 'report_20250504.html', 'report_20250507.html', 'report_20250517.html', '.', 'report_20250526.html', 'report_20250603.html']
2025-06-14 08:00:04,995 - INFO - 文件已成功上传并验证: report_20250613.html
2025-06-14 08:00:04,995 - INFO - 文件访问URL: https://rep.331126.com/reports/report_20250613.html
2025-06-14 08:00:05,032 - INFO - FTP连接已关闭
2025-06-14 08:00:05,032 - INFO - 报表已上传到服务器，URL: https://rep.331126.com/reports/report_20250613.html
2025-06-14 08:00:05,032 - INFO - 日报生成完成
2025-06-14 08:00:05,032 - INFO - ==================================================
2025-06-14 08:00:05,213 - INFO - ==================================================
2025-06-14 08:00:05,213 - INFO - 开始生成并发送昨天的日报（全渠道生产环境）...
2025-06-14 08:00:05,213 - INFO - 开始执行昨天的日报发送流程（全渠道生产环境）...
2025-06-14 08:00:05,230 - INFO - 昨天日期: 2025-06-13
2025-06-14 08:00:05,230 - INFO - 开始获取昨天的订单数据（下单时间维度）...
2025-06-14 08:00:05,230 - INFO - 获取 2025-06-13 的订单数据（下单时间维度）...
2025-06-14 08:00:05,263 - INFO - 成功获取到 192 条去重后的订单数据（下单时间维度）
2025-06-14 08:00:05,263 - INFO - 开始获取昨天的订单数据（激活时间维度）...
2025-06-14 08:00:05,263 - INFO - 获取 2025-06-13 的订单数据（激活时间维度）...
2025-06-14 08:00:05,296 - INFO - 成功获取到 148 条去重后的订单数据（激活时间维度）
2025-06-14 08:00:05,296 - INFO - 成功获取到订单数据，继续生成日报
2025-06-14 08:00:05,296 - INFO - 开始生成昨天的日报...
2025-06-14 08:00:05,296 - INFO - 开始生成日报...
2025-06-14 08:00:05,296 - INFO - 开始分类并过滤订单数据...
2025-06-14 08:00:05,296 - INFO - 订单分类完成: 有效订单 192 单，其中骑手卡 189 单，大流量卡 3 单
2025-06-14 08:00:05,296 - INFO - 统计各类订单按渠道分类...
2025-06-14 08:00:05,297 - INFO - 开始分类并过滤订单数据...
2025-06-14 08:00:05,297 - INFO - 订单分类完成: 有效订单 148 单，其中骑手卡 148 单，大流量卡 0 单
2025-06-14 08:00:05,297 - INFO - 统计各类订单按渠道分类...
2025-06-14 08:00:05,297 - INFO - 日报生成完成
2025-06-14 08:00:05,297 - INFO - 开始分类并过滤订单数据...
2025-06-14 08:00:05,297 - INFO - 订单分类完成: 有效订单 192 单，其中骑手卡 189 单，大流量卡 3 单
2025-06-14 08:00:05,297 - INFO - 统计各类订单按渠道分类...
2025-06-14 08:00:05,298 - INFO - 开始分类并过滤订单数据...
2025-06-14 08:00:05,298 - INFO - 订单分类完成: 有效订单 148 单，其中骑手卡 148 单，大流量卡 0 单
2025-06-14 08:00:05,298 - INFO - 统计各类订单按渠道分类...
2025-06-14 08:00:05,300 - INFO - 开始生成 2025-06-13 的报表数据...
2025-06-14 08:00:05,300 - INFO - 获取 2025-06-13 的订单数据（下单时间维度）...
2025-06-14 08:00:05,336 - INFO - 成功获取到 192 条去重后的订单数据（下单时间维度）
2025-06-14 08:00:05,336 - INFO - 开始分类并过滤订单数据...
2025-06-14 08:00:05,337 - INFO - 订单分类完成: 有效订单 192 单，其中骑手卡 189 单，流量卡 3 单
2025-06-14 08:00:05,337 - INFO - 计算各渠道的D0下单和D1激活数据...
2025-06-14 08:00:05,371 - INFO - 成功获取到 148 条当日激活订单
2025-06-14 08:00:05,373 - INFO - 样本订单日期检查 (共5个):
2025-06-14 08:00:05,373 - INFO - 订单 20250612174365005136: 下单时间=2025-06-12, 激活时间=2025-06-13, 相差=1天
2025-06-14 08:00:05,373 - INFO - 订单 20250611210512699298: 下单时间=2025-06-11, 激活时间=2025-06-13, 相差=2天
2025-06-14 08:00:05,373 - INFO - 订单 20250612100966028140: 下单时间=2025-06-12, 激活时间=2025-06-13, 相差=1天
2025-06-14 08:00:05,373 - INFO - 订单 20250612224567776030: 下单时间=2025-06-12, 激活时间=2025-06-13, 相差=1天
2025-06-14 08:00:05,373 - INFO - 订单 20250612162863331217: 下单时间=2025-06-12, 激活时间=2025-06-13, 相差=1天
2025-06-14 08:00:05,373 - INFO - 熟龄度处理统计: 总订单=148, 成功处理=148, 处理失败=0
2025-06-14 08:00:05,373 - INFO - 熟龄度分布: 当天=7, 昨天=80, 前天=38, 更早=23
2025-06-14 08:00:05,373 - INFO - 骑手卡熟龄度分布: 当天=7, 昨天=80, 前天=38, 更早=23
2025-06-14 08:00:05,374 - INFO - 流量卡熟龄度分布: 当天=0, 昨天=0, 前天=0, 更早=0
2025-06-14 08:00:05,374 - INFO - 获取报表日期与前一日订单数据对比...
2025-06-14 08:00:05,374 - INFO - 对比日期: 报表日期=2025-06-13, 前一日=2025-06-12
2025-06-14 08:00:05,374 - INFO - 获取 2025-06-13 的订单数据（下单时间维度）...
2025-06-14 08:00:05,410 - INFO - 成功获取到 192 条去重后的订单数据（下单时间维度）
2025-06-14 08:00:05,410 - INFO - 获取 2025-06-12 的订单数据（下单时间维度）...
2025-06-14 08:00:05,446 - INFO - 成功获取到 203 条去重后的订单数据（下单时间维度）
2025-06-14 08:00:05,447 - INFO - 获取周订单趋势数据...
2025-06-14 08:00:05,447 - INFO - 查询日期范围: 2025-06-07 00:00:00 至 2025-06-13 23:59:59
2025-06-14 08:00:05,472 - INFO - 成功获取到 7 天的订单趋势数据
2025-06-14 08:00:05,472 - INFO - 熟龄度数据: {'same_day': 7, 'one_day': 80, 'two_days': 38, 'more_days': 23}
2025-06-14 08:00:05,472 - INFO - 骑手卡熟龄度数据: {'same_day': 7, 'one_day': 80, 'two_days': 38, 'more_days': 23}, 总数: 148
2025-06-14 08:00:05,472 - INFO - 流量卡熟龄度数据: {'same_day': 0, 'one_day': 0, 'two_days': 0, 'more_days': 0}, 总数: 0
2025-06-14 08:00:05,472 - INFO - 当日下单当日激活比例: 4.7%（7/148）
2025-06-14 08:00:05,473 - INFO - 样本订单日期检查 (共5个): 148，熟龄度分布: 当天=7, 昨天=80, 前天=38, 更早=23
2025-06-14 08:00:05,473 - INFO - 环比分析: 当前=192单, 前一日=203单, 变化=-5.4%
2025-06-14 08:00:05,473 - INFO - 周趋势数据获取成功，共7天数据
2025-06-14 08:00:05,528 - INFO - 开始发送模板卡片消息到企业微信...
2025-06-14 08:00:05,528 - INFO - 开始发送消息到企业微信（全渠道生产环境）...
2025-06-14 08:00:05,859 - INFO - 企业微信API响应状态码: 200
2025-06-14 08:00:05,859 - INFO - 企业微信消息发送成功
2025-06-14 08:00:05,860 - INFO - 昨天的日报发送成功
2025-06-14 08:00:05,860 - INFO - 昨天的日报处理完成。
2025-06-14 08:00:05,860 - INFO - ==================================================
