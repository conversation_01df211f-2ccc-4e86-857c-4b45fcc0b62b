[2025-05-15 08:00:01] 开始执行日报脚本
[2025-05-15 08:00:01] 工作目录: /volume3/SSD-soft/fzsrb
[2025-05-15 08:00:01] 检查Python依赖...
WARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager, possibly rendering your system unusable. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv. Use the --root-user-action option if you know what you are doing and want to suppress this warning.
[2025-05-15 08:00:01] 开始执行简单日报脚本...
2025-05-15 08:00:02,179 - INFO - ==================================================
2025-05-15 08:00:02,179 - INFO - 开始生成日报统计数据...
2025-05-15 08:00:02,196 - INFO - 开始生成 2025-05-14 的报表数据...
2025-05-15 08:00:02,196 - INFO - 获取 2025-05-14 的订单数据（下单时间维度）...
2025-05-15 08:00:02,227 - INFO - 成功获取到 214 条去重后的订单数据（下单时间维度）
2025-05-15 08:00:02,227 - INFO - 开始分类并过滤订单数据...
2025-05-15 08:00:02,227 - INFO - 订单分类完成: 有效订单 214 单，其中骑手卡 202 单，流量卡 12 单
2025-05-15 08:00:02,227 - INFO - 计算各渠道的D0下单和D1激活数据...
2025-05-15 08:00:02,254 - INFO - 成功获取到 167 条当日激活订单
2025-05-15 08:00:02,257 - INFO - 样本订单日期检查 (共5个):
2025-05-15 08:00:02,257 - INFO - 订单 20250513140512672475: 下单时间=2025-05-13, 激活时间=2025-05-14, 相差=1天
2025-05-15 08:00:02,257 - INFO - 订单 20250512173707818232: 下单时间=2025-05-12, 激活时间=2025-05-14, 相差=2天
2025-05-15 08:00:02,257 - INFO - 订单 20250513134474904691: 下单时间=2025-05-13, 激活时间=2025-05-14, 相差=1天
2025-05-15 08:00:02,258 - INFO - 订单 20250513160424771159: 下单时间=2025-05-13, 激活时间=2025-05-14, 相差=1天
2025-05-15 08:00:02,258 - INFO - 订单 20250513180664866115: 下单时间=2025-05-13, 激活时间=2025-05-14, 相差=1天
2025-05-15 08:00:02,258 - INFO - 熟龄度处理统计: 总订单=167, 成功处理=167, 处理失败=0
2025-05-15 08:00:02,258 - INFO - 熟龄度分布: 当天=16, 昨天=85, 前天=37, 更早=29
2025-05-15 08:00:02,258 - INFO - 骑手卡熟龄度分布: 当天=15, 昨天=84, 前天=36, 更早=26
2025-05-15 08:00:02,258 - INFO - 流量卡熟龄度分布: 当天=1, 昨天=1, 前天=1, 更早=3
2025-05-15 08:00:02,258 - INFO - 获取报表日期与前一日订单数据对比...
2025-05-15 08:00:02,258 - INFO - 对比日期: 报表日期=2025-05-14, 前一日=2025-05-13
2025-05-15 08:00:02,258 - INFO - 获取 2025-05-14 的订单数据（下单时间维度）...
2025-05-15 08:00:02,287 - INFO - 成功获取到 214 条去重后的订单数据（下单时间维度）
2025-05-15 08:00:02,287 - INFO - 获取 2025-05-13 的订单数据（下单时间维度）...
2025-05-15 08:00:02,319 - INFO - 成功获取到 275 条去重后的订单数据（下单时间维度）
2025-05-15 08:00:02,319 - INFO - 获取周订单趋势数据...
2025-05-15 08:00:02,320 - INFO - 查询日期范围: 2025-05-08 00:00:00 至 2025-05-14 23:59:59
2025-05-15 08:00:02,341 - INFO - 成功获取到 7 天的订单趋势数据
2025-05-15 08:00:02,341 - INFO - 熟龄度数据: {'same_day': 16, 'one_day': 85, 'two_days': 37, 'more_days': 29}
2025-05-15 08:00:02,341 - INFO - 骑手卡熟龄度数据: {'same_day': 15, 'one_day': 84, 'two_days': 36, 'more_days': 26}, 总数: 161
2025-05-15 08:00:02,341 - INFO - 流量卡熟龄度数据: {'same_day': 1, 'one_day': 1, 'two_days': 1, 'more_days': 3}, 总数: 6
2025-05-15 08:00:02,341 - INFO - 当日下单当日激活比例: 9.6%（16/167）
2025-05-15 08:00:02,342 - INFO - 样本订单日期检查 (共5个): 167，熟龄度分布: 当天=16, 昨天=85, 前天=37, 更早=29
2025-05-15 08:00:02,342 - INFO - 环比分析: 当前=214单, 前一日=275单, 变化=-22.2%
2025-05-15 08:00:02,342 - INFO - 周趋势数据获取成功，共7天数据
2025-05-15 08:00:02,342 - INFO - 生成HTML报表模板，加载数据: 总活跃=167, 骑手卡=161, 流量卡=6
2025-05-15 08:00:02,342 - INFO - 骑手卡数据: [15, 84, 36, 26]
2025-05-15 08:00:02,342 - INFO - 流量卡数据: [1, 1, 1, 3]
2025-05-15 08:00:02,342 - INFO - HTML报表已生成: reports/report_20250514.html
2025-05-15 08:00:02,343 - INFO - 准备上传文件到FTP服务器: report_20250514.html
2025-05-15 08:00:02,343 - INFO - FTP配置: 主机=211.149.249.234, 端口=21, 目录=reports
2025-05-15 08:00:02,343 - INFO - 本地文件: reports/report_20250514.html, 大小: 51744 字节
2025-05-15 08:00:02,343 - INFO - 正在连接到FTP服务器: 211.149.249.234:21
2025-05-15 08:00:02,416 - INFO - 正在登录FTP服务器: 用户名=reports
2025-05-15 08:00:02,518 - INFO - FTP登录成功
2025-05-15 08:00:02,553 - INFO - 当前FTP目录: /
2025-05-15 08:00:02,553 - INFO - 尝试切换到目录: reports
2025-05-15 08:00:02,624 - INFO - 成功切换到目录: /reports
2025-05-15 08:00:02,767 - INFO - 上传前目录内容: ['report_20250506.html', 'report_20250511.html', 'report_20250512.html', 'report_20250503.html', 'report_20250508.html', 'report_20250502.html', '..', 'report_20250513.html', 'report_20250509.html', 'report_20250510.html', 'report_20250501.html', 'report_20250505.html', 'report_20250504.html', 'report_20250507.html', '.']
2025-05-15 08:00:02,767 - INFO - 开始上传文件: reports/report_20250514.html -> report_20250514.html
2025-05-15 08:00:03,012 - INFO - FTP上传结果: 226-File successfully transferred
226 0.104 seconds (measured here), 484.69 Kbytes per second
2025-05-15 08:00:03,152 - INFO - 上传后目录内容: ['report_20250506.html', 'report_20250511.html', 'report_20250512.html', 'report_20250503.html', 'report_20250508.html', 'report_20250502.html', '..', 'report_20250513.html', 'report_20250509.html', 'report_20250514.html', 'report_20250510.html', 'report_20250501.html', 'report_20250505.html', 'report_20250504.html', 'report_20250507.html', '.']
2025-05-15 08:00:03,152 - INFO - 文件已成功上传并验证: report_20250514.html
2025-05-15 08:00:03,152 - INFO - 文件访问URL: https://rep.331126.com/reports/report_20250514.html
2025-05-15 08:00:03,188 - INFO - FTP连接已关闭
2025-05-15 08:00:03,188 - INFO - 报表已上传到服务器，URL: https://rep.331126.com/reports/report_20250514.html
2025-05-15 08:00:03,188 - INFO - 日报生成完成
2025-05-15 08:00:03,188 - INFO - ==================================================
*get* '220---------- Welcome to Pure-FTPd [privsep] [TLS] ----------\n'
*get* '220-You are user number 1 of 50 allowed.\n'
*get* '220-Local time is now 08:00. Server port: 21.\n'
*get* '220-This is a private system - No anonymous login\n'
*get* '220-IPv6 connections are also welcome on this server.\n'
*get* '220 You will be disconnected after 15 minutes of inactivity.\n'
*resp* '220---------- Welcome to Pure-FTPd [privsep] [TLS] ----------\n220-You are user number 1 of 50 allowed.\n220-Local time is now 08:00. Server port: 21.\n220-This is a private system - No anonymous login\n220-IPv6 connections are also welcome on this server.\n220 You will be disconnected after 15 minutes of inactivity.'
*cmd* 'USER reports'
*put* 'USER reports\r\n'
*get* '331 User reports OK. Password required\n'
*resp* '331 User reports OK. Password required'
*cmd* 'PASS ****************'
*put* 'PASS ****************\r\n'
*get* '230 OK. Current directory is /\n'
*resp* '230 OK. Current directory is /'
*cmd* 'PWD'
*put* 'PWD\r\n'
*get* '257 "/" is your current location\n'
*resp* '257 "/" is your current location'
*cmd* 'CWD reports'
*put* 'CWD reports\r\n'
*get* '250 OK. Current directory is /reports\n'
*resp* '250 OK. Current directory is /reports'
*cmd* 'PWD'
*put* 'PWD\r\n'
*get* '257 "/reports" is your current location\n'
*resp* '257 "/reports" is your current location'
*cmd* 'TYPE A'
*put* 'TYPE A\r\n'
*get* '200 TYPE is now ASCII\n'
*resp* '200 TYPE is now ASCII'
*cmd* 'PASV'
*put* 'PASV\r\n'
*get* '227 Entering Passive Mode (211,149,249,234,155,84)\n'
*resp* '227 Entering Passive Mode (211,149,249,234,155,84)'
*cmd* 'NLST'
*put* 'NLST\r\n'
*get* '150 Accepted data connection\n'
*resp* '150 Accepted data connection'
*get* '226 15 matches total\n'
*resp* '226 15 matches total'
*cmd* 'TYPE I'
*put* 'TYPE I\r\n'
*get* '200 TYPE is now 8-bit binary\n'
*resp* '200 TYPE is now 8-bit binary'
*cmd* 'PASV'
*put* 'PASV\r\n'
*get* '227 Entering Passive Mode (211,149,249,234,156,45)\n'
*resp* '227 Entering Passive Mode (211,149,249,234,156,45)'
*cmd* 'STOR report_20250514.html'
*put* 'STOR report_20250514.html\r\n'
*get* '150 Accepted data connection\n'
*resp* '150 Accepted data connection'
*get* '226-File successfully transferred\n'
*get* '226 0.104 seconds (measured here), 484.69 Kbytes per second\n'
*resp* '226-File successfully transferred\n226 0.104 seconds (measured here), 484.69 Kbytes per second'
*cmd* 'TYPE A'
*put* 'TYPE A\r\n'
*get* '200 TYPE is now ASCII\n'
*resp* '200 TYPE is now ASCII'
*cmd* 'PASV'
*put* 'PASV\r\n'
*get* '227 Entering Passive Mode (211,149,249,234,155,132)\n'
*resp* '227 Entering Passive Mode (211,149,249,234,155,132)'
*cmd* 'NLST'
*put* 'NLST\r\n'
*get* '150 Accepted data connection\n'
*resp* '150 Accepted data connection'
*get* '226 16 matches total\n'
*resp* '226 16 matches total'
*cmd* 'QUIT'
*put* 'QUIT\r\n'
*get* '221-Goodbye. You uploaded 51 and downloaded 0 kbytes.\n'
*get* '221 Logout.\n'
*resp* '221-Goodbye. You uploaded 51 and downloaded 0 kbytes.\n221 Logout.'
[2025-05-15 08:00:01] 简单日报脚本执行成功
[2025-05-15 08:00:01] 开始执行全渠道日报脚本...
2025-05-15 08:00:03,341 - INFO - ==================================================
2025-05-15 08:00:03,341 - INFO - 开始生成并发送昨天的日报（全渠道生产环境）...
2025-05-15 08:00:03,341 - INFO - 开始执行昨天的日报发送流程（全渠道生产环境）...
2025-05-15 08:00:03,358 - INFO - 昨天日期: 2025-05-14
2025-05-15 08:00:03,358 - INFO - 开始获取昨天的订单数据（下单时间维度）...
2025-05-15 08:00:03,358 - INFO - 获取 2025-05-14 的订单数据（下单时间维度）...
2025-05-15 08:00:03,389 - INFO - 成功获取到 214 条去重后的订单数据（下单时间维度）
2025-05-15 08:00:03,389 - INFO - 开始获取昨天的订单数据（激活时间维度）...
2025-05-15 08:00:03,389 - INFO - 获取 2025-05-14 的订单数据（激活时间维度）...
2025-05-15 08:00:03,418 - INFO - 成功获取到 167 条去重后的订单数据（激活时间维度）
2025-05-15 08:00:03,418 - INFO - 成功获取到订单数据，继续生成日报
2025-05-15 08:00:03,418 - INFO - 开始生成昨天的日报...
2025-05-15 08:00:03,418 - INFO - 开始生成日报...
2025-05-15 08:00:03,418 - INFO - 开始分类并过滤订单数据...
2025-05-15 08:00:03,418 - INFO - 订单分类完成: 有效订单 214 单，其中骑手卡 202 单，大流量卡 12 单
2025-05-15 08:00:03,418 - INFO - 统计各类订单按渠道分类...
2025-05-15 08:00:03,418 - INFO - 开始分类并过滤订单数据...
2025-05-15 08:00:03,419 - INFO - 订单分类完成: 有效订单 167 单，其中骑手卡 161 单，大流量卡 6 单
2025-05-15 08:00:03,419 - INFO - 统计各类订单按渠道分类...
2025-05-15 08:00:03,419 - INFO - 日报生成完成
2025-05-15 08:00:03,419 - INFO - 开始分类并过滤订单数据...
2025-05-15 08:00:03,419 - INFO - 订单分类完成: 有效订单 214 单，其中骑手卡 202 单，大流量卡 12 单
2025-05-15 08:00:03,419 - INFO - 统计各类订单按渠道分类...
2025-05-15 08:00:03,419 - INFO - 开始分类并过滤订单数据...
2025-05-15 08:00:03,420 - INFO - 订单分类完成: 有效订单 167 单，其中骑手卡 161 单，大流量卡 6 单
2025-05-15 08:00:03,420 - INFO - 统计各类订单按渠道分类...
2025-05-15 08:00:03,421 - INFO - 开始生成 2025-05-14 的报表数据...
2025-05-15 08:00:03,421 - INFO - 获取 2025-05-14 的订单数据（下单时间维度）...
2025-05-15 08:00:03,449 - INFO - 成功获取到 214 条去重后的订单数据（下单时间维度）
2025-05-15 08:00:03,450 - INFO - 开始分类并过滤订单数据...
2025-05-15 08:00:03,450 - INFO - 订单分类完成: 有效订单 214 单，其中骑手卡 202 单，流量卡 12 单
2025-05-15 08:00:03,450 - INFO - 计算各渠道的D0下单和D1激活数据...
2025-05-15 08:00:03,476 - INFO - 成功获取到 167 条当日激活订单
2025-05-15 08:00:03,478 - INFO - 样本订单日期检查 (共5个):
2025-05-15 08:00:03,478 - INFO - 订单 20250513140512672475: 下单时间=2025-05-13, 激活时间=2025-05-14, 相差=1天
2025-05-15 08:00:03,478 - INFO - 订单 20250512173707818232: 下单时间=2025-05-12, 激活时间=2025-05-14, 相差=2天
2025-05-15 08:00:03,478 - INFO - 订单 20250513134474904691: 下单时间=2025-05-13, 激活时间=2025-05-14, 相差=1天
2025-05-15 08:00:03,478 - INFO - 订单 20250513160424771159: 下单时间=2025-05-13, 激活时间=2025-05-14, 相差=1天
2025-05-15 08:00:03,478 - INFO - 订单 20250513180664866115: 下单时间=2025-05-13, 激活时间=2025-05-14, 相差=1天
2025-05-15 08:00:03,478 - INFO - 熟龄度处理统计: 总订单=167, 成功处理=167, 处理失败=0
2025-05-15 08:00:03,478 - INFO - 熟龄度分布: 当天=16, 昨天=85, 前天=37, 更早=29
2025-05-15 08:00:03,478 - INFO - 骑手卡熟龄度分布: 当天=15, 昨天=84, 前天=36, 更早=26
2025-05-15 08:00:03,478 - INFO - 流量卡熟龄度分布: 当天=1, 昨天=1, 前天=1, 更早=3
2025-05-15 08:00:03,479 - INFO - 获取报表日期与前一日订单数据对比...
2025-05-15 08:00:03,479 - INFO - 对比日期: 报表日期=2025-05-14, 前一日=2025-05-13
2025-05-15 08:00:03,479 - INFO - 获取 2025-05-14 的订单数据（下单时间维度）...
2025-05-15 08:00:03,506 - INFO - 成功获取到 214 条去重后的订单数据（下单时间维度）
2025-05-15 08:00:03,506 - INFO - 获取 2025-05-13 的订单数据（下单时间维度）...
2025-05-15 08:00:03,537 - INFO - 成功获取到 275 条去重后的订单数据（下单时间维度）
2025-05-15 08:00:03,538 - INFO - 获取周订单趋势数据...
2025-05-15 08:00:03,538 - INFO - 查询日期范围: 2025-05-08 00:00:00 至 2025-05-14 23:59:59
2025-05-15 08:00:03,561 - INFO - 成功获取到 7 天的订单趋势数据
2025-05-15 08:00:03,561 - INFO - 熟龄度数据: {'same_day': 16, 'one_day': 85, 'two_days': 37, 'more_days': 29}
2025-05-15 08:00:03,561 - INFO - 骑手卡熟龄度数据: {'same_day': 15, 'one_day': 84, 'two_days': 36, 'more_days': 26}, 总数: 161
2025-05-15 08:00:03,561 - INFO - 流量卡熟龄度数据: {'same_day': 1, 'one_day': 1, 'two_days': 1, 'more_days': 3}, 总数: 6
2025-05-15 08:00:03,561 - INFO - 当日下单当日激活比例: 9.6%（16/167）
2025-05-15 08:00:03,562 - INFO - 样本订单日期检查 (共5个): 167，熟龄度分布: 当天=16, 昨天=85, 前天=37, 更早=29
2025-05-15 08:00:03,562 - INFO - 环比分析: 当前=214单, 前一日=275单, 变化=-22.2%
2025-05-15 08:00:03,562 - INFO - 周趋势数据获取成功，共7天数据
2025-05-15 08:00:03,562 - INFO - 开始发送模板卡片消息到企业微信...
2025-05-15 08:00:03,562 - INFO - 开始发送消息到企业微信（全渠道生产环境）...
2025-05-15 08:00:03,819 - INFO - 企业微信API响应状态码: 200
2025-05-15 08:00:03,820 - INFO - 企业微信消息发送成功
2025-05-15 08:00:03,820 - INFO - 昨天的日报发送成功
2025-05-15 08:00:03,820 - INFO - 昨天的日报处理完成。
2025-05-15 08:00:03,820 - INFO - ==================================================
[2025-05-15 08:00:01] 全渠道日报脚本执行成功
[2025-05-15 08:00:01] 开始执行OPPO渠道日报脚本...
2025-05-15 08:00:03,988 - INFO - ==================================================
2025-05-15 08:00:03,988 - INFO - 开始生成并发送OPPO和荣耀渠道日报（OPPO生产环境）...
2025-05-15 08:00:03,988 - INFO - 开始执行OPPO和荣耀渠道日报发送流程（OPPO生产环境）...
2025-05-15 08:00:04,007 - INFO - 查询日期: 2025-05-14
2025-05-15 08:00:04,007 - INFO - 获取 2025-05-14 的OPPO和荣耀渠道订单数据（下单时间维度）...
2025-05-15 08:00:04,026 - INFO - 查询到 8 条去重后的订单数据
2025-05-15 08:00:04,026 - INFO - 开始生成OPPO和荣耀渠道Markdown格式日报...
2025-05-15 08:00:04,026 - INFO - 开始处理订单数据，总数据条数: 8
2025-05-15 08:00:04,026 - INFO - 统计结果: 总订单 8
2025-05-15 08:00:04,026 - INFO -   OPPO: 7单
2025-05-15 08:00:04,027 - INFO -   荣耀: 1单
2025-05-15 08:00:04,027 - INFO - Markdown格式日报生成完成
2025-05-15 08:00:04,027 - INFO - 开始发送消息到企业微信（OPPO生产环境）...
2025-05-15 08:00:04,259 - INFO - 企业微信API响应状态码: 200
2025-05-15 08:00:04,260 - INFO - 企业微信消息发送成功
2025-05-15 08:00:04,260 - INFO - OPPO和荣耀渠道日报发送成功
2025-05-15 08:00:04,260 - INFO - OPPO和荣耀渠道日报处理完成。
2025-05-15 08:00:04,260 - INFO - ==================================================
[2025-05-15 08:00:01] OPPO渠道日报脚本执行成功
[2025-05-15 08:00:04] 所有日报脚本执行完成
----------------------------------------
