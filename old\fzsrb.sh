#!/bin/bash

# 设置日期时间变量
TIMESTAMP=$(date +"%Y-%m-%d %H:%M:%S")
LOG_DATE=$(date +"%Y%m%d")

# 设置工作目录
SCRIPT_DIR="/volume3/SSD-soft/fzsrb"
LOG_DIR="${SCRIPT_DIR}/logs"

# 确保日志目录存在
mkdir -p ${LOG_DIR}

# 日志文件
SHELL_LOG="${LOG_DIR}/shell_execution_${LOG_DATE}.log"

# 记录开始执行
echo "[$TIMESTAMP] 开始执行日报脚本" | tee -a ${SHELL_LOG}

# 切换到脚本目录
cd ${SCRIPT_DIR}
echo "[$TIMESTAMP] 工作目录: $(pwd)" | tee -a ${SHELL_LOG}

# 检查依赖库
echo "[$TIMESTAMP] 检查Python依赖..." | tee -a ${SHELL_LOG}

# 这里你可以选择使用系统Python或者虚拟环境
# 如果使用虚拟环境，取消下面的注释并修改路径
# source /path/to/your/venv/bin/activate

# 检查并安装缺失的依赖库
python -m pip install -q requests pytz pymysql >> ${SHELL_LOG} 2>&1
if [ $? -ne 0 ]; then
    echo "[$TIMESTAMP] 警告: 安装依赖库可能失败，请检查日志" | tee -a ${SHELL_LOG}
fi

# 初始化结果变量
RESULT_ALL=0

# 执行第一个Python脚本 - 简单日报
echo "[$TIMESTAMP] 开始执行简单日报脚本..." | tee -a ${SHELL_LOG}
python ${SCRIPT_DIR}/daily_report_simple.py >> ${SHELL_LOG} 2>&1
RESULT_SIMPLE=$?

# 检查执行结果
if [ ${RESULT_SIMPLE} -eq 0 ]; then
    echo "[$TIMESTAMP] 简单日报脚本执行成功" | tee -a ${SHELL_LOG}
else
    echo "[$TIMESTAMP] 错误: 简单日报脚本执行失败，退出码 ${RESULT_SIMPLE}" | tee -a ${SHELL_LOG}
    RESULT_ALL=1
fi

# 执行第二个Python脚本 - 全渠道日报
echo "[$TIMESTAMP] 开始执行全渠道日报脚本..." | tee -a ${SHELL_LOG}
python ${SCRIPT_DIR}/fzsrb.py >> ${SHELL_LOG} 2>&1
RESULT_FZSRB=$?

# 检查执行结果
if [ ${RESULT_FZSRB} -eq 0 ]; then
    echo "[$TIMESTAMP] 全渠道日报脚本执行成功" | tee -a ${SHELL_LOG}
else
    echo "[$TIMESTAMP] 错误: 全渠道日报脚本执行失败，退出码 ${RESULT_FZSRB}" | tee -a ${SHELL_LOG}
    RESULT_ALL=1
fi

# 执行第三个Python脚本 - OPPO渠道日报
echo "[$TIMESTAMP] 开始执行OPPO渠道日报脚本..." | tee -a ${SHELL_LOG}
python ${SCRIPT_DIR}/fzsrb_oppo.py >> ${SHELL_LOG} 2>&1
RESULT_OPPO=$?

# 检查执行结果
if [ ${RESULT_OPPO} -eq 0 ]; then
    echo "[$TIMESTAMP] OPPO渠道日报脚本执行成功" | tee -a ${SHELL_LOG}
else
    echo "[$TIMESTAMP] 错误: OPPO渠道日报脚本执行失败，退出码 ${RESULT_OPPO}" | tee -a ${SHELL_LOG}
    RESULT_ALL=1
fi

# 记录结束时间
TIMESTAMP=$(date +"%Y-%m-%d %H:%M:%S")
echo "[$TIMESTAMP] 所有日报脚本执行完成" | tee -a ${SHELL_LOG}
echo "----------------------------------------" | tee -a ${SHELL_LOG}

exit ${RESULT_ALL}
