2025-06-04 08:00:03,529 - INFO - ==================================================
2025-06-04 08:00:03,529 - INFO - 开始生成日报统计数据...
2025-06-04 08:00:03,562 - INFO - 开始生成 2025-06-03 的报表数据...
2025-06-04 08:00:03,562 - INFO - 获取 2025-06-03 的订单数据（下单时间维度）...
2025-06-04 08:00:03,612 - INFO - 成功获取到 195 条去重后的订单数据（下单时间维度）
2025-06-04 08:00:03,612 - INFO - 开始分类并过滤订单数据...
2025-06-04 08:00:03,612 - INFO - 订单分类完成: 有效订单 195 单，其中骑手卡 187 单，流量卡 8 单
2025-06-04 08:00:03,612 - INFO - 计算各渠道的D0下单和D1激活数据...
2025-06-04 08:00:03,644 - INFO - 成功获取到 152 条当日激活订单
2025-06-04 08:00:03,648 - INFO - 样本订单日期检查 (共5个):
2025-06-04 08:00:03,648 - INFO - 订单 20250603131825109666: 下单时间=2025-06-03, 激活时间=2025-06-03, 相差=0天
2025-06-04 08:00:03,648 - INFO - 订单 20250602154226795349: 下单时间=2025-06-02, 激活时间=2025-06-03, 相差=1天
2025-06-04 08:00:03,648 - INFO - 订单 20250525195865933391: 下单时间=2025-05-25, 激活时间=2025-06-03, 相差=9天
2025-06-04 08:00:03,648 - INFO - 订单 20250602061192807072: 下单时间=2025-06-02, 激活时间=2025-06-03, 相差=1天
2025-06-04 08:00:03,648 - INFO - 订单 20250602112439278394: 下单时间=2025-06-02, 激活时间=2025-06-03, 相差=1天
2025-06-04 08:00:03,648 - INFO - 熟龄度处理统计: 总订单=152, 成功处理=152, 处理失败=0
2025-06-04 08:00:03,648 - INFO - 熟龄度分布: 当天=9, 昨天=63, 前天=34, 更早=46
2025-06-04 08:00:03,648 - INFO - 骑手卡熟龄度分布: 当天=9, 昨天=63, 前天=34, 更早=46
2025-06-04 08:00:03,648 - INFO - 流量卡熟龄度分布: 当天=0, 昨天=0, 前天=0, 更早=0
2025-06-04 08:00:03,649 - INFO - 获取报表日期与前一日订单数据对比...
2025-06-04 08:00:03,649 - INFO - 对比日期: 报表日期=2025-06-03, 前一日=2025-06-02
2025-06-04 08:00:03,649 - INFO - 获取 2025-06-03 的订单数据（下单时间维度）...
2025-06-04 08:00:03,682 - INFO - 成功获取到 195 条去重后的订单数据（下单时间维度）
2025-06-04 08:00:03,682 - INFO - 获取 2025-06-02 的订单数据（下单时间维度）...
2025-06-04 08:00:03,713 - INFO - 成功获取到 150 条去重后的订单数据（下单时间维度）
2025-06-04 08:00:03,713 - INFO - 获取周订单趋势数据...
2025-06-04 08:00:03,713 - INFO - 查询日期范围: 2025-05-28 00:00:00 至 2025-06-03 23:59:59
2025-06-04 08:00:03,738 - INFO - 成功获取到 7 天的订单趋势数据
2025-06-04 08:00:03,738 - INFO - 熟龄度数据: {'same_day': 9, 'one_day': 63, 'two_days': 34, 'more_days': 46}
2025-06-04 08:00:03,738 - INFO - 骑手卡熟龄度数据: {'same_day': 9, 'one_day': 63, 'two_days': 34, 'more_days': 46}, 总数: 152
2025-06-04 08:00:03,738 - INFO - 流量卡熟龄度数据: {'same_day': 0, 'one_day': 0, 'two_days': 0, 'more_days': 0}, 总数: 0
2025-06-04 08:00:03,738 - INFO - 当日下单当日激活比例: 5.9%（9/152）
2025-06-04 08:00:03,738 - INFO - 样本订单日期检查 (共5个): 152，熟龄度分布: 当天=9, 昨天=63, 前天=34, 更早=46
2025-06-04 08:00:03,738 - INFO - 环比分析: 当前=195单, 前一日=150单, 变化=30.0%
2025-06-04 08:00:03,738 - INFO - 周趋势数据获取成功，共7天数据
2025-06-04 08:00:03,739 - INFO - 生成HTML报表模板，加载数据: 总活跃=152, 骑手卡=152, 流量卡=0
2025-06-04 08:00:03,739 - INFO - 骑手卡数据: [9, 63, 34, 46]
2025-06-04 08:00:03,739 - INFO - 流量卡数据: [0, 0, 0, 0]
2025-06-04 08:00:03,740 - INFO - HTML报表已生成: reports/report_20250603.html
2025-06-04 08:00:03,740 - INFO - 准备上传文件到FTP服务器: report_20250603.html
2025-06-04 08:00:03,740 - INFO - FTP配置: 主机=211.149.249.234, 端口=21, 目录=reports
2025-06-04 08:00:03,740 - INFO - 本地文件: reports/report_20250603.html, 大小: 51066 字节
2025-06-04 08:00:03,740 - INFO - 正在连接到FTP服务器: 211.149.249.234:21
2025-06-04 08:00:03,808 - INFO - 正在登录FTP服务器: 用户名=reports
2025-06-04 08:00:03,902 - INFO - FTP登录成功
2025-06-04 08:00:03,935 - INFO - 当前FTP目录: /
2025-06-04 08:00:03,935 - INFO - 尝试切换到目录: reports
2025-06-04 08:00:04,003 - INFO - 成功切换到目录: /reports
2025-06-04 08:00:04,135 - INFO - 上传前目录内容: ['report_20250506.html', 'report_20250522.html', 'report_20250511.html', 'report_20250515.html', 'report_20250518.html', 'report_20250516.html', 'report_20250512.html', 'report_20250503.html', 'report_20250602.html', 'report_20250508.html', 'report_20250502.html', 'report_20250601.html', 'report_20250531.html', 'report_20250519.html', 'report_20250525.html', '..', 'report_20250513.html', 'report_20250527.html', 'report_20250524.html', 'report_20250528.html', 'report_20250509.html', 'report_20250514.html', 'report_20250510.html', 'report_20250520.html', 'report_20250521.html', 'report_20250523.html', 'report_20250530.html', 'report_20250529.html', 'report_20250501.html', 'report_20250505.html', 'report_20250504.html', 'report_20250507.html', 'report_20250517.html', '.', 'report_20250526.html']
2025-06-04 08:00:04,135 - INFO - 开始上传文件: reports/report_20250603.html -> report_20250603.html
2025-06-04 08:00:04,365 - INFO - FTP上传结果: 226-File successfully transferred
226 0.096 seconds (measured here), 0.51 Mbytes per second
2025-06-04 08:00:04,499 - INFO - 上传后目录内容: ['report_20250506.html', 'report_20250522.html', 'report_20250511.html', 'report_20250515.html', 'report_20250518.html', 'report_20250516.html', 'report_20250512.html', 'report_20250503.html', 'report_20250602.html', 'report_20250508.html', 'report_20250502.html', 'report_20250601.html', 'report_20250531.html', 'report_20250519.html', 'report_20250525.html', '..', 'report_20250513.html', 'report_20250527.html', 'report_20250524.html', 'report_20250528.html', 'report_20250509.html', 'report_20250514.html', 'report_20250510.html', 'report_20250520.html', 'report_20250521.html', 'report_20250523.html', 'report_20250530.html', 'report_20250529.html', 'report_20250501.html', 'report_20250505.html', 'report_20250504.html', 'report_20250507.html', 'report_20250517.html', '.', 'report_20250526.html', 'report_20250603.html']
2025-06-04 08:00:04,499 - INFO - 文件已成功上传并验证: report_20250603.html
2025-06-04 08:00:04,499 - INFO - 文件访问URL: https://rep.331126.com/reports/report_20250603.html
2025-06-04 08:00:04,534 - INFO - FTP连接已关闭
2025-06-04 08:00:04,534 - INFO - 报表已上传到服务器，URL: https://rep.331126.com/reports/report_20250603.html
2025-06-04 08:00:04,534 - INFO - 日报生成完成
2025-06-04 08:00:04,534 - INFO - ==================================================
2025-06-04 08:00:04,732 - INFO - ==================================================
2025-06-04 08:00:04,732 - INFO - 开始生成并发送昨天的日报（全渠道生产环境）...
2025-06-04 08:00:04,732 - INFO - 开始执行昨天的日报发送流程（全渠道生产环境）...
2025-06-04 08:00:04,750 - INFO - 昨天日期: 2025-06-03
2025-06-04 08:00:04,751 - INFO - 开始获取昨天的订单数据（下单时间维度）...
2025-06-04 08:00:04,751 - INFO - 获取 2025-06-03 的订单数据（下单时间维度）...
2025-06-04 08:00:04,784 - INFO - 成功获取到 195 条去重后的订单数据（下单时间维度）
2025-06-04 08:00:04,784 - INFO - 开始获取昨天的订单数据（激活时间维度）...
2025-06-04 08:00:04,784 - INFO - 获取 2025-06-03 的订单数据（激活时间维度）...
2025-06-04 08:00:04,815 - INFO - 成功获取到 152 条去重后的订单数据（激活时间维度）
2025-06-04 08:00:04,815 - INFO - 成功获取到订单数据，继续生成日报
2025-06-04 08:00:04,815 - INFO - 开始生成昨天的日报...
2025-06-04 08:00:04,815 - INFO - 开始生成日报...
2025-06-04 08:00:04,815 - INFO - 开始分类并过滤订单数据...
2025-06-04 08:00:04,815 - INFO - 订单分类完成: 有效订单 195 单，其中骑手卡 187 单，大流量卡 8 单
2025-06-04 08:00:04,815 - INFO - 统计各类订单按渠道分类...
2025-06-04 08:00:04,816 - INFO - 开始分类并过滤订单数据...
2025-06-04 08:00:04,816 - INFO - 订单分类完成: 有效订单 152 单，其中骑手卡 152 单，大流量卡 0 单
2025-06-04 08:00:04,816 - INFO - 统计各类订单按渠道分类...
2025-06-04 08:00:04,816 - INFO - 日报生成完成
2025-06-04 08:00:04,816 - INFO - 开始分类并过滤订单数据...
2025-06-04 08:00:04,816 - INFO - 订单分类完成: 有效订单 195 单，其中骑手卡 187 单，大流量卡 8 单
2025-06-04 08:00:04,816 - INFO - 统计各类订单按渠道分类...
2025-06-04 08:00:04,816 - INFO - 开始分类并过滤订单数据...
2025-06-04 08:00:04,817 - INFO - 订单分类完成: 有效订单 152 单，其中骑手卡 152 单，大流量卡 0 单
2025-06-04 08:00:04,817 - INFO - 统计各类订单按渠道分类...
2025-06-04 08:00:04,819 - INFO - 开始生成 2025-06-03 的报表数据...
2025-06-04 08:00:04,819 - INFO - 获取 2025-06-03 的订单数据（下单时间维度）...
2025-06-04 08:00:04,853 - INFO - 成功获取到 195 条去重后的订单数据（下单时间维度）
2025-06-04 08:00:04,854 - INFO - 开始分类并过滤订单数据...
2025-06-04 08:00:04,854 - INFO - 订单分类完成: 有效订单 195 单，其中骑手卡 187 单，流量卡 8 单
2025-06-04 08:00:04,854 - INFO - 计算各渠道的D0下单和D1激活数据...
2025-06-04 08:00:04,886 - INFO - 成功获取到 152 条当日激活订单
2025-06-04 08:00:04,888 - INFO - 样本订单日期检查 (共5个):
2025-06-04 08:00:04,888 - INFO - 订单 20250603131825109666: 下单时间=2025-06-03, 激活时间=2025-06-03, 相差=0天
2025-06-04 08:00:04,888 - INFO - 订单 20250602154226795349: 下单时间=2025-06-02, 激活时间=2025-06-03, 相差=1天
2025-06-04 08:00:04,888 - INFO - 订单 20250525195865933391: 下单时间=2025-05-25, 激活时间=2025-06-03, 相差=9天
2025-06-04 08:00:04,888 - INFO - 订单 20250602061192807072: 下单时间=2025-06-02, 激活时间=2025-06-03, 相差=1天
2025-06-04 08:00:04,888 - INFO - 订单 20250602112439278394: 下单时间=2025-06-02, 激活时间=2025-06-03, 相差=1天
2025-06-04 08:00:04,888 - INFO - 熟龄度处理统计: 总订单=152, 成功处理=152, 处理失败=0
2025-06-04 08:00:04,888 - INFO - 熟龄度分布: 当天=9, 昨天=63, 前天=34, 更早=46
2025-06-04 08:00:04,888 - INFO - 骑手卡熟龄度分布: 当天=9, 昨天=63, 前天=34, 更早=46
2025-06-04 08:00:04,889 - INFO - 流量卡熟龄度分布: 当天=0, 昨天=0, 前天=0, 更早=0
2025-06-04 08:00:04,889 - INFO - 获取报表日期与前一日订单数据对比...
2025-06-04 08:00:04,889 - INFO - 对比日期: 报表日期=2025-06-03, 前一日=2025-06-02
2025-06-04 08:00:04,889 - INFO - 获取 2025-06-03 的订单数据（下单时间维度）...
2025-06-04 08:00:04,923 - INFO - 成功获取到 195 条去重后的订单数据（下单时间维度）
2025-06-04 08:00:04,923 - INFO - 获取 2025-06-02 的订单数据（下单时间维度）...
2025-06-04 08:00:04,955 - INFO - 成功获取到 150 条去重后的订单数据（下单时间维度）
2025-06-04 08:00:04,955 - INFO - 获取周订单趋势数据...
2025-06-04 08:00:04,955 - INFO - 查询日期范围: 2025-05-28 00:00:00 至 2025-06-03 23:59:59
2025-06-04 08:00:04,979 - INFO - 成功获取到 7 天的订单趋势数据
2025-06-04 08:00:04,979 - INFO - 熟龄度数据: {'same_day': 9, 'one_day': 63, 'two_days': 34, 'more_days': 46}
2025-06-04 08:00:04,979 - INFO - 骑手卡熟龄度数据: {'same_day': 9, 'one_day': 63, 'two_days': 34, 'more_days': 46}, 总数: 152
2025-06-04 08:00:04,979 - INFO - 流量卡熟龄度数据: {'same_day': 0, 'one_day': 0, 'two_days': 0, 'more_days': 0}, 总数: 0
2025-06-04 08:00:04,979 - INFO - 当日下单当日激活比例: 5.9%（9/152）
2025-06-04 08:00:04,980 - INFO - 样本订单日期检查 (共5个): 152，熟龄度分布: 当天=9, 昨天=63, 前天=34, 更早=46
2025-06-04 08:00:04,980 - INFO - 环比分析: 当前=195单, 前一日=150单, 变化=30.0%
2025-06-04 08:00:04,980 - INFO - 周趋势数据获取成功，共7天数据
2025-06-04 08:00:05,017 - INFO - 开始发送模板卡片消息到企业微信...
2025-06-04 08:00:05,017 - INFO - 开始发送消息到企业微信（全渠道生产环境）...
2025-06-04 08:00:05,283 - INFO - 企业微信API响应状态码: 200
2025-06-04 08:00:05,283 - INFO - 企业微信消息发送成功
2025-06-04 08:00:05,283 - INFO - 昨天的日报发送成功
2025-06-04 08:00:05,283 - INFO - 昨天的日报处理完成。
2025-06-04 08:00:05,284 - INFO - ==================================================
