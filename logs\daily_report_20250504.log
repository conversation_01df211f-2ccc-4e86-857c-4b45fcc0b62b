2025-05-04 08:00:03,541 - INFO - ==================================================
2025-05-04 08:00:03,541 - INFO - 开始生成日报统计数据...
2025-05-04 08:00:03,575 - INFO - 开始生成 2025-05-03 的报表数据...
2025-05-04 08:00:03,575 - INFO - 获取 2025-05-03 的订单数据（下单时间维度）...
2025-05-04 08:00:03,605 - INFO - 成功获取到 202 条去重后的订单数据（下单时间维度）
2025-05-04 08:00:03,606 - INFO - 开始分类并过滤订单数据...
2025-05-04 08:00:03,606 - INFO - 订单分类完成: 有效订单 202 单，其中骑手卡 173 单，流量卡 29 单
2025-05-04 08:00:03,606 - INFO - 计算各渠道的D0下单和D1激活数据...
2025-05-04 08:00:03,628 - INFO - 成功获取到 180 条当日激活订单
2025-05-04 08:00:03,631 - INFO - 样本订单日期检查 (共5个):
2025-05-04 08:00:03,631 - INFO - 订单 20250502075489805048: 下单时间=2025-05-02, 激活时间=2025-05-03, 相差=1天
2025-05-04 08:00:03,631 - INFO - 订单 20250502173092651765: 下单时间=2025-05-02, 激活时间=2025-05-03, 相差=1天
2025-05-04 08:00:03,631 - INFO - 订单 20250502134920460808: 下单时间=2025-05-02, 激活时间=2025-05-03, 相差=1天
2025-05-04 08:00:03,631 - INFO - 订单 20250423092133115591: 下单时间=2025-04-23, 激活时间=2025-05-03, 相差=10天
2025-05-04 08:00:03,631 - INFO - 订单 20250426093155852260: 下单时间=2025-04-26, 激活时间=2025-05-03, 相差=7天
2025-05-04 08:00:03,632 - INFO - 熟龄度处理统计: 总订单=180, 成功处理=180, 处理失败=0
2025-05-04 08:00:03,632 - INFO - 熟龄度分布: 当天=7, 昨天=55, 前天=45, 更早=73
2025-05-04 08:00:03,632 - INFO - 骑手卡熟龄度分布: 当天=6, 昨天=52, 前天=34, 更早=48
2025-05-04 08:00:03,632 - INFO - 流量卡熟龄度分布: 当天=1, 昨天=3, 前天=11, 更早=25
2025-05-04 08:00:03,632 - INFO - 熟龄度数据: {'same_day': 7, 'one_day': 55, 'two_days': 45, 'more_days': 73}
2025-05-04 08:00:03,632 - INFO - 骑手卡熟龄度数据: {'same_day': 6, 'one_day': 52, 'two_days': 34, 'more_days': 48}, 总数: 140
2025-05-04 08:00:03,632 - INFO - 流量卡熟龄度数据: {'same_day': 1, 'one_day': 3, 'two_days': 11, 'more_days': 25}, 总数: 40
2025-05-04 08:00:03,632 - INFO - 当日下单当日激活比例: 3.9%（7/180）
2025-05-04 08:00:03,633 - INFO - 样本订单日期检查 (共5个): 180，熟龄度分布: 当天=7, 昨天=55, 前天=45, 更早=73
2025-05-04 08:00:03,633 - INFO - 生成HTML报表模板，加载数据: 总活跃=180, 骑手卡=140, 流量卡=40
2025-05-04 08:00:03,633 - INFO - 骑手卡数据: [6, 52, 34, 48]
2025-05-04 08:00:03,633 - INFO - 流量卡数据: [1, 3, 11, 25]
2025-05-04 08:00:03,633 - INFO - HTML报表已生成: reports/report_20250503.html
2025-05-04 08:00:03,633 - INFO - 准备上传文件到FTP服务器: report_20250503.html
2025-05-04 08:00:03,633 - INFO - FTP配置: 主机=211.149.249.234, 端口=21, 目录=reports
2025-05-04 08:00:03,634 - INFO - 本地文件: reports/report_20250503.html, 大小: 33132 字节
2025-05-04 08:00:03,634 - INFO - 正在连接到FTP服务器: 211.149.249.234:21
2025-05-04 08:00:03,709 - INFO - 正在登录FTP服务器: 用户名=reports
2025-05-04 08:00:03,819 - INFO - FTP登录成功
2025-05-04 08:00:03,855 - INFO - 当前FTP目录: /
2025-05-04 08:00:03,855 - INFO - 尝试切换到目录: reports
2025-05-04 08:00:03,927 - INFO - 成功切换到目录: /reports
2025-05-04 08:00:04,066 - INFO - 上传前目录内容: ['report_20250502.html', '..', 'report_20250501.html', '.']
2025-05-04 08:00:04,067 - INFO - 开始上传文件: reports/report_20250503.html -> report_20250503.html
2025-05-04 08:00:04,282 - INFO - FTP上传结果: 226-File successfully transferred
226 0.073 seconds (measured here), 444.34 Kbytes per second
2025-05-04 08:00:04,418 - INFO - 上传后目录内容: ['report_20250503.html', 'report_20250502.html', '..', 'report_20250501.html', '.']
2025-05-04 08:00:04,418 - INFO - 文件已成功上传并验证: report_20250503.html
2025-05-04 08:00:04,418 - INFO - 文件访问URL: https://b.zhoumeiren.cn/reports/report_20250503.html
2025-05-04 08:00:04,454 - INFO - FTP连接已关闭
2025-05-04 08:00:04,454 - INFO - 报表已上传到服务器，URL: https://b.zhoumeiren.cn/reports/report_20250503.html
2025-05-04 08:00:04,454 - INFO - 日报生成完成
2025-05-04 08:00:04,454 - INFO - ==================================================
2025-05-04 08:00:04,611 - INFO - ==================================================
2025-05-04 08:00:04,611 - INFO - 开始生成并发送昨天的日报（全渠道生产环境）...
2025-05-04 08:00:04,611 - INFO - 开始执行昨天的日报发送流程（全渠道生产环境）...
2025-05-04 08:00:04,630 - INFO - 昨天日期: 2025-05-03
2025-05-04 08:00:04,630 - INFO - 开始获取昨天的订单数据（下单时间维度）...
2025-05-04 08:00:04,630 - INFO - 获取 2025-05-03 的订单数据（下单时间维度）...
2025-05-04 08:00:04,655 - INFO - 成功获取到 202 条去重后的订单数据（下单时间维度）
2025-05-04 08:00:04,655 - INFO - 开始获取昨天的订单数据（激活时间维度）...
2025-05-04 08:00:04,655 - INFO - 获取 2025-05-03 的订单数据（激活时间维度）...
2025-05-04 08:00:04,677 - INFO - 成功获取到 180 条去重后的订单数据（激活时间维度）
2025-05-04 08:00:04,677 - INFO - 成功获取到订单数据，继续生成日报
2025-05-04 08:00:04,677 - INFO - 开始生成昨天的日报...
2025-05-04 08:00:04,677 - INFO - 开始生成日报...
2025-05-04 08:00:04,677 - INFO - 开始分类并过滤订单数据...
2025-05-04 08:00:04,678 - INFO - 订单分类完成: 有效订单 202 单，其中骑手卡 173 单，大流量卡 29 单
2025-05-04 08:00:04,678 - INFO - 统计各类订单按渠道分类...
2025-05-04 08:00:04,678 - INFO - 开始分类并过滤订单数据...
2025-05-04 08:00:04,678 - INFO - 订单分类完成: 有效订单 180 单，其中骑手卡 140 单，大流量卡 40 单
2025-05-04 08:00:04,678 - INFO - 统计各类订单按渠道分类...
2025-05-04 08:00:04,678 - INFO - 日报生成完成
2025-05-04 08:00:04,678 - INFO - 开始分类并过滤订单数据...
2025-05-04 08:00:04,679 - INFO - 订单分类完成: 有效订单 202 单，其中骑手卡 173 单，大流量卡 29 单
2025-05-04 08:00:04,679 - INFO - 统计各类订单按渠道分类...
2025-05-04 08:00:04,679 - INFO - 开始分类并过滤订单数据...
2025-05-04 08:00:04,679 - INFO - 订单分类完成: 有效订单 180 单，其中骑手卡 140 单，大流量卡 40 单
2025-05-04 08:00:04,679 - INFO - 统计各类订单按渠道分类...
2025-05-04 08:00:04,681 - INFO - 开始生成 2025-05-03 的报表数据...
2025-05-04 08:00:04,681 - INFO - 获取 2025-05-03 的订单数据（下单时间维度）...
2025-05-04 08:00:04,704 - INFO - 成功获取到 202 条去重后的订单数据（下单时间维度）
2025-05-04 08:00:04,705 - INFO - 开始分类并过滤订单数据...
2025-05-04 08:00:04,705 - INFO - 订单分类完成: 有效订单 202 单，其中骑手卡 173 单，流量卡 29 单
2025-05-04 08:00:04,705 - INFO - 计算各渠道的D0下单和D1激活数据...
2025-05-04 08:00:04,726 - INFO - 成功获取到 180 条当日激活订单
2025-05-04 08:00:04,728 - INFO - 样本订单日期检查 (共5个):
2025-05-04 08:00:04,728 - INFO - 订单 20250502075489805048: 下单时间=2025-05-02, 激活时间=2025-05-03, 相差=1天
2025-05-04 08:00:04,728 - INFO - 订单 20250502173092651765: 下单时间=2025-05-02, 激活时间=2025-05-03, 相差=1天
2025-05-04 08:00:04,728 - INFO - 订单 20250502134920460808: 下单时间=2025-05-02, 激活时间=2025-05-03, 相差=1天
2025-05-04 08:00:04,728 - INFO - 订单 20250423092133115591: 下单时间=2025-04-23, 激活时间=2025-05-03, 相差=10天
2025-05-04 08:00:04,728 - INFO - 订单 20250426093155852260: 下单时间=2025-04-26, 激活时间=2025-05-03, 相差=7天
2025-05-04 08:00:04,728 - INFO - 熟龄度处理统计: 总订单=180, 成功处理=180, 处理失败=0
2025-05-04 08:00:04,728 - INFO - 熟龄度分布: 当天=7, 昨天=55, 前天=45, 更早=73
2025-05-04 08:00:04,729 - INFO - 骑手卡熟龄度分布: 当天=6, 昨天=52, 前天=34, 更早=48
2025-05-04 08:00:04,729 - INFO - 流量卡熟龄度分布: 当天=1, 昨天=3, 前天=11, 更早=25
2025-05-04 08:00:04,729 - INFO - 熟龄度数据: {'same_day': 7, 'one_day': 55, 'two_days': 45, 'more_days': 73}
2025-05-04 08:00:04,729 - INFO - 骑手卡熟龄度数据: {'same_day': 6, 'one_day': 52, 'two_days': 34, 'more_days': 48}, 总数: 140
2025-05-04 08:00:04,729 - INFO - 流量卡熟龄度数据: {'same_day': 1, 'one_day': 3, 'two_days': 11, 'more_days': 25}, 总数: 40
2025-05-04 08:00:04,729 - INFO - 当日下单当日激活比例: 3.9%（7/180）
2025-05-04 08:00:04,729 - INFO - 样本订单日期检查 (共5个): 180，熟龄度分布: 当天=7, 昨天=55, 前天=45, 更早=73
2025-05-04 08:00:04,729 - INFO - 开始发送模板卡片消息到企业微信...
2025-05-04 08:00:04,729 - INFO - 开始发送消息到企业微信（全渠道生产环境）...
2025-05-04 08:00:04,983 - INFO - 企业微信API响应状态码: 200
2025-05-04 08:00:04,983 - INFO - 企业微信消息发送成功
2025-05-04 08:00:04,983 - INFO - 昨天的日报发送成功
2025-05-04 08:00:04,984 - INFO - 昨天的日报处理完成。
2025-05-04 08:00:04,984 - INFO - ==================================================
