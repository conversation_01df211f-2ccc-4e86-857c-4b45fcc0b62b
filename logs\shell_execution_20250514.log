[2025-05-14 08:00:02] 开始执行日报脚本
[2025-05-14 08:00:02] 工作目录: /volume3/SSD-soft/fzsrb
[2025-05-14 08:00:02] 检查Python依赖...
WARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager, possibly rendering your system unusable. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv. Use the --root-user-action option if you know what you are doing and want to suppress this warning.
[2025-05-14 08:00:02] 开始执行简单日报脚本...
2025-05-14 08:00:03,650 - INFO - ==================================================
2025-05-14 08:00:03,650 - INFO - 开始生成日报统计数据...
2025-05-14 08:00:03,692 - INFO - 开始生成 2025-05-13 的报表数据...
2025-05-14 08:00:03,693 - INFO - 获取 2025-05-13 的订单数据（下单时间维度）...
2025-05-14 08:00:03,733 - INFO - 成功获取到 280 条去重后的订单数据（下单时间维度）
2025-05-14 08:00:03,733 - INFO - 开始分类并过滤订单数据...
2025-05-14 08:00:03,733 - INFO - 订单分类完成: 有效订单 280 单，其中骑手卡 263 单，流量卡 17 单
2025-05-14 08:00:03,733 - INFO - 计算各渠道的D0下单和D1激活数据...
2025-05-14 08:00:03,760 - INFO - 成功获取到 207 条当日激活订单
2025-05-14 08:00:03,772 - INFO - 样本订单日期检查 (共5个):
2025-05-14 08:00:03,772 - INFO - 订单 20250507225715329778: 下单时间=2025-05-07, 激活时间=2025-05-13, 相差=6天
2025-05-14 08:00:03,772 - INFO - 订单 20250509232078495684: 下单时间=2025-05-09, 激活时间=2025-05-13, 相差=4天
2025-05-14 08:00:03,772 - INFO - 订单 20250513081395926816: 下单时间=2025-05-13, 激活时间=2025-05-13, 相差=0天
2025-05-14 08:00:03,772 - INFO - 订单 20250512195759945687: 下单时间=2025-05-12, 激活时间=2025-05-13, 相差=1天
2025-05-14 08:00:03,772 - INFO - 订单 20250512110617434031: 下单时间=2025-05-12, 激活时间=2025-05-13, 相差=1天
2025-05-14 08:00:03,772 - INFO - 熟龄度处理统计: 总订单=207, 成功处理=207, 处理失败=0
2025-05-14 08:00:03,772 - INFO - 熟龄度分布: 当天=14, 昨天=105, 前天=44, 更早=44
2025-05-14 08:00:03,772 - INFO - 骑手卡熟龄度分布: 当天=14, 昨天=96, 前天=41, 更早=42
2025-05-14 08:00:03,772 - INFO - 流量卡熟龄度分布: 当天=0, 昨天=9, 前天=3, 更早=2
2025-05-14 08:00:03,773 - INFO - 获取报表日期与前一日订单数据对比...
2025-05-14 08:00:03,773 - INFO - 对比日期: 报表日期=2025-05-13, 前一日=2025-05-12
2025-05-14 08:00:03,773 - INFO - 获取 2025-05-13 的订单数据（下单时间维度）...
2025-05-14 08:00:03,805 - INFO - 成功获取到 280 条去重后的订单数据（下单时间维度）
2025-05-14 08:00:03,805 - INFO - 获取 2025-05-12 的订单数据（下单时间维度）...
2025-05-14 08:00:03,837 - INFO - 成功获取到 283 条去重后的订单数据（下单时间维度）
2025-05-14 08:00:03,838 - INFO - 获取周订单趋势数据...
2025-05-14 08:00:03,838 - INFO - 查询日期范围: 2025-05-07 00:00:00 至 2025-05-13 23:59:59
2025-05-14 08:00:03,864 - INFO - 成功获取到 7 天的订单趋势数据
2025-05-14 08:00:03,864 - INFO - 熟龄度数据: {'same_day': 14, 'one_day': 105, 'two_days': 44, 'more_days': 44}
2025-05-14 08:00:03,864 - INFO - 骑手卡熟龄度数据: {'same_day': 14, 'one_day': 96, 'two_days': 41, 'more_days': 42}, 总数: 193
2025-05-14 08:00:03,864 - INFO - 流量卡熟龄度数据: {'same_day': 0, 'one_day': 9, 'two_days': 3, 'more_days': 2}, 总数: 14
2025-05-14 08:00:03,864 - INFO - 当日下单当日激活比例: 6.8%（14/207）
2025-05-14 08:00:03,864 - INFO - 样本订单日期检查 (共5个): 207，熟龄度分布: 当天=14, 昨天=105, 前天=44, 更早=44
2025-05-14 08:00:03,864 - INFO - 环比分析: 当前=280单, 前一日=283单, 变化=-1.1%
2025-05-14 08:00:03,864 - INFO - 周趋势数据获取成功，共7天数据
2025-05-14 08:00:03,865 - INFO - 生成HTML报表模板，加载数据: 总活跃=207, 骑手卡=193, 流量卡=14
2025-05-14 08:00:03,865 - INFO - 骑手卡数据: [14, 96, 41, 42]
2025-05-14 08:00:03,865 - INFO - 流量卡数据: [0, 9, 3, 2]
2025-05-14 08:00:03,865 - INFO - HTML报表已生成: reports/report_20250513.html
2025-05-14 08:00:03,865 - INFO - 准备上传文件到FTP服务器: report_20250513.html
2025-05-14 08:00:03,865 - INFO - FTP配置: 主机=211.149.249.234, 端口=21, 目录=reports
2025-05-14 08:00:03,865 - INFO - 本地文件: reports/report_20250513.html, 大小: 50797 字节
2025-05-14 08:00:03,866 - INFO - 正在连接到FTP服务器: 211.149.249.234:21
2025-05-14 08:00:03,937 - INFO - 正在登录FTP服务器: 用户名=reports
2025-05-14 08:00:04,034 - INFO - FTP登录成功
2025-05-14 08:00:04,070 - INFO - 当前FTP目录: /
2025-05-14 08:00:04,070 - INFO - 尝试切换到目录: reports
2025-05-14 08:00:04,140 - INFO - 成功切换到目录: /reports
2025-05-14 08:00:04,279 - INFO - 上传前目录内容: ['report_20250506.html', 'report_20250511.html', 'report_20250512.html', 'report_20250503.html', 'report_20250508.html', 'report_20250502.html', '..', 'report_20250509.html', 'report_20250510.html', 'report_20250501.html', 'report_20250505.html', 'report_20250504.html', 'report_20250507.html', '.']
2025-05-14 08:00:04,279 - INFO - 开始上传文件: reports/report_20250513.html -> report_20250513.html
2025-05-14 08:00:04,518 - INFO - FTP上传结果: 226-File successfully transferred
226 0.101 seconds (measured here), 490.18 Kbytes per second
2025-05-14 08:00:04,660 - INFO - 上传后目录内容: ['report_20250506.html', 'report_20250511.html', 'report_20250512.html', 'report_20250503.html', 'report_20250508.html', 'report_20250502.html', '..', 'report_20250513.html', 'report_20250509.html', 'report_20250510.html', 'report_20250501.html', 'report_20250505.html', 'report_20250504.html', 'report_20250507.html', '.']
2025-05-14 08:00:04,660 - INFO - 文件已成功上传并验证: report_20250513.html
2025-05-14 08:00:04,660 - INFO - 文件访问URL: https://rep.331126.com/reports/report_20250513.html
2025-05-14 08:00:04,695 - INFO - FTP连接已关闭
2025-05-14 08:00:04,695 - INFO - 报表已上传到服务器，URL: https://rep.331126.com/reports/report_20250513.html
2025-05-14 08:00:04,695 - INFO - 日报生成完成
2025-05-14 08:00:04,695 - INFO - ==================================================
*get* '220---------- Welcome to Pure-FTPd [privsep] [TLS] ----------\n'
*get* '220-You are user number 1 of 50 allowed.\n'
*get* '220-Local time is now 08:00. Server port: 21.\n'
*get* '220-This is a private system - No anonymous login\n'
*get* '220-IPv6 connections are also welcome on this server.\n'
*get* '220 You will be disconnected after 15 minutes of inactivity.\n'
*resp* '220---------- Welcome to Pure-FTPd [privsep] [TLS] ----------\n220-You are user number 1 of 50 allowed.\n220-Local time is now 08:00. Server port: 21.\n220-This is a private system - No anonymous login\n220-IPv6 connections are also welcome on this server.\n220 You will be disconnected after 15 minutes of inactivity.'
*cmd* 'USER reports'
*put* 'USER reports\r\n'
*get* '331 User reports OK. Password required\n'
*resp* '331 User reports OK. Password required'
*cmd* 'PASS ****************'
*put* 'PASS ****************\r\n'
*get* '230 OK. Current directory is /\n'
*resp* '230 OK. Current directory is /'
*cmd* 'PWD'
*put* 'PWD\r\n'
*get* '257 "/" is your current location\n'
*resp* '257 "/" is your current location'
*cmd* 'CWD reports'
*put* 'CWD reports\r\n'
*get* '250 OK. Current directory is /reports\n'
*resp* '250 OK. Current directory is /reports'
*cmd* 'PWD'
*put* 'PWD\r\n'
*get* '257 "/reports" is your current location\n'
*resp* '257 "/reports" is your current location'
*cmd* 'TYPE A'
*put* 'TYPE A\r\n'
*get* '200 TYPE is now ASCII\n'
*resp* '200 TYPE is now ASCII'
*cmd* 'PASV'
*put* 'PASV\r\n'
*get* '227 Entering Passive Mode (211,149,249,234,153,170)\n'
*resp* '227 Entering Passive Mode (211,149,249,234,153,170)'
*cmd* 'NLST'
*put* 'NLST\r\n'
*get* '150 Accepted data connection\n'
*resp* '150 Accepted data connection'
*get* '226 14 matches total\n'
*resp* '226 14 matches total'
*cmd* 'TYPE I'
*put* 'TYPE I\r\n'
*get* '200 TYPE is now 8-bit binary\n'
*resp* '200 TYPE is now 8-bit binary'
*cmd* 'PASV'
*put* 'PASV\r\n'
*get* '227 Entering Passive Mode (211,149,249,234,152,203)\n'
*resp* '227 Entering Passive Mode (211,149,249,234,152,203)'
*cmd* 'STOR report_20250513.html'
*put* 'STOR report_20250513.html\r\n'
*get* '150 Accepted data connection\n'
*resp* '150 Accepted data connection'
*get* '226-File successfully transferred\n'
*get* '226 0.101 seconds (measured here), 490.18 Kbytes per second\n'
*resp* '226-File successfully transferred\n226 0.101 seconds (measured here), 490.18 Kbytes per second'
*cmd* 'TYPE A'
*put* 'TYPE A\r\n'
*get* '200 TYPE is now ASCII\n'
*resp* '200 TYPE is now ASCII'
*cmd* 'PASV'
*put* 'PASV\r\n'
*get* '227 Entering Passive Mode (211,149,249,234,153,120)\n'
*resp* '227 Entering Passive Mode (211,149,249,234,153,120)'
*cmd* 'NLST'
*put* 'NLST\r\n'
*get* '150 Accepted data connection\n'
*resp* '150 Accepted data connection'
*get* '226 15 matches total\n'
*resp* '226 15 matches total'
*cmd* 'QUIT'
*put* 'QUIT\r\n'
*get* '221-Goodbye. You uploaded 50 and downloaded 0 kbytes.\n'
*get* '221 Logout.\n'
*resp* '221-Goodbye. You uploaded 50 and downloaded 0 kbytes.\n221 Logout.'
[2025-05-14 08:00:02] 简单日报脚本执行成功
[2025-05-14 08:00:02] 开始执行全渠道日报脚本...
2025-05-14 08:00:04,879 - INFO - ==================================================
2025-05-14 08:00:04,879 - INFO - 开始生成并发送昨天的日报（全渠道生产环境）...
2025-05-14 08:00:04,879 - INFO - 开始执行昨天的日报发送流程（全渠道生产环境）...
2025-05-14 08:00:04,896 - INFO - 昨天日期: 2025-05-13
2025-05-14 08:00:04,896 - INFO - 开始获取昨天的订单数据（下单时间维度）...
2025-05-14 08:00:04,896 - INFO - 获取 2025-05-13 的订单数据（下单时间维度）...
2025-05-14 08:00:04,929 - INFO - 成功获取到 280 条去重后的订单数据（下单时间维度）
2025-05-14 08:00:04,930 - INFO - 开始获取昨天的订单数据（激活时间维度）...
2025-05-14 08:00:04,930 - INFO - 获取 2025-05-13 的订单数据（激活时间维度）...
2025-05-14 08:00:04,958 - INFO - 成功获取到 207 条去重后的订单数据（激活时间维度）
2025-05-14 08:00:04,958 - INFO - 成功获取到订单数据，继续生成日报
2025-05-14 08:00:04,958 - INFO - 开始生成昨天的日报...
2025-05-14 08:00:04,958 - INFO - 开始生成日报...
2025-05-14 08:00:04,958 - INFO - 开始分类并过滤订单数据...
2025-05-14 08:00:04,959 - INFO - 订单分类完成: 有效订单 280 单，其中骑手卡 263 单，大流量卡 17 单
2025-05-14 08:00:04,959 - INFO - 统计各类订单按渠道分类...
2025-05-14 08:00:04,959 - INFO - 开始分类并过滤订单数据...
2025-05-14 08:00:04,959 - INFO - 订单分类完成: 有效订单 207 单，其中骑手卡 193 单，大流量卡 14 单
2025-05-14 08:00:04,959 - INFO - 统计各类订单按渠道分类...
2025-05-14 08:00:04,960 - INFO - 日报生成完成
2025-05-14 08:00:04,960 - INFO - 开始分类并过滤订单数据...
2025-05-14 08:00:04,960 - INFO - 订单分类完成: 有效订单 280 单，其中骑手卡 263 单，大流量卡 17 单
2025-05-14 08:00:04,960 - INFO - 统计各类订单按渠道分类...
2025-05-14 08:00:04,960 - INFO - 开始分类并过滤订单数据...
2025-05-14 08:00:04,960 - INFO - 订单分类完成: 有效订单 207 单，其中骑手卡 193 单，大流量卡 14 单
2025-05-14 08:00:04,960 - INFO - 统计各类订单按渠道分类...
2025-05-14 08:00:04,962 - INFO - 开始生成 2025-05-13 的报表数据...
2025-05-14 08:00:04,962 - INFO - 获取 2025-05-13 的订单数据（下单时间维度）...
2025-05-14 08:00:04,993 - INFO - 成功获取到 280 条去重后的订单数据（下单时间维度）
2025-05-14 08:00:04,994 - INFO - 开始分类并过滤订单数据...
2025-05-14 08:00:04,994 - INFO - 订单分类完成: 有效订单 280 单，其中骑手卡 263 单，流量卡 17 单
2025-05-14 08:00:04,994 - INFO - 计算各渠道的D0下单和D1激活数据...
2025-05-14 08:00:05,020 - INFO - 成功获取到 207 条当日激活订单
2025-05-14 08:00:05,022 - INFO - 样本订单日期检查 (共5个):
2025-05-14 08:00:05,022 - INFO - 订单 20250507225715329778: 下单时间=2025-05-07, 激活时间=2025-05-13, 相差=6天
2025-05-14 08:00:05,023 - INFO - 订单 20250509232078495684: 下单时间=2025-05-09, 激活时间=2025-05-13, 相差=4天
2025-05-14 08:00:05,023 - INFO - 订单 20250513081395926816: 下单时间=2025-05-13, 激活时间=2025-05-13, 相差=0天
2025-05-14 08:00:05,023 - INFO - 订单 20250512195759945687: 下单时间=2025-05-12, 激活时间=2025-05-13, 相差=1天
2025-05-14 08:00:05,023 - INFO - 订单 20250512110617434031: 下单时间=2025-05-12, 激活时间=2025-05-13, 相差=1天
2025-05-14 08:00:05,023 - INFO - 熟龄度处理统计: 总订单=207, 成功处理=207, 处理失败=0
2025-05-14 08:00:05,023 - INFO - 熟龄度分布: 当天=14, 昨天=105, 前天=44, 更早=44
2025-05-14 08:00:05,023 - INFO - 骑手卡熟龄度分布: 当天=14, 昨天=96, 前天=41, 更早=42
2025-05-14 08:00:05,023 - INFO - 流量卡熟龄度分布: 当天=0, 昨天=9, 前天=3, 更早=2
2025-05-14 08:00:05,024 - INFO - 获取报表日期与前一日订单数据对比...
2025-05-14 08:00:05,024 - INFO - 对比日期: 报表日期=2025-05-13, 前一日=2025-05-12
2025-05-14 08:00:05,024 - INFO - 获取 2025-05-13 的订单数据（下单时间维度）...
2025-05-14 08:00:05,056 - INFO - 成功获取到 280 条去重后的订单数据（下单时间维度）
2025-05-14 08:00:05,056 - INFO - 获取 2025-05-12 的订单数据（下单时间维度）...
2025-05-14 08:00:05,088 - INFO - 成功获取到 283 条去重后的订单数据（下单时间维度）
2025-05-14 08:00:05,088 - INFO - 获取周订单趋势数据...
2025-05-14 08:00:05,088 - INFO - 查询日期范围: 2025-05-07 00:00:00 至 2025-05-13 23:59:59
2025-05-14 08:00:05,111 - INFO - 成功获取到 7 天的订单趋势数据
2025-05-14 08:00:05,111 - INFO - 熟龄度数据: {'same_day': 14, 'one_day': 105, 'two_days': 44, 'more_days': 44}
2025-05-14 08:00:05,111 - INFO - 骑手卡熟龄度数据: {'same_day': 14, 'one_day': 96, 'two_days': 41, 'more_days': 42}, 总数: 193
2025-05-14 08:00:05,111 - INFO - 流量卡熟龄度数据: {'same_day': 0, 'one_day': 9, 'two_days': 3, 'more_days': 2}, 总数: 14
2025-05-14 08:00:05,112 - INFO - 当日下单当日激活比例: 6.8%（14/207）
2025-05-14 08:00:05,112 - INFO - 样本订单日期检查 (共5个): 207，熟龄度分布: 当天=14, 昨天=105, 前天=44, 更早=44
2025-05-14 08:00:05,112 - INFO - 环比分析: 当前=280单, 前一日=283单, 变化=-1.1%
2025-05-14 08:00:05,112 - INFO - 周趋势数据获取成功，共7天数据
2025-05-14 08:00:05,112 - INFO - 开始发送模板卡片消息到企业微信...
2025-05-14 08:00:05,112 - INFO - 开始发送消息到企业微信（全渠道生产环境）...
2025-05-14 08:00:05,420 - INFO - 企业微信API响应状态码: 200
2025-05-14 08:00:05,421 - INFO - 企业微信消息发送成功
2025-05-14 08:00:05,421 - INFO - 昨天的日报发送成功
2025-05-14 08:00:05,421 - INFO - 昨天的日报处理完成。
2025-05-14 08:00:05,421 - INFO - ==================================================
[2025-05-14 08:00:02] 全渠道日报脚本执行成功
[2025-05-14 08:00:02] 开始执行OPPO渠道日报脚本...
2025-05-14 08:00:05,594 - INFO - ==================================================
2025-05-14 08:00:05,595 - INFO - 开始生成并发送OPPO和荣耀渠道日报（OPPO生产环境）...
2025-05-14 08:00:05,595 - INFO - 开始执行OPPO和荣耀渠道日报发送流程（OPPO生产环境）...
2025-05-14 08:00:05,612 - INFO - 查询日期: 2025-05-13
2025-05-14 08:00:05,612 - INFO - 获取 2025-05-13 的OPPO和荣耀渠道订单数据（下单时间维度）...
2025-05-14 08:00:05,630 - INFO - 查询到 12 条去重后的订单数据
2025-05-14 08:00:05,631 - INFO - 开始生成OPPO和荣耀渠道Markdown格式日报...
2025-05-14 08:00:05,631 - INFO - 开始处理订单数据，总数据条数: 12
2025-05-14 08:00:05,631 - INFO - 统计结果: 总订单 12
2025-05-14 08:00:05,631 - INFO -   OPPO: 11单
2025-05-14 08:00:05,631 - INFO -   荣耀: 1单
2025-05-14 08:00:05,631 - INFO - Markdown格式日报生成完成
2025-05-14 08:00:05,631 - INFO - 开始发送消息到企业微信（OPPO生产环境）...
2025-05-14 08:00:05,898 - INFO - 企业微信API响应状态码: 200
2025-05-14 08:00:05,898 - INFO - 企业微信消息发送成功
2025-05-14 08:00:05,898 - INFO - OPPO和荣耀渠道日报发送成功
2025-05-14 08:00:05,898 - INFO - OPPO和荣耀渠道日报处理完成。
2025-05-14 08:00:05,899 - INFO - ==================================================
[2025-05-14 08:00:02] OPPO渠道日报脚本执行成功
[2025-05-14 08:00:05] 所有日报脚本执行完成
----------------------------------------
