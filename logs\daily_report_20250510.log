2025-05-10 08:00:03,082 - INFO - ==================================================
2025-05-10 08:00:03,082 - INFO - 开始生成日报统计数据...
2025-05-10 08:00:03,125 - INFO - 开始生成 2025-05-09 的报表数据...
2025-05-10 08:00:03,125 - INFO - 获取 2025-05-09 的订单数据（下单时间维度）...
2025-05-10 08:00:03,156 - INFO - 成功获取到 198 条去重后的订单数据（下单时间维度）
2025-05-10 08:00:03,156 - INFO - 开始分类并过滤订单数据...
2025-05-10 08:00:03,156 - INFO - 订单分类完成: 有效订单 198 单，其中骑手卡 172 单，流量卡 26 单
2025-05-10 08:00:03,156 - INFO - 计算各渠道的D0下单和D1激活数据...
2025-05-10 08:00:03,184 - INFO - 成功获取到 206 条当日激活订单
2025-05-10 08:00:03,188 - INFO - 样本订单日期检查 (共5个):
2025-05-10 08:00:03,188 - INFO - 订单 20250508212364134151: 下单时间=2025-05-08, 激活时间=2025-05-09, 相差=1天
2025-05-10 08:00:03,188 - INFO - 订单 20250507165919013730: 下单时间=2025-05-07, 激活时间=2025-05-09, 相差=2天
2025-05-10 08:00:03,188 - INFO - 订单 20250508131532304716: 下单时间=2025-05-08, 激活时间=2025-05-09, 相差=1天
2025-05-10 08:00:03,188 - INFO - 订单 20250508230676538280: 下单时间=2025-05-08, 激活时间=2025-05-09, 相差=1天
2025-05-10 08:00:03,188 - INFO - 订单 20250508221827232193: 下单时间=2025-05-08, 激活时间=2025-05-09, 相差=1天
2025-05-10 08:00:03,188 - INFO - 熟龄度处理统计: 总订单=206, 成功处理=206, 处理失败=0
2025-05-10 08:00:03,189 - INFO - 熟龄度分布: 当天=9, 昨天=99, 前天=54, 更早=44
2025-05-10 08:00:03,189 - INFO - 骑手卡熟龄度分布: 当天=8, 昨天=97, 前天=52, 更早=34
2025-05-10 08:00:03,189 - INFO - 流量卡熟龄度分布: 当天=1, 昨天=2, 前天=2, 更早=10
2025-05-10 08:00:03,189 - INFO - 熟龄度数据: {'same_day': 9, 'one_day': 99, 'two_days': 54, 'more_days': 44}
2025-05-10 08:00:03,189 - INFO - 骑手卡熟龄度数据: {'same_day': 8, 'one_day': 97, 'two_days': 52, 'more_days': 34}, 总数: 191
2025-05-10 08:00:03,189 - INFO - 流量卡熟龄度数据: {'same_day': 1, 'one_day': 2, 'two_days': 2, 'more_days': 10}, 总数: 15
2025-05-10 08:00:03,189 - INFO - 当日下单当日激活比例: 4.4%（9/206）
2025-05-10 08:00:03,189 - INFO - 样本订单日期检查 (共5个): 206，熟龄度分布: 当天=9, 昨天=99, 前天=54, 更早=44
2025-05-10 08:00:03,190 - INFO - 生成HTML报表模板，加载数据: 总活跃=206, 骑手卡=191, 流量卡=15
2025-05-10 08:00:03,190 - INFO - 骑手卡数据: [8, 97, 52, 34]
2025-05-10 08:00:03,190 - INFO - 流量卡数据: [1, 2, 2, 10]
2025-05-10 08:00:03,190 - INFO - HTML报表已生成: reports/report_20250509.html
2025-05-10 08:00:03,190 - INFO - 准备上传文件到FTP服务器: report_20250509.html
2025-05-10 08:00:03,190 - INFO - FTP配置: 主机=211.149.249.234, 端口=21, 目录=reports
2025-05-10 08:00:03,190 - INFO - 本地文件: reports/report_20250509.html, 大小: 33274 字节
2025-05-10 08:00:03,190 - INFO - 正在连接到FTP服务器: 211.149.249.234:21
2025-05-10 08:00:03,269 - INFO - 正在登录FTP服务器: 用户名=reports
2025-05-10 08:00:03,377 - INFO - FTP登录成功
2025-05-10 08:00:03,414 - INFO - 当前FTP目录: /
2025-05-10 08:00:03,414 - INFO - 尝试切换到目录: reports
2025-05-10 08:00:03,489 - INFO - 成功切换到目录: /reports
2025-05-10 08:00:03,640 - INFO - 上传前目录内容: ['report_20250506.html', 'report_20250503.html', 'report_20250508.html', 'report_20250502.html', '..', 'report_20250501.html', 'report_20250505.html', 'report_20250504.html', 'report_20250507.html', '.']
2025-05-10 08:00:03,640 - INFO - 开始上传文件: reports/report_20250509.html -> report_20250509.html
2025-05-10 08:00:03,858 - INFO - FTP上传结果: 226-File successfully transferred
226 0.070 seconds (measured here), 465.73 Kbytes per second
2025-05-10 08:00:04,007 - INFO - 上传后目录内容: ['report_20250506.html', 'report_20250503.html', 'report_20250508.html', 'report_20250502.html', '..', 'report_20250509.html', 'report_20250501.html', 'report_20250505.html', 'report_20250504.html', 'report_20250507.html', '.']
2025-05-10 08:00:04,007 - INFO - 文件已成功上传并验证: report_20250509.html
2025-05-10 08:00:04,007 - INFO - 文件访问URL: https://rep.331126.com/reports/report_20250509.html
2025-05-10 08:00:04,044 - INFO - FTP连接已关闭
2025-05-10 08:00:04,044 - INFO - 报表已上传到服务器，URL: https://rep.331126.com/reports/report_20250509.html
2025-05-10 08:00:04,044 - INFO - 日报生成完成
2025-05-10 08:00:04,044 - INFO - ==================================================
2025-05-10 08:00:04,202 - INFO - ==================================================
2025-05-10 08:00:04,202 - INFO - 开始生成并发送昨天的日报（全渠道生产环境）...
2025-05-10 08:00:04,202 - INFO - 开始执行昨天的日报发送流程（全渠道生产环境）...
2025-05-10 08:00:04,219 - INFO - 昨天日期: 2025-05-09
2025-05-10 08:00:04,219 - INFO - 开始获取昨天的订单数据（下单时间维度）...
2025-05-10 08:00:04,219 - INFO - 获取 2025-05-09 的订单数据（下单时间维度）...
2025-05-10 08:00:04,246 - INFO - 成功获取到 198 条去重后的订单数据（下单时间维度）
2025-05-10 08:00:04,246 - INFO - 开始获取昨天的订单数据（激活时间维度）...
2025-05-10 08:00:04,246 - INFO - 获取 2025-05-09 的订单数据（激活时间维度）...
2025-05-10 08:00:04,273 - INFO - 成功获取到 206 条去重后的订单数据（激活时间维度）
2025-05-10 08:00:04,273 - INFO - 成功获取到订单数据，继续生成日报
2025-05-10 08:00:04,273 - INFO - 开始生成昨天的日报...
2025-05-10 08:00:04,274 - INFO - 开始生成日报...
2025-05-10 08:00:04,274 - INFO - 开始分类并过滤订单数据...
2025-05-10 08:00:04,274 - INFO - 订单分类完成: 有效订单 198 单，其中骑手卡 172 单，大流量卡 26 单
2025-05-10 08:00:04,274 - INFO - 统计各类订单按渠道分类...
2025-05-10 08:00:04,274 - INFO - 开始分类并过滤订单数据...
2025-05-10 08:00:04,274 - INFO - 订单分类完成: 有效订单 206 单，其中骑手卡 191 单，大流量卡 15 单
2025-05-10 08:00:04,274 - INFO - 统计各类订单按渠道分类...
2025-05-10 08:00:04,275 - INFO - 日报生成完成
2025-05-10 08:00:04,275 - INFO - 开始分类并过滤订单数据...
2025-05-10 08:00:04,275 - INFO - 订单分类完成: 有效订单 198 单，其中骑手卡 172 单，大流量卡 26 单
2025-05-10 08:00:04,275 - INFO - 统计各类订单按渠道分类...
2025-05-10 08:00:04,275 - INFO - 开始分类并过滤订单数据...
2025-05-10 08:00:04,275 - INFO - 订单分类完成: 有效订单 206 单，其中骑手卡 191 单，大流量卡 15 单
2025-05-10 08:00:04,275 - INFO - 统计各类订单按渠道分类...
2025-05-10 08:00:04,277 - INFO - 开始生成 2025-05-09 的报表数据...
2025-05-10 08:00:04,277 - INFO - 获取 2025-05-09 的订单数据（下单时间维度）...
2025-05-10 08:00:04,302 - INFO - 成功获取到 198 条去重后的订单数据（下单时间维度）
2025-05-10 08:00:04,303 - INFO - 开始分类并过滤订单数据...
2025-05-10 08:00:04,303 - INFO - 订单分类完成: 有效订单 198 单，其中骑手卡 172 单，流量卡 26 单
2025-05-10 08:00:04,303 - INFO - 计算各渠道的D0下单和D1激活数据...
2025-05-10 08:00:04,328 - INFO - 成功获取到 206 条当日激活订单
2025-05-10 08:00:04,330 - INFO - 样本订单日期检查 (共5个):
2025-05-10 08:00:04,330 - INFO - 订单 20250508212364134151: 下单时间=2025-05-08, 激活时间=2025-05-09, 相差=1天
2025-05-10 08:00:04,330 - INFO - 订单 20250507165919013730: 下单时间=2025-05-07, 激活时间=2025-05-09, 相差=2天
2025-05-10 08:00:04,330 - INFO - 订单 20250508131532304716: 下单时间=2025-05-08, 激活时间=2025-05-09, 相差=1天
2025-05-10 08:00:04,330 - INFO - 订单 20250508230676538280: 下单时间=2025-05-08, 激活时间=2025-05-09, 相差=1天
2025-05-10 08:00:04,330 - INFO - 订单 20250508221827232193: 下单时间=2025-05-08, 激活时间=2025-05-09, 相差=1天
2025-05-10 08:00:04,331 - INFO - 熟龄度处理统计: 总订单=206, 成功处理=206, 处理失败=0
2025-05-10 08:00:04,331 - INFO - 熟龄度分布: 当天=9, 昨天=99, 前天=54, 更早=44
2025-05-10 08:00:04,331 - INFO - 骑手卡熟龄度分布: 当天=8, 昨天=97, 前天=52, 更早=34
2025-05-10 08:00:04,331 - INFO - 流量卡熟龄度分布: 当天=1, 昨天=2, 前天=2, 更早=10
2025-05-10 08:00:04,331 - INFO - 熟龄度数据: {'same_day': 9, 'one_day': 99, 'two_days': 54, 'more_days': 44}
2025-05-10 08:00:04,331 - INFO - 骑手卡熟龄度数据: {'same_day': 8, 'one_day': 97, 'two_days': 52, 'more_days': 34}, 总数: 191
2025-05-10 08:00:04,331 - INFO - 流量卡熟龄度数据: {'same_day': 1, 'one_day': 2, 'two_days': 2, 'more_days': 10}, 总数: 15
2025-05-10 08:00:04,331 - INFO - 当日下单当日激活比例: 4.4%（9/206）
2025-05-10 08:00:04,331 - INFO - 样本订单日期检查 (共5个): 206，熟龄度分布: 当天=9, 昨天=99, 前天=54, 更早=44
2025-05-10 08:00:04,331 - INFO - 开始发送模板卡片消息到企业微信...
2025-05-10 08:00:04,332 - INFO - 开始发送消息到企业微信（全渠道生产环境）...
2025-05-10 08:00:04,572 - INFO - 企业微信API响应状态码: 200
2025-05-10 08:00:04,572 - INFO - 企业微信消息发送成功
2025-05-10 08:00:04,572 - INFO - 昨天的日报发送成功
2025-05-10 08:00:04,573 - INFO - 昨天的日报处理完成。
2025-05-10 08:00:04,573 - INFO - ==================================================
