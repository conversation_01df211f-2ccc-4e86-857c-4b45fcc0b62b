[2025-05-09 08:00:02] 开始执行日报脚本
[2025-05-09 08:00:02] 工作目录: /volume3/SSD-soft/fzsrb
[2025-05-09 08:00:02] 检查Python依赖...
WARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager, possibly rendering your system unusable. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv. Use the --root-user-action option if you know what you are doing and want to suppress this warning.
[2025-05-09 08:00:02] 开始执行简单日报脚本...
2025-05-09 08:00:04,737 - INFO - ==================================================
2025-05-09 08:00:04,737 - INFO - 开始生成日报统计数据...
2025-05-09 08:00:04,773 - INFO - 开始生成 2025-05-08 的报表数据...
2025-05-09 08:00:04,773 - INFO - 获取 2025-05-08 的订单数据（下单时间维度）...
2025-05-09 08:00:04,809 - INFO - 成功获取到 281 条去重后的订单数据（下单时间维度）
2025-05-09 08:00:04,809 - INFO - 开始分类并过滤订单数据...
2025-05-09 08:00:04,810 - INFO - 订单分类完成: 有效订单 281 单，其中骑手卡 254 单，流量卡 27 单
2025-05-09 08:00:04,810 - INFO - 计算各渠道的D0下单和D1激活数据...
2025-05-09 08:00:04,836 - INFO - 成功获取到 184 条当日激活订单
2025-05-09 08:00:04,839 - INFO - 样本订单日期检查 (共5个):
2025-05-09 08:00:04,839 - INFO - 订单 20250507101056137203: 下单时间=2025-05-07, 激活时间=2025-05-08, 相差=1天
2025-05-09 08:00:04,839 - INFO - 订单 20250430235461240599: 下单时间=2025-04-30, 激活时间=2025-05-08, 相差=8天
2025-05-09 08:00:04,839 - INFO - 订单 20250503070240565243: 下单时间=2025-05-03, 激活时间=2025-05-08, 相差=5天
2025-05-09 08:00:04,839 - INFO - 订单 20250507172415243890: 下单时间=2025-05-07, 激活时间=2025-05-08, 相差=1天
2025-05-09 08:00:04,840 - INFO - 订单 20250507071092654732: 下单时间=2025-05-07, 激活时间=2025-05-08, 相差=1天
2025-05-09 08:00:04,840 - INFO - 熟龄度处理统计: 总订单=184, 成功处理=184, 处理失败=0
2025-05-09 08:00:04,840 - INFO - 熟龄度分布: 当天=14, 昨天=100, 前天=44, 更早=26
2025-05-09 08:00:04,840 - INFO - 骑手卡熟龄度分布: 当天=13, 昨天=98, 前天=38, 更早=24
2025-05-09 08:00:04,840 - INFO - 流量卡熟龄度分布: 当天=1, 昨天=2, 前天=6, 更早=2
2025-05-09 08:00:04,840 - INFO - 熟龄度数据: {'same_day': 14, 'one_day': 100, 'two_days': 44, 'more_days': 26}
2025-05-09 08:00:04,840 - INFO - 骑手卡熟龄度数据: {'same_day': 13, 'one_day': 98, 'two_days': 38, 'more_days': 24}, 总数: 173
2025-05-09 08:00:04,840 - INFO - 流量卡熟龄度数据: {'same_day': 1, 'one_day': 2, 'two_days': 6, 'more_days': 2}, 总数: 11
2025-05-09 08:00:04,841 - INFO - 当日下单当日激活比例: 7.6%（14/184）
2025-05-09 08:00:04,841 - INFO - 样本订单日期检查 (共5个): 184，熟龄度分布: 当天=14, 昨天=100, 前天=44, 更早=26
2025-05-09 08:00:04,841 - INFO - 生成HTML报表模板，加载数据: 总活跃=184, 骑手卡=173, 流量卡=11
2025-05-09 08:00:04,841 - INFO - 骑手卡数据: [13, 98, 38, 24]
2025-05-09 08:00:04,841 - INFO - 流量卡数据: [1, 2, 6, 2]
2025-05-09 08:00:04,842 - INFO - HTML报表已生成: reports/report_20250508.html
2025-05-09 08:00:04,842 - INFO - 准备上传文件到FTP服务器: report_20250508.html
2025-05-09 08:00:04,842 - INFO - FTP配置: 主机=211.149.249.234, 端口=21, 目录=reports
2025-05-09 08:00:04,842 - INFO - 本地文件: reports/report_20250508.html, 大小: 33213 字节
2025-05-09 08:00:04,842 - INFO - 正在连接到FTP服务器: 211.149.249.234:21
2025-05-09 08:00:04,909 - INFO - 正在登录FTP服务器: 用户名=reports
2025-05-09 08:00:04,997 - INFO - FTP登录成功
2025-05-09 08:00:05,029 - INFO - 当前FTP目录: /
2025-05-09 08:00:05,029 - INFO - 尝试切换到目录: reports
2025-05-09 08:00:05,096 - INFO - 成功切换到目录: /reports
2025-05-09 08:00:05,228 - INFO - 上传前目录内容: ['report_20250506.html', 'report_20250503.html', 'report_20250502.html', '..', 'report_20250501.html', 'report_20250505.html', 'report_20250504.html', 'report_20250507.html', '.']
2025-05-09 08:00:05,228 - INFO - 开始上传文件: reports/report_20250508.html -> report_20250508.html
2025-05-09 08:00:05,418 - INFO - FTP上传结果: 226-File successfully transferred
226 0.062 seconds (measured here), 0.51 Mbytes per second
2025-05-09 08:00:05,551 - INFO - 上传后目录内容: ['report_20250506.html', 'report_20250503.html', 'report_20250508.html', 'report_20250502.html', '..', 'report_20250501.html', 'report_20250505.html', 'report_20250504.html', 'report_20250507.html', '.']
2025-05-09 08:00:05,551 - INFO - 文件已成功上传并验证: report_20250508.html
2025-05-09 08:00:05,551 - INFO - 文件访问URL: https://rep.331126.com/reports/report_20250508.html
2025-05-09 08:00:05,584 - INFO - FTP连接已关闭
2025-05-09 08:00:05,584 - INFO - 报表已上传到服务器，URL: https://rep.331126.com/reports/report_20250508.html
2025-05-09 08:00:05,584 - INFO - 日报生成完成
2025-05-09 08:00:05,584 - INFO - ==================================================
*get* '220---------- Welcome to Pure-FTPd [privsep] [TLS] ----------\n'
*get* '220-You are user number 1 of 50 allowed.\n'
*get* '220-Local time is now 08:00. Server port: 21.\n'
*get* '220-This is a private system - No anonymous login\n'
*get* '220-IPv6 connections are also welcome on this server.\n'
*get* '220 You will be disconnected after 15 minutes of inactivity.\n'
*resp* '220---------- Welcome to Pure-FTPd [privsep] [TLS] ----------\n220-You are user number 1 of 50 allowed.\n220-Local time is now 08:00. Server port: 21.\n220-This is a private system - No anonymous login\n220-IPv6 connections are also welcome on this server.\n220 You will be disconnected after 15 minutes of inactivity.'
*cmd* 'USER reports'
*put* 'USER reports\r\n'
*get* '331 User reports OK. Password required\n'
*resp* '331 User reports OK. Password required'
*cmd* 'PASS ****************'
*put* 'PASS ****************\r\n'
*get* '230 OK. Current directory is /\n'
*resp* '230 OK. Current directory is /'
*cmd* 'PWD'
*put* 'PWD\r\n'
*get* '257 "/" is your current location\n'
*resp* '257 "/" is your current location'
*cmd* 'CWD reports'
*put* 'CWD reports\r\n'
*get* '250 OK. Current directory is /reports\n'
*resp* '250 OK. Current directory is /reports'
*cmd* 'PWD'
*put* 'PWD\r\n'
*get* '257 "/reports" is your current location\n'
*resp* '257 "/reports" is your current location'
*cmd* 'TYPE A'
*put* 'TYPE A\r\n'
*get* '200 TYPE is now ASCII\n'
*resp* '200 TYPE is now ASCII'
*cmd* 'PASV'
*put* 'PASV\r\n'
*get* '227 Entering Passive Mode (211,149,249,234,153,37)\n'
*resp* '227 Entering Passive Mode (211,149,249,234,153,37)'
*cmd* 'NLST'
*put* 'NLST\r\n'
*get* '150 Accepted data connection\n'
*resp* '150 Accepted data connection'
*get* '226 9 matches total\n'
*resp* '226 9 matches total'
*cmd* 'TYPE I'
*put* 'TYPE I\r\n'
*get* '200 TYPE is now 8-bit binary\n'
*resp* '200 TYPE is now 8-bit binary'
*cmd* 'PASV'
*put* 'PASV\r\n'
*get* '227 Entering Passive Mode (211,149,249,234,153,41)\n'
*resp* '227 Entering Passive Mode (211,149,249,234,153,41)'
*cmd* 'STOR report_20250508.html'
*put* 'STOR report_20250508.html\r\n'
*get* '150 Accepted data connection\n'
*resp* '150 Accepted data connection'
*get* '226-File successfully transferred\n'
*get* '226 0.062 seconds (measured here), 0.51 Mbytes per second\n'
*resp* '226-File successfully transferred\n226 0.062 seconds (measured here), 0.51 Mbytes per second'
*cmd* 'TYPE A'
*put* 'TYPE A\r\n'
*get* '200 TYPE is now ASCII\n'
*resp* '200 TYPE is now ASCII'
*cmd* 'PASV'
*put* 'PASV\r\n'
*get* '227 Entering Passive Mode (211,149,249,234,154,106)\n'
*resp* '227 Entering Passive Mode (211,149,249,234,154,106)'
*cmd* 'NLST'
*put* 'NLST\r\n'
*get* '150 Accepted data connection\n'
*resp* '150 Accepted data connection'
*get* '226 10 matches total\n'
*resp* '226 10 matches total'
*cmd* 'QUIT'
*put* 'QUIT\r\n'
*get* '221-Goodbye. You uploaded 33 and downloaded 0 kbytes.\n'
*get* '221 Logout.\n'
*resp* '221-Goodbye. You uploaded 33 and downloaded 0 kbytes.\n221 Logout.'
[2025-05-09 08:00:02] 简单日报脚本执行成功
[2025-05-09 08:00:02] 开始执行全渠道日报脚本...
2025-05-09 08:00:05,741 - INFO - ==================================================
2025-05-09 08:00:05,741 - INFO - 开始生成并发送昨天的日报（全渠道生产环境）...
2025-05-09 08:00:05,741 - INFO - 开始执行昨天的日报发送流程（全渠道生产环境）...
2025-05-09 08:00:05,759 - INFO - 昨天日期: 2025-05-08
2025-05-09 08:00:05,759 - INFO - 开始获取昨天的订单数据（下单时间维度）...
2025-05-09 08:00:05,759 - INFO - 获取 2025-05-08 的订单数据（下单时间维度）...
2025-05-09 08:00:05,790 - INFO - 成功获取到 281 条去重后的订单数据（下单时间维度）
2025-05-09 08:00:05,790 - INFO - 开始获取昨天的订单数据（激活时间维度）...
2025-05-09 08:00:05,790 - INFO - 获取 2025-05-08 的订单数据（激活时间维度）...
2025-05-09 08:00:05,815 - INFO - 成功获取到 184 条去重后的订单数据（激活时间维度）
2025-05-09 08:00:05,816 - INFO - 成功获取到订单数据，继续生成日报
2025-05-09 08:00:05,816 - INFO - 开始生成昨天的日报...
2025-05-09 08:00:05,816 - INFO - 开始生成日报...
2025-05-09 08:00:05,816 - INFO - 开始分类并过滤订单数据...
2025-05-09 08:00:05,816 - INFO - 订单分类完成: 有效订单 281 单，其中骑手卡 254 单，大流量卡 27 单
2025-05-09 08:00:05,816 - INFO - 统计各类订单按渠道分类...
2025-05-09 08:00:05,816 - INFO - 开始分类并过滤订单数据...
2025-05-09 08:00:05,816 - INFO - 订单分类完成: 有效订单 184 单，其中骑手卡 173 单，大流量卡 11 单
2025-05-09 08:00:05,816 - INFO - 统计各类订单按渠道分类...
2025-05-09 08:00:05,817 - INFO - 日报生成完成
2025-05-09 08:00:05,817 - INFO - 开始分类并过滤订单数据...
2025-05-09 08:00:05,817 - INFO - 订单分类完成: 有效订单 281 单，其中骑手卡 254 单，大流量卡 27 单
2025-05-09 08:00:05,817 - INFO - 统计各类订单按渠道分类...
2025-05-09 08:00:05,817 - INFO - 开始分类并过滤订单数据...
2025-05-09 08:00:05,818 - INFO - 订单分类完成: 有效订单 184 单，其中骑手卡 173 单，大流量卡 11 单
2025-05-09 08:00:05,818 - INFO - 统计各类订单按渠道分类...
2025-05-09 08:00:05,819 - INFO - 开始生成 2025-05-08 的报表数据...
2025-05-09 08:00:05,819 - INFO - 获取 2025-05-08 的订单数据（下单时间维度）...
2025-05-09 08:00:05,850 - INFO - 成功获取到 281 条去重后的订单数据（下单时间维度）
2025-05-09 08:00:05,850 - INFO - 开始分类并过滤订单数据...
2025-05-09 08:00:05,851 - INFO - 订单分类完成: 有效订单 281 单，其中骑手卡 254 单，流量卡 27 单
2025-05-09 08:00:05,851 - INFO - 计算各渠道的D0下单和D1激活数据...
2025-05-09 08:00:05,875 - INFO - 成功获取到 184 条当日激活订单
2025-05-09 08:00:05,877 - INFO - 样本订单日期检查 (共5个):
2025-05-09 08:00:05,877 - INFO - 订单 20250507101056137203: 下单时间=2025-05-07, 激活时间=2025-05-08, 相差=1天
2025-05-09 08:00:05,878 - INFO - 订单 20250430235461240599: 下单时间=2025-04-30, 激活时间=2025-05-08, 相差=8天
2025-05-09 08:00:05,878 - INFO - 订单 20250503070240565243: 下单时间=2025-05-03, 激活时间=2025-05-08, 相差=5天
2025-05-09 08:00:05,878 - INFO - 订单 20250507172415243890: 下单时间=2025-05-07, 激活时间=2025-05-08, 相差=1天
2025-05-09 08:00:05,878 - INFO - 订单 20250507071092654732: 下单时间=2025-05-07, 激活时间=2025-05-08, 相差=1天
2025-05-09 08:00:05,878 - INFO - 熟龄度处理统计: 总订单=184, 成功处理=184, 处理失败=0
2025-05-09 08:00:05,878 - INFO - 熟龄度分布: 当天=14, 昨天=100, 前天=44, 更早=26
2025-05-09 08:00:05,878 - INFO - 骑手卡熟龄度分布: 当天=13, 昨天=98, 前天=38, 更早=24
2025-05-09 08:00:05,878 - INFO - 流量卡熟龄度分布: 当天=1, 昨天=2, 前天=6, 更早=2
2025-05-09 08:00:05,879 - INFO - 熟龄度数据: {'same_day': 14, 'one_day': 100, 'two_days': 44, 'more_days': 26}
2025-05-09 08:00:05,879 - INFO - 骑手卡熟龄度数据: {'same_day': 13, 'one_day': 98, 'two_days': 38, 'more_days': 24}, 总数: 173
2025-05-09 08:00:05,879 - INFO - 流量卡熟龄度数据: {'same_day': 1, 'one_day': 2, 'two_days': 6, 'more_days': 2}, 总数: 11
2025-05-09 08:00:05,879 - INFO - 当日下单当日激活比例: 7.6%（14/184）
2025-05-09 08:00:05,879 - INFO - 样本订单日期检查 (共5个): 184，熟龄度分布: 当天=14, 昨天=100, 前天=44, 更早=26
2025-05-09 08:00:05,879 - INFO - 开始发送模板卡片消息到企业微信...
2025-05-09 08:00:05,879 - INFO - 开始发送消息到企业微信（全渠道生产环境）...
2025-05-09 08:00:06,200 - INFO - 企业微信API响应状态码: 200
2025-05-09 08:00:06,200 - INFO - 企业微信消息发送成功
2025-05-09 08:00:06,200 - INFO - 昨天的日报发送成功
2025-05-09 08:00:06,200 - INFO - 昨天的日报处理完成。
2025-05-09 08:00:06,200 - INFO - ==================================================
[2025-05-09 08:00:02] 全渠道日报脚本执行成功
[2025-05-09 08:00:02] 开始执行OPPO渠道日报脚本...
2025-05-09 08:00:06,369 - INFO - ==================================================
2025-05-09 08:00:06,369 - INFO - 开始生成并发送OPPO和荣耀渠道日报（OPPO生产环境）...
2025-05-09 08:00:06,369 - INFO - 开始执行OPPO和荣耀渠道日报发送流程（OPPO生产环境）...
2025-05-09 08:00:06,387 - INFO - 查询日期: 2025-05-08
2025-05-09 08:00:06,387 - INFO - 获取 2025-05-08 的OPPO和荣耀渠道订单数据（下单时间维度）...
2025-05-09 08:00:06,406 - INFO - 查询到 26 条去重后的订单数据
2025-05-09 08:00:06,406 - INFO - 开始生成OPPO和荣耀渠道Markdown格式日报...
2025-05-09 08:00:06,406 - INFO - 开始处理订单数据，总数据条数: 26
2025-05-09 08:00:06,406 - INFO - 统计结果: 总订单 26
2025-05-09 08:00:06,406 - INFO -   OPPO: 21单
2025-05-09 08:00:06,406 - INFO -   荣耀: 5单
2025-05-09 08:00:06,406 - INFO - Markdown格式日报生成完成
2025-05-09 08:00:06,406 - INFO - 开始发送消息到企业微信（OPPO生产环境）...
2025-05-09 08:00:06,649 - INFO - 企业微信API响应状态码: 200
2025-05-09 08:00:06,649 - INFO - 企业微信消息发送成功
2025-05-09 08:00:06,649 - INFO - OPPO和荣耀渠道日报发送成功
2025-05-09 08:00:06,649 - INFO - OPPO和荣耀渠道日报处理完成。
2025-05-09 08:00:06,649 - INFO - ==================================================
[2025-05-09 08:00:02] OPPO渠道日报脚本执行成功
[2025-05-09 08:00:06] 所有日报脚本执行完成
----------------------------------------
