import requests
import json
import datetime
import pytz
import logging
import os
import pymysql
import config  # 导入配置文件
import argparse

# 配置日志
log_dir = "logs"
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

log_file = os.path.join(log_dir, f"oppo_report_{datetime.datetime.now().strftime('%Y%m%d')}.log")
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file, encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 从配置文件获取当前环境的配置
WEBHOOK_URL = config.get_current_webhook_url()
MYSQL_CONFIG = {**config.get_current_db_config(), "cursorclass": pymysql.cursors.DictCursor}

CHANNEL_LIST = [
    'OPPO',
    '荣耀'
]

def send_to_wechat(content, msgtype="text"):
    """
    发送消息到企业微信群聊机器人
    支持普通文本消息、卡片模板消息和Markdown消息
    
    参数:
        content: 消息内容，文本消息时为字符串，卡片或Markdown消息时为字典
        msgtype: 消息类型，默认为"text"，支持"text"、"template_card"和"markdown"
    """
    logger.info(f"开始发送消息到企业微信（{config.get_current_env_name()}）...")
    
    headers = {'Content-Type': 'application/json'}
    
    if msgtype == "text":
        data = {
            "msgtype": "text",
            "text": {
                "content": content
            }
        }
    elif msgtype == "template_card":
        # 卡片消息类型，直接使用传入的content作为整个消息体
        data = content
    elif msgtype == "markdown":
        # Markdown消息类型，直接使用传入的content作为整个消息体
        data = content
    else:
        logger.error(f"不支持的消息类型: {msgtype}")
        return False
    
    try:
        response = requests.post(WEBHOOK_URL, headers=headers, data=json.dumps(data))
        logger.info(f"企业微信API响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('errcode') == 0:
                logger.info("企业微信消息发送成功")
                return True
            else:
                logger.error(f"企业微信消息发送失败: {result.get('errmsg')}")
                return False
        else:
            logger.error(f"HTTP请求失败: {response.status_code}")
            return False
    except Exception as e:
        logger.exception(f"发送消息到企业微信时出错: {e}")
        return False

def get_oppo_order_data(date, by_update_time=False):
    """
    获取指定日期的OPPO和荣耀渠道订单数据（使用激活时间或下单时间维度）
    """
    logger.info(f"获取 {date} 的OPPO和荣耀渠道订单数据（{'激活时间' if by_update_time else '下单时间'}维度）...")
    start_time = f"{date} 00:00:00"
    end_time = f"{date} 23:59:59"
    if by_update_time:
        query = f"""
        WITH RankedOrders AS (
        SELECT
            f.orderNo,
            f.target,
            cc.description AS channel_description,
            pc.card_typ,
            pc.productName_alias,
            f.businessStatus,
            f.outStatus,
            f.send_goods_result_Alias,
            f.createTime,
            f.updateTime,
            f.logisticsStatus,
            ROW_NUMBER() OVER (PARTITION BY f.target, pc.productName_alias ORDER BY f.updateTime) as rn
        FROM
            forders f
        LEFT JOIN
            channel_config cc ON f.channel_config = cc.id
        LEFT JOIN
            phone_card pc ON f.Phone_Card = pc.skuCode
        WHERE
            f.updateTime BETWEEN %s AND %s
            AND cc.description IN ({', '.join(['\'' + c + '\'' for c in CHANNEL_LIST])})
            AND f.logisticsStatus = '发货成功'
        )
        SELECT * FROM RankedOrders WHERE rn = 1
        """
    else:
        query = f"""
        WITH RankedOrders AS (
        SELECT
            f.orderNo,
            f.target,
            cc.description AS channel_description,
            pc.card_typ,
            pc.productName_alias,
            f.businessStatus,
            f.outStatus,
            f.send_goods_result_Alias,
            f.createTime,
            f.updateTime,
            f.logisticsStatus,
            ROW_NUMBER() OVER (PARTITION BY f.target, pc.productName_alias ORDER BY f.createTime) as rn
        FROM
            forders f
        LEFT JOIN
            channel_config cc ON f.channel_config = cc.id
        LEFT JOIN
            phone_card pc ON f.Phone_Card = pc.skuCode
        WHERE
            f.createTime BETWEEN %s AND %s
            AND cc.description IN ({', '.join(['\'' + c + '\'' for c in CHANNEL_LIST])})
            AND f.logisticsStatus IN ('发货中', '发货成功')
        )
        SELECT * FROM RankedOrders WHERE rn = 1
        """
    try:
        conn = pymysql.connect(**MYSQL_CONFIG)
        with conn.cursor() as cursor:
            cursor.execute(query, (start_time, end_time))
            data = cursor.fetchall()
        conn.close()
        logger.info(f"查询到 {len(data)} 条去重后的订单数据")
        for row in data:
            logger.debug(f"订单号: {row['orderNo']}, 渠道: {row['channel_description']}, 手机号: {row['target']}, 状态: {row['logisticsStatus']}, 产品: {row.get('productName_alias', '未知产品')}")
        return data
    except Exception as e:
        logger.exception(f"获取OPPO和荣耀订单数据时出错: {e}")
        return []

def format_date_no_padding(date_obj):
    month = date_obj.month
    day = date_obj.day
    return f"{month}月{day}日"

def process_order_data_group_by_channel_and_product(data, is_activation_dimension=False):
    """
    统计每个渠道下各商品别名的数量
    """
    channel_product_stats = {c: {} for c in CHANNEL_LIST}
    valid_orders = []
    for order in data:
        logistics_status = order.get("logisticsStatus", "Unknown")
        if is_activation_dimension:
            if logistics_status == "发货成功":
                valid_orders.append(order)
        else:
            if logistics_status in ["发货中", "发货成功"]:
                valid_orders.append(order)
    for order in valid_orders:
        channel = order.get("channel_description", "未知渠道")
        product = order.get("productName_alias", "未知产品")
        if channel in channel_product_stats:
            if product not in channel_product_stats[channel]:
                channel_product_stats[channel][product] = 0
            channel_product_stats[channel][product] += 1
    return len(valid_orders), channel_product_stats

def send_oppo_daily_report():
    logger.info(f"开始执行OPPO和荣耀渠道日报发送流程（{config.get_current_env_name()}）...")
    try:
        china_tz = pytz.timezone('Asia/Shanghai')
        now = datetime.datetime.now(china_tz)
        yesterday = (now - datetime.timedelta(days=1)).replace(hour=0, minute=0, second=0, microsecond=0)
        yesterday_str = yesterday.strftime("%Y-%m-%d")
        month = yesterday.month
        day = yesterday.day
        # 获取订单数据（下单时间维度）
        order_data = get_oppo_order_data(yesterday_str, by_update_time=False)
        # 获取订单数据（激活时间维度）
        activation_data = get_oppo_order_data(yesterday_str, by_update_time=True)
        # 统计本月总激活数
        try:
            month_start = yesterday.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            month_start_str = month_start.strftime("%Y-%m-%d 00:00:00")
            month_end_str = yesterday.strftime("%Y-%m-%d 23:59:59")
            month_query = f"""
            SELECT COUNT(DISTINCT f.orderNo) as total
            FROM forders f
            LEFT JOIN channel_config cc ON f.channel_config = cc.id
            WHERE f.updateTime BETWEEN %s AND %s
              AND f.logisticsStatus = '发货成功'
              AND cc.description IN ({', '.join(['\'' + c + '\'' for c in CHANNEL_LIST])})
            """
            conn = pymysql.connect(**MYSQL_CONFIG)
            with conn.cursor() as cursor:
                cursor.execute(month_query, (month_start_str, month_end_str))
                month_result = cursor.fetchone()
            conn.close()
            month_total_activations = month_result["total"] if month_result and "total" in month_result else 0
        except Exception as e:
            logger.warning(f"获取本月总激活数失败: {e}")
            month_total_activations = 0
        # 统计本月总订单数
        try:
            month_order_query = f"""
            WITH RankedOrders AS (
                SELECT
                    f.orderNo,
                    f.target,
                    pc.productName_alias,
                    cc.description AS channel_description,
                    ROW_NUMBER() OVER (PARTITION BY f.target, pc.productName_alias ORDER BY f.createTime) as rn
                FROM
                    forders f
                LEFT JOIN
                    phone_card pc ON f.Phone_Card = pc.skuCode
                LEFT JOIN
                    channel_config cc ON f.channel_config = cc.id
                WHERE
                    f.createTime BETWEEN %s AND %s
                    AND f.logisticsStatus IN ('发货中', '发货成功')
                    AND cc.description IN ({', '.join(['\'' + c + '\'' for c in CHANNEL_LIST])})
            )
            SELECT COUNT(*) as total FROM RankedOrders WHERE rn = 1
            """
            conn = pymysql.connect(**MYSQL_CONFIG)
            with conn.cursor() as cursor:
                cursor.execute(month_order_query, (month_start_str, month_end_str))
                month_order_result = cursor.fetchone()
            conn.close()
            month_total_orders = month_order_result["total"] if month_order_result and "total" in month_order_result else 0
        except Exception as e:
            logger.warning(f"获取本月总订单数失败: {e}")
            month_total_orders = 0
        # 统计昨日订单和激活，按渠道和商品分组
        order_count, order_channel_stats = process_order_data_group_by_channel_and_product(order_data, is_activation_dimension=False)
        activation_count, activation_channel_stats = process_order_data_group_by_channel_and_product(activation_data, is_activation_dimension=True)
        # 组装文本
        report_lines = [
            f"{month}月{day}日运营日报",
            "",
            f"本月总激活：{month_total_activations}",
            f"当月订单：{month_total_orders}",
            f"1、当日订单：{order_count}"
        ]
        for channel in CHANNEL_LIST:
            prods = order_channel_stats.get(channel, {})
            if prods:
                prod_str = "，".join([f"{k}×{v}" for k, v in prods.items()])
                report_lines.append(f"{channel}：{prod_str}")
            else:
                report_lines.append(f"{channel}：无")
        report_lines.append(f"2、当日激活：{activation_count}")
        for channel in CHANNEL_LIST:
            prods = activation_channel_stats.get(channel, {})
            if prods:
                prod_str = "，".join([f"{k}×{v}" for k, v in prods.items()])
                report_lines.append(f"{channel}：{prod_str}")
            else:
                report_lines.append(f"{channel}：无")
        report_text = "\n".join(report_lines)
        send_result = send_to_wechat(report_text)
        if send_result:
            logger.info("OPPO和荣耀渠道日报发送成功")
        else:
            logger.error("日报发送失败")
        return "日报已发送"
    except Exception as e:
        logger.exception(f"发送OPPO和荣耀渠道日报时发生错误: {e}")
        return f"发送OPPO和荣耀渠道日报时发生错误: {e}"

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='OPPO和荣耀渠道日报生成器')
    args = parser.parse_args()
    logger.info("=" * 50)
    logger.info(f"开始生成并发送OPPO和荣耀渠道日报（{config.get_current_env_name()}）...")
    result = send_oppo_daily_report()
    if "未获取到订单数据" in str(result):
        logger.warning("由于没有数据，日报未发送")
    else:
        logger.info("OPPO和荣耀渠道日报处理完成。")
    logger.info("=" * 50)