2025-05-13 14:37:42,546 - INFO - ==================================================
2025-05-13 14:37:42,546 - INFO - 开始统计5月份骑手卡订单数据...
2025-05-13 14:37:42,546 - INFO - 获取5月份的订单数据...
2025-05-13 14:37:42,775 - INFO - 成功获取到 0 条订单数据
2025-05-13 14:37:42,775 - WARNING - 未获取到订单数据，程序退出
2025-05-13 14:40:24,343 - INFO - ==================================================
2025-05-13 14:40:24,343 - INFO - 开始统计2025年5月份骑手卡订单数据...
2025-05-13 14:40:24,343 - INFO - 获取数据库调试信息...
2025-05-13 14:40:24,426 - INFO - 数据库中的表: ['article', 'categories', 'channel_config', 'directus_access', 'directus_activity', 'directus_collections', 'directus_comments', 'directus_dashboards', 'directus_extensions', 'directus_fields', 'directus_files', 'directus_flows', 'directus_folders', 'directus_migrations', 'directus_notifications', 'directus_operations', 'directus_panels', 'directus_permissions', 'directus_policies', 'directus_presets', 'directus_relations', 'directus_revisions', 'directus_roles', 'directus_sessions', 'directus_settings', 'directus_shares', 'directus_translations', 'directus_users', 'directus_versions', 'directus_webhooks', 'forders', 'order_analysis_view', 'order_stats_by_activation_date', 'order_stats_by_create_date', 'phone_card', 'product_data', 'product_log', 'report_history', 'shop_data', 'shop_log', 'wechat_robots']
2025-05-13 14:40:24,442 - INFO - forders表的列: ['orderNo', 'outStatus', 'businessStatus', 'createTime', 'updateTime', 'deliveryTime', 'send_goods_result', 'target', 'send_goods_result_Alias', 'Phone_Card', 'channel_config', 'logisticsStatus']
2025-05-13 14:40:24,463 - INFO - forders表示例数据（前5条）: [{'orderNo': '20250401000036332312', 'outStatus': '已激活', 'businessStatus': '交易成功', 'createTime': datetime.datetime(2025, 4, 1, 0, 0, 3), 'updateTime': datetime.datetime(2025, 4, 2, 10, 54, 18), 'deliveryTime': datetime.datetime(2025, 4, 2, 10, 54, 18), 'send_goods_result': '{"raw": "发货回调，回调入参是{\\"orderExecBillId\\":\\"4a6f1f38b1f74c06934d3fa8de603d84\\",\\"resultCode\\":\\"success\\",\\"resultMsg\\":null,\\"oriPayload\\":\\"{\\\\\\"originalContent\\\\\\":\\\\\\"{\\\\\\\\\\\\\\"activityTime\\\\\\\\\\\\\\":\\\\\\\\\\\\\\"1743562457000\\\\\\\\\\\\\\",\\\\\\\\\\\\\\"channelSeqId\\\\\\\\\\\\\\":\\\\\\\\\\\\\\"4a6f1f38b1f74c06934d3fa8de603d84\\\\\\\\\\\\\\",\\\\\\\\\\\\\\"mobile\\\\\\\\\\\\\\":\\\\\\\\\\\\\\"***********\\\\\\\\\\\\\\",\\\\\\\\\\\\\\"orderNo\\\\\\\\\\\\\\":\\\\\\\\\\\\\\"2504010000500111110\\\\\\\\\\\\\\",\\\\\\\\\\\\\\"shipmentCompanyName\\\\\\\\\\\\\\":\\\\\\\\\\\\\\"顺丰快递\\\\\\\\\\\\\\",\\\\\\\\\\\\\\"shipmentNumber\\\\\\\\\\\\\\":\\\\\\\\\\\\\\"SF3152638904915\\\\\\\\\\\\\\",\\\\\\\\\\\\\\"status\\\\\\\\\\\\\\":\\\\\\\\\\\\\\"1035\\\\\\\\\\\\\\",\\\\\\\\\\\\\\"updateTime\\\\\\\\\\\\\\":\\\\\\\\\\\\\\"1743562458168\\\\\\\\\\\\\\"}\\\\\\",\\\\\\"mMsg\\\\\\":null,\\\\\\"mStatus\\\\\\":"}', 'target': '***********', 'send_goods_result_Alias': '', 'Phone_Card': 'ZGYDHK_JYOPPOK_OPPO_39', 'channel_config': 20, 'logisticsStatus': '发货成功'}, {'orderNo': '20250401000657666013', 'outStatus': None, 'businessStatus': '待发货', 'createTime': datetime.datetime(2025, 4, 1, 0, 6, 22), 'updateTime': datetime.datetime(2025, 4, 1, 0, 6, 22), 'deliveryTime': datetime.datetime(2025, 4, 1, 0, 6, 26), 'send_goods_result': '{"resultCode": "0004", "title": "访问上游接口出错", "details": "订购提交前置校验失败，入网资格校验失败，用户不在开展范围内。", "links": ""}', 'target': '***********', 'send_goods_result_Alias': '订购提交前置校验失败，入网资格校验失败，用户不在开展范围内。', 'Phone_Card': 'ZGYDHK_JYSFK_59', 'channel_config': 29, 'logisticsStatus': '发货失败'}, {'orderNo': '20250401001190865066', 'outStatus': '已激活', 'businessStatus': '交易成功', 'createTime': datetime.datetime(2025, 4, 1, 0, 11, 20), 'updateTime': datetime.datetime(2025, 4, 1, 16, 0, 30), 'deliveryTime': datetime.datetime(2025, 4, 1, 16, 0, 30), 'send_goods_result': '{"raw": "发货回调，回调入参是{\\"orderExecBillId\\":\\"2caeea92dac3476bb94f91cedbf7d647\\",\\"resultCode\\":\\"success\\",\\"resultMsg\\":null,\\"oriPayload\\":\\"{\\\\\\"originalContent\\\\\\":\\\\\\"{\\\\\\\\\\\\\\"activityTime\\\\\\\\\\\\\\":\\\\\\\\\\\\\\"1743494430000\\\\\\\\\\\\\\",\\\\\\\\\\\\\\"channelSeqId\\\\\\\\\\\\\\":\\\\\\\\\\\\\\"2caeea92dac3476bb94f91cedbf7d647\\\\\\\\\\\\\\",\\\\\\\\\\\\\\"mobile\\\\\\\\\\\\\\":\\\\\\\\\\\\\\"***********\\\\\\\\\\\\\\",\\\\\\\\\\\\\\"orderNo\\\\\\\\\\\\\\":\\\\\\\\\\\\\\"2504010068100111592\\\\\\\\\\\\\\",\\\\\\\\\\\\\\"shipmentCompanyName\\\\\\\\\\\\\\":\\\\\\\\\\\\\\"顺丰快递\\\\\\\\\\\\\\",\\\\\\\\\\\\\\"shipmentNumber\\\\\\\\\\\\\\":\\\\\\\\\\\\\\"SF3160862366587\\\\\\\\\\\\\\",\\\\\\\\\\\\\\"status\\\\\\\\\\\\\\":\\\\\\\\\\\\\\"1035\\\\\\\\\\\\\\",\\\\\\\\\\\\\\"updateTime\\\\\\\\\\\\\\":\\\\\\\\\\\\\\"1743494430491\\\\\\\\\\\\\\"}\\\\\\",\\\\\\"mMsg\\\\\\":null,\\\\\\"mStatus\\\\\\":"}', 'target': '***********', 'send_goods_result_Alias': '', 'Phone_Card': 'ZGYDHK_JYSFK_59', 'channel_config': 29, 'logisticsStatus': '发货成功'}, {'orderNo': '20250401001192715802', 'outStatus': '已激活', 'businessStatus': '交易成功', 'createTime': datetime.datetime(2025, 4, 1, 0, 11, 49), 'updateTime': datetime.datetime(2025, 4, 2, 10, 16, 42), 'deliveryTime': datetime.datetime(2025, 4, 2, 10, 16, 42), 'send_goods_result': '{"raw": "发货回调，回调入参是{\\"orderExecBillId\\":\\"ef869e6f0dd042b9b15900863d2d68b2\\",\\"resultCode\\":\\"success\\",\\"resultMsg\\":null,\\"oriPayload\\":\\"{\\\\\\"originalContent\\\\\\":\\\\\\"{\\\\\\\\\\\\\\"activityTime\\\\\\\\\\\\\\":\\\\\\\\\\\\\\"1743560200000\\\\\\\\\\\\\\",\\\\\\\\\\\\\\"channelSeqId\\\\\\\\\\\\\\":\\\\\\\\\\\\\\"ef869e6f0dd042b9b15900863d2d68b2\\\\\\\\\\\\\\",\\\\\\\\\\\\\\"mobile\\\\\\\\\\\\\\":\\\\\\\\\\\\\\"***********\\\\\\\\\\\\\\",\\\\\\\\\\\\\\"orderNo\\\\\\\\\\\\\\":\\\\\\\\\\\\\\"2504010071100111385\\\\\\\\\\\\\\",\\\\\\\\\\\\\\"shipmentCompanyName\\\\\\\\\\\\\\":\\\\\\\\\\\\\\"京东快递\\\\\\\\\\\\\\",\\\\\\\\\\\\\\"shipmentNumber\\\\\\\\\\\\\\":\\\\\\\\\\\\\\"JDV020353309335\\\\\\\\\\\\\\",\\\\\\\\\\\\\\"status\\\\\\\\\\\\\\":\\\\\\\\\\\\\\"1035\\\\\\\\\\\\\\",\\\\\\\\\\\\\\"updateTime\\\\\\\\\\\\\\":\\\\\\\\\\\\\\"1743560202134\\\\\\\\\\\\\\"}\\\\\\",\\\\\\"mMsg\\\\\\":null,\\\\\\"mStatus\\\\\\":"}', 'target': '***********', 'send_goods_result_Alias': '', 'Phone_Card': 'ZGYDHK_JYSFK_59', 'channel_config': 29, 'logisticsStatus': '发货成功'}, {'orderNo': '20250401003157761222', 'outStatus': 'ERROR', 'businessStatus': '发货中', 'createTime': datetime.datetime(2025, 4, 1, 0, 31, 46), 'updateTime': datetime.datetime(2025, 5, 1, 3, 1, 4), 'deliveryTime': None, 'send_goods_result': '{"raw": "发货回调，回调入参是{\\"orderExecBillId\\":\\"3f4f5dac1cbd411693dd226230e2e432\\",\\"resultCode\\":\\"failure\\",\\"resultMsg\\":null,\\"oriPayload\\":\\"{\\\\\\"originalContent\\\\\\":\\\\\\"{\\\\\\\\\\\\\\"channelSeqId\\\\\\\\\\\\\\":\\\\\\\\\\\\\\"3f4f5dac1cbd411693dd226230e2e432\\\\\\\\\\\\\\",\\\\\\\\\\\\\\"mobile\\\\\\\\\\\\\\":\\\\\\\\\\\\\\"***********\\\\\\\\\\\\\\",\\\\\\\\\\\\\\"orderNo\\\\\\\\\\\\\\":\\\\\\\\\\\\\\"2504010190800111824\\\\\\\\\\\\\\",\\\\\\\\\\\\\\"shipmentCompanyName\\\\\\\\\\\\\\":\\\\\\\\\\\\\\"省公司自建物流\\\\\\\\\\\\\\",\\\\\\\\\\\\\\"shipmentNumber\\\\\\\\\\\\\\":\\\\\\\\\\\\\\"82025040111195586319\\\\\\\\\\\\\\",\\\\\\\\\\\\\\"status\\\\\\\\\\\\\\":\\\\\\\\\\\\\\"1030\\\\\\\\\\\\\\",\\\\\\\\\\\\\\"statusDesc\\\\\\\\\\\\\\":\\\\\\\\\\\\\\"H05-超过省公司期限未激活关闭\\\\\\\\\\\\\\",\\\\\\\\\\\\\\"updateTime\\\\\\\\\\\\\\":\\\\\\\\\\\\\\"1746039664286\\\\\\\\\\\\\\"}\\\\\\",\\\\\\"mMsg\\\\\\":null,\\\\\\"m"}', 'target': '15061882608', 'send_goods_result_Alias': '', 'Phone_Card': 'ZGYDHK_JYSFK_59', 'channel_config': 29, 'logisticsStatus': '发货失败'}]
2025-05-13 14:40:24,486 - INFO - 最近的订单记录: [{'createTime': datetime.datetime(2025, 5, 13, 0, 0, 28), 'logisticsStatus': '发货中'}, {'createTime': datetime.datetime(2025, 5, 12, 23, 49, 50), 'logisticsStatus': '发货中'}, {'createTime': datetime.datetime(2025, 5, 12, 23, 42, 58), 'logisticsStatus': '发货中'}, {'createTime': datetime.datetime(2025, 5, 12, 23, 42, 37), 'logisticsStatus': '发货失败'}, {'createTime': datetime.datetime(2025, 5, 12, 23, 42, 2), 'logisticsStatus': '发货失败'}]
2025-05-13 14:40:24,505 - INFO - 2025年5月份订单记录数: 9405
2025-05-13 14:40:24,520 - INFO - phone_card表中的卡类型: ['流量卡', '骑手卡']
2025-05-13 14:40:24,521 - INFO - 获取2025年5月份的订单数据...
2025-05-13 14:40:24,829 - INFO - 成功获取到 5205 条订单数据
2025-05-13 14:40:24,829 - INFO - 开始处理订单数据...
2025-05-13 14:40:24,833 - INFO - 订单数据处理完成，共有 13 天有订单记录
2025-05-13 14:40:24,833 - INFO - 开始生成Excel报表...
2025-05-13 14:40:25,151 - INFO - Excel报表生成完成: reports\骑手卡订单统计_2025年5月_20250513.xlsx
2025-05-13 14:40:25,151 - INFO - 2025年5月份骑手卡订单统计完成，Excel报表已生成: reports\骑手卡订单统计_2025年5月_20250513.xlsx
2025-05-13 14:40:25,151 - INFO - ==================================================
