2025-05-08 08:00:02,564 - INFO - ==================================================
2025-05-08 08:00:02,564 - INFO - 开始生成日报统计数据...
2025-05-08 08:00:02,580 - INFO - 开始生成 2025-05-07 的报表数据...
2025-05-08 08:00:02,581 - INFO - 获取 2025-05-07 的订单数据（下单时间维度）...
2025-05-08 08:00:02,618 - INFO - 成功获取到 273 条去重后的订单数据（下单时间维度）
2025-05-08 08:00:02,618 - INFO - 开始分类并过滤订单数据...
2025-05-08 08:00:02,618 - INFO - 订单分类完成: 有效订单 273 单，其中骑手卡 252 单，流量卡 21 单
2025-05-08 08:00:02,618 - INFO - 计算各渠道的D0下单和D1激活数据...
2025-05-08 08:00:02,641 - INFO - 成功获取到 201 条当日激活订单
2025-05-08 08:00:02,645 - INFO - 样本订单日期检查 (共5个):
2025-05-08 08:00:02,645 - INFO - 订单 20250506145498869572: 下单时间=2025-05-06, 激活时间=2025-05-07, 相差=1天
2025-05-08 08:00:02,645 - INFO - 订单 20250506231241242507: 下单时间=2025-05-06, 激活时间=2025-05-07, 相差=1天
2025-05-08 08:00:02,645 - INFO - 订单 20250505100337034010: 下单时间=2025-05-05, 激活时间=2025-05-07, 相差=2天
2025-05-08 08:00:02,645 - INFO - 订单 20250506100590682568: 下单时间=2025-05-06, 激活时间=2025-05-07, 相差=1天
2025-05-08 08:00:02,645 - INFO - 订单 20250506173862311697: 下单时间=2025-05-06, 激活时间=2025-05-07, 相差=1天
2025-05-08 08:00:02,645 - INFO - 熟龄度处理统计: 总订单=201, 成功处理=201, 处理失败=0
2025-05-08 08:00:02,645 - INFO - 熟龄度分布: 当天=14, 昨天=116, 前天=34, 更早=37
2025-05-08 08:00:02,645 - INFO - 骑手卡熟龄度分布: 当天=14, 昨天=108, 前天=34, 更早=28
2025-05-08 08:00:02,645 - INFO - 流量卡熟龄度分布: 当天=0, 昨天=8, 前天=0, 更早=9
2025-05-08 08:00:02,646 - INFO - 熟龄度数据: {'same_day': 14, 'one_day': 116, 'two_days': 34, 'more_days': 37}
2025-05-08 08:00:02,646 - INFO - 骑手卡熟龄度数据: {'same_day': 14, 'one_day': 108, 'two_days': 34, 'more_days': 28}, 总数: 184
2025-05-08 08:00:02,646 - INFO - 流量卡熟龄度数据: {'same_day': 0, 'one_day': 8, 'two_days': 0, 'more_days': 9}, 总数: 17
2025-05-08 08:00:02,646 - INFO - 当日下单当日激活比例: 7.0%（14/201）
2025-05-08 08:00:02,646 - INFO - 样本订单日期检查 (共5个): 201，熟龄度分布: 当天=14, 昨天=116, 前天=34, 更早=37
2025-05-08 08:00:02,647 - INFO - 生成HTML报表模板，加载数据: 总活跃=201, 骑手卡=184, 流量卡=17
2025-05-08 08:00:02,647 - INFO - 骑手卡数据: [14, 108, 34, 28]
2025-05-08 08:00:02,647 - INFO - 流量卡数据: [0, 8, 0, 9]
2025-05-08 08:00:02,647 - INFO - HTML报表已生成: reports/report_20250507.html
2025-05-08 08:00:02,647 - INFO - 准备上传文件到FTP服务器: report_20250507.html
2025-05-08 08:00:02,647 - INFO - FTP配置: 主机=211.149.249.234, 端口=21, 目录=reports
2025-05-08 08:00:02,647 - INFO - 本地文件: reports/report_20250507.html, 大小: 33083 字节
2025-05-08 08:00:02,647 - INFO - 正在连接到FTP服务器: 211.149.249.234:21
2025-05-08 08:00:02,709 - INFO - 正在登录FTP服务器: 用户名=reports
2025-05-08 08:00:02,797 - INFO - FTP登录成功
2025-05-08 08:00:02,827 - INFO - 当前FTP目录: /
2025-05-08 08:00:02,827 - INFO - 尝试切换到目录: reports
2025-05-08 08:00:02,887 - INFO - 成功切换到目录: /reports
2025-05-08 08:00:03,025 - INFO - 上传前目录内容: ['report_20250506.html', 'report_20250503.html', 'report_20250502.html', '..', 'report_20250501.html', 'report_20250505.html', 'report_20250504.html', '.']
2025-05-08 08:00:03,025 - INFO - 开始上传文件: reports/report_20250507.html -> report_20250507.html
2025-05-08 08:00:03,214 - INFO - FTP上传结果: 226-File successfully transferred
226 0.067 seconds (measured here), 482.60 Kbytes per second
2025-05-08 08:00:03,344 - INFO - 上传后目录内容: ['report_20250506.html', 'report_20250503.html', 'report_20250502.html', '..', 'report_20250501.html', 'report_20250505.html', 'report_20250504.html', 'report_20250507.html', '.']
2025-05-08 08:00:03,344 - INFO - 文件已成功上传并验证: report_20250507.html
2025-05-08 08:00:03,344 - INFO - 文件访问URL: https://rep.331126.com/reports/report_20250507.html
2025-05-08 08:00:03,374 - INFO - FTP连接已关闭
2025-05-08 08:00:03,374 - INFO - 报表已上传到服务器，URL: https://rep.331126.com/reports/report_20250507.html
2025-05-08 08:00:03,374 - INFO - 日报生成完成
2025-05-08 08:00:03,374 - INFO - ==================================================
2025-05-08 08:00:03,530 - INFO - ==================================================
2025-05-08 08:00:03,530 - INFO - 开始生成并发送昨天的日报（全渠道生产环境）...
2025-05-08 08:00:03,530 - INFO - 开始执行昨天的日报发送流程（全渠道生产环境）...
2025-05-08 08:00:03,548 - INFO - 昨天日期: 2025-05-07
2025-05-08 08:00:03,548 - INFO - 开始获取昨天的订单数据（下单时间维度）...
2025-05-08 08:00:03,548 - INFO - 获取 2025-05-07 的订单数据（下单时间维度）...
2025-05-08 08:00:03,578 - INFO - 成功获取到 273 条去重后的订单数据（下单时间维度）
2025-05-08 08:00:03,578 - INFO - 开始获取昨天的订单数据（激活时间维度）...
2025-05-08 08:00:03,578 - INFO - 获取 2025-05-07 的订单数据（激活时间维度）...
2025-05-08 08:00:03,603 - INFO - 成功获取到 201 条去重后的订单数据（激活时间维度）
2025-05-08 08:00:03,603 - INFO - 成功获取到订单数据，继续生成日报
2025-05-08 08:00:03,603 - INFO - 开始生成昨天的日报...
2025-05-08 08:00:03,603 - INFO - 开始生成日报...
2025-05-08 08:00:03,603 - INFO - 开始分类并过滤订单数据...
2025-05-08 08:00:03,604 - INFO - 订单分类完成: 有效订单 273 单，其中骑手卡 252 单，大流量卡 21 单
2025-05-08 08:00:03,604 - INFO - 统计各类订单按渠道分类...
2025-05-08 08:00:03,604 - INFO - 开始分类并过滤订单数据...
2025-05-08 08:00:03,604 - INFO - 订单分类完成: 有效订单 201 单，其中骑手卡 184 单，大流量卡 17 单
2025-05-08 08:00:03,604 - INFO - 统计各类订单按渠道分类...
2025-05-08 08:00:03,604 - INFO - 日报生成完成
2025-05-08 08:00:03,604 - INFO - 开始分类并过滤订单数据...
2025-05-08 08:00:03,605 - INFO - 订单分类完成: 有效订单 273 单，其中骑手卡 252 单，大流量卡 21 单
2025-05-08 08:00:03,605 - INFO - 统计各类订单按渠道分类...
2025-05-08 08:00:03,605 - INFO - 开始分类并过滤订单数据...
2025-05-08 08:00:03,605 - INFO - 订单分类完成: 有效订单 201 单，其中骑手卡 184 单，大流量卡 17 单
2025-05-08 08:00:03,605 - INFO - 统计各类订单按渠道分类...
2025-05-08 08:00:03,606 - INFO - 开始生成 2025-05-07 的报表数据...
2025-05-08 08:00:03,607 - INFO - 获取 2025-05-07 的订单数据（下单时间维度）...
2025-05-08 08:00:03,636 - INFO - 成功获取到 273 条去重后的订单数据（下单时间维度）
2025-05-08 08:00:03,636 - INFO - 开始分类并过滤订单数据...
2025-05-08 08:00:03,637 - INFO - 订单分类完成: 有效订单 273 单，其中骑手卡 252 单，流量卡 21 单
2025-05-08 08:00:03,637 - INFO - 计算各渠道的D0下单和D1激活数据...
2025-05-08 08:00:03,661 - INFO - 成功获取到 201 条当日激活订单
2025-05-08 08:00:03,663 - INFO - 样本订单日期检查 (共5个):
2025-05-08 08:00:03,664 - INFO - 订单 20250506145498869572: 下单时间=2025-05-06, 激活时间=2025-05-07, 相差=1天
2025-05-08 08:00:03,664 - INFO - 订单 20250506231241242507: 下单时间=2025-05-06, 激活时间=2025-05-07, 相差=1天
2025-05-08 08:00:03,664 - INFO - 订单 20250505100337034010: 下单时间=2025-05-05, 激活时间=2025-05-07, 相差=2天
2025-05-08 08:00:03,664 - INFO - 订单 20250506100590682568: 下单时间=2025-05-06, 激活时间=2025-05-07, 相差=1天
2025-05-08 08:00:03,664 - INFO - 订单 20250506173862311697: 下单时间=2025-05-06, 激活时间=2025-05-07, 相差=1天
2025-05-08 08:00:03,664 - INFO - 熟龄度处理统计: 总订单=201, 成功处理=201, 处理失败=0
2025-05-08 08:00:03,664 - INFO - 熟龄度分布: 当天=14, 昨天=116, 前天=34, 更早=37
2025-05-08 08:00:03,664 - INFO - 骑手卡熟龄度分布: 当天=14, 昨天=108, 前天=34, 更早=28
2025-05-08 08:00:03,664 - INFO - 流量卡熟龄度分布: 当天=0, 昨天=8, 前天=0, 更早=9
2025-05-08 08:00:03,665 - INFO - 熟龄度数据: {'same_day': 14, 'one_day': 116, 'two_days': 34, 'more_days': 37}
2025-05-08 08:00:03,665 - INFO - 骑手卡熟龄度数据: {'same_day': 14, 'one_day': 108, 'two_days': 34, 'more_days': 28}, 总数: 184
2025-05-08 08:00:03,665 - INFO - 流量卡熟龄度数据: {'same_day': 0, 'one_day': 8, 'two_days': 0, 'more_days': 9}, 总数: 17
2025-05-08 08:00:03,665 - INFO - 当日下单当日激活比例: 7.0%（14/201）
2025-05-08 08:00:03,665 - INFO - 样本订单日期检查 (共5个): 201，熟龄度分布: 当天=14, 昨天=116, 前天=34, 更早=37
2025-05-08 08:00:03,665 - INFO - 开始发送模板卡片消息到企业微信...
2025-05-08 08:00:03,665 - INFO - 开始发送消息到企业微信（全渠道生产环境）...
2025-05-08 08:00:03,912 - INFO - 企业微信API响应状态码: 200
2025-05-08 08:00:03,912 - INFO - 企业微信消息发送成功
2025-05-08 08:00:03,912 - INFO - 昨天的日报发送成功
2025-05-08 08:00:03,913 - INFO - 昨天的日报处理完成。
2025-05-08 08:00:03,913 - INFO - ==================================================
