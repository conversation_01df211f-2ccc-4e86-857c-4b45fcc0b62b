import pymysql
import json
import datetime
import pytz
import logging
import os
import config  # 导入配置文件
from ftplib import FTP  # 添加FTP库

# 配置日志
log_dir = "logs"
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

log_file = os.path.join(log_dir, f"daily_report_{datetime.datetime.now().strftime('%Y%m%d')}.log")
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file, encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 从配置文件获取当前环境的配置
MYSQL_CONFIG = {**config.get_current_db_config(), "cursorclass": pymysql.cursors.DictCursor}
# 获取FTP配置
FTP_CONFIG = config.get_ftp_config()

def get_order_data_by_createtime(date):
    """
    获取指定日期的订单数据（使用下单时间createTime维度）
    """
    logger.info(f"获取 {date} 的订单数据（下单时间维度）...")
    
    start_time = f"{date} 00:00:00"
    end_time = f"{date} 23:59:59"
    
    # 修改SQL查询，添加与fzsrb.py相同的去重逻辑
    query = """
    WITH RankedOrders AS (
    SELECT 
        f.orderNo,
        f.target,
        cc.description AS channel_description,
        pc.card_typ,
        pc.productName_alias,
        f.businessStatus,
        f.outStatus,
        f.send_goods_result_Alias,
        f.createTime,
        f.updateTime,
        f.logisticsStatus,
        ROW_NUMBER() OVER (PARTITION BY f.target, pc.productName_alias ORDER BY f.createTime) as rn
    FROM 
        forders f
    LEFT JOIN 
        channel_config cc ON f.channel_config = cc.id
    LEFT JOIN 
        phone_card pc ON f.Phone_Card = pc.skuCode
    WHERE
        f.createTime BETWEEN %s AND %s
        AND f.logisticsStatus IN ('发货中', '发货成功')
    )
    SELECT * FROM RankedOrders WHERE rn = 1
    """
    
    try:
        conn = pymysql.connect(**MYSQL_CONFIG)
        with conn.cursor() as cursor:
            cursor.execute(query, (start_time, end_time))
            data = cursor.fetchall()
        conn.close()
        
        formatted_data = []
        for row in data:
            formatted_row = {
                "orderNo": row["orderNo"],
                "target": row["target"],
                "channel_description": row["channel_description"] or "未知渠道",
                "card_typ": row["card_typ"] or "其他卡",
                "productName_alias": row["productName_alias"] or "未知产品",
                "businessStatus": row["businessStatus"],
                "outStatus": row["outStatus"],
                "send_goods_result_Alias": row["send_goods_result_Alias"],
                "logisticsStatus": row["logisticsStatus"],
                "createTime": row["createTime"].isoformat() if isinstance(row["createTime"], datetime.datetime) else row["createTime"],
                "updateTime": row["updateTime"].isoformat() if isinstance(row["updateTime"], datetime.datetime) else row["updateTime"]
            }
            formatted_data.append(formatted_row)
        
        logger.info(f"成功获取到 {len(formatted_data)} 条去重后的订单数据（下单时间维度）")
        return formatted_data
    except Exception as e:
        logger.exception(f"获取订单数据（下单时间维度）时出错: {e}")
        return []

def get_weekly_orders_trend(end_date=None):
    """
    获取截至指定日期（默认为当前日期）的前7天下单数据趋势
    返回每日下单数据统计
    """
    logger.info("获取周订单趋势数据...")
    
    # 设置默认结束日期为昨天
    if not end_date:
        china_tz = pytz.timezone('Asia/Shanghai')
        yesterday = (datetime.datetime.now(china_tz) - datetime.timedelta(days=1)).date()
        end_date = yesterday
    elif isinstance(end_date, str):
        end_date = datetime.datetime.strptime(end_date, "%Y-%m-%d").date()
    
    # 计算开始日期（结束日期前推6天，共7天数据）
    start_date = end_date - datetime.timedelta(days=6)
    
    # SQL查询，按日期分组获取订单数量
    query = """
    WITH DailyOrders AS (
    SELECT 
        DATE(f.createTime) as order_date,
        COUNT(DISTINCT CASE WHEN pc.card_typ = '骑手卡' THEN f.orderNo END) as rider_card_count,
        COUNT(DISTINCT CASE WHEN pc.card_typ != '骑手卡' OR pc.card_typ IS NULL THEN f.orderNo END) as data_card_count
    FROM 
        forders f
    LEFT JOIN 
        phone_card pc ON f.Phone_Card = pc.skuCode
    WHERE
        f.createTime BETWEEN %s AND %s
        AND f.logisticsStatus IN ('发货中', '发货成功')
    GROUP BY 
        DATE(f.createTime)
    )
    SELECT 
        order_date,
        rider_card_count,
        data_card_count,
        (rider_card_count + data_card_count) as total_count
    FROM 
        DailyOrders
    ORDER BY 
        order_date
    """
    
    try:
        # 转换为字符串日期
        start_date_str = start_date.strftime("%Y-%m-%d 00:00:00")
        end_date_str = end_date.strftime("%Y-%m-%d 23:59:59")
        
        logger.info(f"查询日期范围: {start_date_str} 至 {end_date_str}")
        
        conn = pymysql.connect(**MYSQL_CONFIG)
        with conn.cursor() as cursor:
            cursor.execute(query, (start_date_str, end_date_str))
            data = cursor.fetchall()
        conn.close()
        
        # 处理结果，确保有7天完整数据
        result = {}
        date_range = [start_date + datetime.timedelta(days=i) for i in range(7)]
        
        # 初始化结果字典
        for date in date_range:
            date_str = date.strftime("%Y-%m-%d")
            result[date_str] = {
                "date": date_str,
                "rider_card_count": 0,
                "data_card_count": 0,
                "total_count": 0
            }
        
        # 填充查询结果
        for row in data:
            date_str = row["order_date"].strftime("%Y-%m-%d")
            if date_str in result:
                result[date_str]["rider_card_count"] = row["rider_card_count"]
                result[date_str]["data_card_count"] = row["data_card_count"]
                result[date_str]["total_count"] = row["total_count"]
        
        # 转换为列表格式
        trend_data = [result[date.strftime("%Y-%m-%d")] for date in date_range]
        
        logger.info(f"成功获取到 {len(trend_data)} 天的订单趋势数据")
        return trend_data
    except Exception as e:
        logger.exception(f"获取周订单趋势数据时出错: {e}")
        return []

def get_day_over_day_comparison(current_date_str=None):
    """
    获取报表日期与前一日订单数据对比（环比分析）
    current_date_str: 报表日期，默认为昨天
    """
    logger.info("获取报表日期与前一日订单数据对比...")
    
    # 设置默认日期为昨天
    if not current_date_str:
        china_tz = pytz.timezone('Asia/Shanghai')
        yesterday = (datetime.datetime.now(china_tz) - datetime.timedelta(days=1)).date()
        current_date_str = yesterday.strftime("%Y-%m-%d")
    
    # 解析当前日期
    current_date = datetime.datetime.strptime(current_date_str, "%Y-%m-%d").date()
    
    # 计算前一天日期
    previous_date = current_date - datetime.timedelta(days=1)
    previous_date_str = previous_date.strftime("%Y-%m-%d")
    
    logger.info(f"对比日期: 报表日期={current_date_str}, 前一日={previous_date_str}")
    
    # 获取当日订单数据
    current_orders = get_order_data_by_createtime(current_date_str)
    
    # 获取前一日订单数据
    previous_orders = get_order_data_by_createtime(previous_date_str)
    
    # 当前日期订单统计
    current_stats = {
        "date": current_date_str,
        "total": len(current_orders),
        "rider_card": len([o for o in current_orders if o.get("card_typ") == "骑手卡"]),
        "data_card": len([o for o in current_orders if o.get("card_typ") != "骑手卡"])
    }
    
    # 前一日订单统计
    previous_stats = {
        "date": previous_date_str,
        "total": len(previous_orders),
        "rider_card": len([o for o in previous_orders if o.get("card_typ") == "骑手卡"]),
        "data_card": len([o for o in previous_orders if o.get("card_typ") != "骑手卡"])
    }
    
    # 计算环比变化
    change_stats = {
        "total_change": current_stats["total"] - previous_stats["total"],
        "total_change_percent": calculate_change_percent(current_stats["total"], previous_stats["total"]),
        "rider_card_change": current_stats["rider_card"] - previous_stats["rider_card"],
        "rider_card_change_percent": calculate_change_percent(current_stats["rider_card"], previous_stats["rider_card"]),
        "data_card_change": current_stats["data_card"] - previous_stats["data_card"],
        "data_card_change_percent": calculate_change_percent(current_stats["data_card"], previous_stats["data_card"])
    }
    
    # 按渠道统计环比变化
    current_by_channel = {}
    for order in current_orders:
        channel = order.get("channel_description", "未知渠道")
        if channel not in current_by_channel:
            current_by_channel[channel] = {
                "total": 0,
                "rider_card": 0,
                "data_card": 0
            }
        
        current_by_channel[channel]["total"] += 1
        if order.get("card_typ") == "骑手卡":
            current_by_channel[channel]["rider_card"] += 1
        else:
            current_by_channel[channel]["data_card"] += 1
    
    previous_by_channel = {}
    for order in previous_orders:
        channel = order.get("channel_description", "未知渠道")
        if channel not in previous_by_channel:
            previous_by_channel[channel] = {
                "total": 0,
                "rider_card": 0,
                "data_card": 0
            }
        
        previous_by_channel[channel]["total"] += 1
        if order.get("card_typ") == "骑手卡":
            previous_by_channel[channel]["rider_card"] += 1
        else:
            previous_by_channel[channel]["data_card"] += 1
    
    # 合并所有渠道
    all_channels = set(list(current_by_channel.keys()) + list(previous_by_channel.keys()))
    channel_comparison = []
    
    for channel in all_channels:
        current_channel = current_by_channel.get(channel, {"total": 0, "rider_card": 0, "data_card": 0})
        previous_channel = previous_by_channel.get(channel, {"total": 0, "rider_card": 0, "data_card": 0})
        
        channel_comparison.append({
            "channel": channel,
            "current": current_channel,
            "previous": previous_channel,
            "change": {
                "total": current_channel["total"] - previous_channel["total"],
                "total_percent": calculate_change_percent(current_channel["total"], previous_channel["total"]),
                "rider_card": current_channel["rider_card"] - previous_channel["rider_card"],
                "rider_card_percent": calculate_change_percent(current_channel["rider_card"], previous_channel["rider_card"]),
                "data_card": current_channel["data_card"] - previous_channel["data_card"],
                "data_card_percent": calculate_change_percent(current_channel["data_card"], previous_channel["data_card"])
            }
        })
    
    # 按变化幅度排序
    channel_comparison.sort(key=lambda x: abs(x["change"]["total"]), reverse=True)
    
    return {
        "current": current_stats,
        "previous": previous_stats,
        "change": change_stats,
        "channels": channel_comparison
    }

def calculate_change_percent(current, previous):
    """计算环比变化百分比"""
    if previous == 0:
        if current == 0:
            return 0
        else:
            return 100  # 从0增长到非0，返回100%
    return round((current - previous) / previous * 100, 1)

def process_order_data(data):
    """
    处理订单数据，提取统计信息
    """
    # 统计骑手卡和其他订单
    rider_card_orders = []
    other_orders = []
    
    # 记录有效订单
    valid_orders = []
    
    logger.info("开始分类并过滤订单数据...")
    for order in data:
        # 记录订单详情和状态用于调试
        logistics_status = order.get("logisticsStatus", "Unknown")
        
        # 下单时间维度，统计"发货中"和"发货成功"的订单
        if logistics_status in ["发货中", "发货成功"]:
            valid_orders.append(order)
            # 分类骑手卡和其他
            if order.get("card_typ") == "骑手卡":
                rider_card_orders.append(order)
            else:
                other_orders.append(order)
    
    logger.info(f"订单分类完成: 有效订单 {len(valid_orders)} 单，其中骑手卡 {len(rider_card_orders)} 单，流量卡 {len(other_orders)} 单")
    
    return {
        "total_valid_orders": len(valid_orders),
        "rider_card_orders": len(rider_card_orders),
        "other_orders": len(other_orders),
        "valid_orders": valid_orders
    }

def get_channel_statistics(data, date_str):
    """获取各渠道的D0下单和D1激活数据"""
    logger.info("计算各渠道的D0下单和D1激活数据...")
    
    # 获取指定日期的订单（D0订单）
    d0_orders = [order for order in data["valid_orders"] if order.get("logisticsStatus") in ["发货中", "发货成功"]]
    
    # 计算当日激活的订单
    try:
        # 直接查询数据库获取当天激活的订单数
        conn = pymysql.connect(**MYSQL_CONFIG)
        with conn.cursor() as cursor:
            # 使用日期比较查询指定日期激活的订单
            activation_query = """
            WITH RankedOrders AS (
            SELECT 
                f.orderNo,
                f.target,
                cc.description AS channel_description,
                pc.card_typ,
                pc.productName_alias,
                f.createTime,
                f.updateTime,
                ROW_NUMBER() OVER (PARTITION BY f.target, pc.productName_alias ORDER BY f.updateTime) as rn
            FROM 
                forders f
            LEFT JOIN 
                channel_config cc ON f.channel_config = cc.id
            LEFT JOIN 
                phone_card pc ON f.Phone_Card = pc.skuCode
            WHERE
                f.logisticsStatus = '发货成功'
                AND DATE(f.updateTime) = %s
            )
            SELECT * FROM RankedOrders WHERE rn = 1
            """
            cursor.execute(activation_query, (date_str,))
            activation_data = cursor.fetchall()
            
            # 处理激活数据
            d1_activations = []
            for row in activation_data:
                d1_activations.append({
                    "orderNo": row["orderNo"],
                    "channel_description": row["channel_description"] or "未知渠道",
                    "card_typ": row["card_typ"] or "其他卡",
                    "productName_alias": row["productName_alias"] or "未知产品",
                    "createTime": row["createTime"],
                    "updateTime": row["updateTime"]
                })
        conn.close()
        logger.info(f"成功获取到 {len(d1_activations)} 条当日激活订单")
    except Exception as e:
        logger.exception(f"获取当日激活数据时出错: {e}")
        # 如果数据库查询失败，则使用之前的方法作为备用
        d1_activations = []
        for order in data["valid_orders"]:
            if order.get("logisticsStatus") == "发货成功":
                update_time_str = order.get("updateTime")
                if isinstance(update_time_str, str):
                    try:
                        update_time = datetime.datetime.fromisoformat(update_time_str).date()
                        current_date = datetime.datetime.strptime(date_str, "%Y-%m-%d").date()
                        if update_time == current_date:
                            d1_activations.append(order)
                    except (ValueError, TypeError):
                        continue
        logger.info(f"备用方法：获取到 {len(d1_activations)} 条当日激活订单")
    
    # 按渠道统计D0下单数量
    channels_d0 = {}
    for order in d0_orders:
        channel = order.get("channel_description", "未知渠道")
        if channel not in channels_d0:
            channels_d0[channel] = 0
        channels_d0[channel] += 1
    
    # 按渠道统计D1激活数量
    channels_d1 = {}
    for order in d1_activations:
        channel = order.get("channel_description", "未知渠道")
        if channel not in channels_d1:
            channels_d1[channel] = 0
        channels_d1[channel] += 1
    
    # 合并渠道列表
    all_channels = set(list(channels_d0.keys()) + list(channels_d1.keys()))
    
    # 计算熟龄度分布 - 统计当日激活订单的下单时间分布
    activation_age_distribution = {
        "same_day": 0,      # 当天下单当天激活
        "one_day": 0,       # 昨天下单今天激活
        "two_days": 0,      # 前天下单今天激活
        "more_days": 0      # 更早下单今天激活
    }
    
    # 为骑手卡和流量卡分别创建熟龄度分布统计
    rider_card_activation_age = {
        "same_day": 0,
        "one_day": 0,
        "two_days": 0,
        "more_days": 0
    }

    data_card_activation_age = {
        "same_day": 0,
        "one_day": 0,
        "two_days": 0,
        "more_days": 0
    }

    current_date = datetime.datetime.strptime(date_str, "%Y-%m-%d").date()
    
    # 调试日志 - 记录几个样本订单的日期
    sample_size = min(5, len(d1_activations))
    if sample_size > 0:
        logger.info(f"样本订单日期检查 (共{sample_size}个):")
        for i in range(sample_size):
            order = d1_activations[i]
            try:
                if isinstance(order.get("createTime"), datetime.datetime):
                    create_date = order["createTime"].date()
                    create_date_str = create_date.strftime("%Y-%m-%d")
                else:
                    create_time_str = order.get("createTime")
                    if not create_time_str:
                        logger.warning(f"订单 {order.get('orderNo')} 缺少创建时间")
                        continue
                    create_date = datetime.datetime.fromisoformat(create_time_str).date()
                    create_date_str = create_date.strftime("%Y-%m-%d")
                
                if isinstance(order.get("updateTime"), datetime.datetime):
                    update_date = order["updateTime"].date()
                    update_date_str = update_date.strftime("%Y-%m-%d")
                else:
                    update_time_str = order.get("updateTime")
                    if not update_time_str:
                        logger.warning(f"订单 {order.get('orderNo')} 缺少更新时间")
                        continue
                    update_date = datetime.datetime.fromisoformat(update_time_str).date()
                    update_date_str = update_date.strftime("%Y-%m-%d")
                
                days_diff = (update_date - create_date).days
                logger.info(f"订单 {order.get('orderNo')}: 下单时间={create_date_str}, 激活时间={update_date_str}, 相差={days_diff}天")
            except Exception as e:
                logger.warning(f"处理样本订单 {order.get('orderNo')} 日期时出错: {e}")
    
    # 计算熟龄度
    processing_errors = 0
    processed_count = 0
    rider_card_count = 0
    data_card_count = 0
    
    for order in d1_activations:
        try:
            if isinstance(order.get("createTime"), datetime.datetime):
                create_date = order["createTime"].date()
            else:
                create_time_str = order.get("createTime")
                if not create_time_str:
                    processing_errors += 1
                    continue
                create_date = datetime.datetime.fromisoformat(create_time_str).date()
            
            days_diff = (current_date - create_date).days
            processed_count += 1
            
            # 判断卡类型
            is_rider_card = order.get("card_typ") == "骑手卡"
            
            # 输出更多调试信息
            if processed_count < 5:
                logger.debug(f"订单 {order.get('orderNo')}: 卡类型={order.get('card_typ')}, 是否为骑手卡={is_rider_card}")
            
            # 更新总体熟龄度分布
            if days_diff == 0:
                activation_age_distribution["same_day"] += 1
                if is_rider_card:
                    rider_card_activation_age["same_day"] += 1
                    rider_card_count += 1
                else:
                    data_card_activation_age["same_day"] += 1
                    data_card_count += 1
            elif days_diff == 1:
                activation_age_distribution["one_day"] += 1
                if is_rider_card:
                    rider_card_activation_age["one_day"] += 1
                    rider_card_count += 1
                else:
                    data_card_activation_age["one_day"] += 1
                    data_card_count += 1
            elif days_diff == 2:
                activation_age_distribution["two_days"] += 1
                if is_rider_card:
                    rider_card_activation_age["two_days"] += 1
                    rider_card_count += 1
                else:
                    data_card_activation_age["two_days"] += 1
                    data_card_count += 1
            else:
                activation_age_distribution["more_days"] += 1
                if is_rider_card:
                    rider_card_activation_age["more_days"] += 1
                    rider_card_count += 1
                else:
                    data_card_activation_age["more_days"] += 1
                    data_card_count += 1
                
        except (ValueError, TypeError, AttributeError) as e:
            logger.warning(f"处理激活订单熟龄度时出错: {e}")
            processing_errors += 1
            continue
    
    logger.info(f"熟龄度处理统计: 总订单={len(d1_activations)}, 成功处理={processed_count}, 处理失败={processing_errors}")
    logger.info(f"熟龄度分布: 当天={activation_age_distribution['same_day']}, 昨天={activation_age_distribution['one_day']}, 前天={activation_age_distribution['two_days']}, 更早={activation_age_distribution['more_days']}")
    logger.info(f"骑手卡熟龄度分布: 当天={rider_card_activation_age['same_day']}, 昨天={rider_card_activation_age['one_day']}, 前天={rider_card_activation_age['two_days']}, 更早={rider_card_activation_age['more_days']}")
    logger.info(f"流量卡熟龄度分布: 当天={data_card_activation_age['same_day']}, 昨天={data_card_activation_age['one_day']}, 前天={data_card_activation_age['two_days']}, 更早={data_card_activation_age['more_days']}")
    
    # 生成渠道统计数据
    channel_list = []
    for channel in all_channels:
        d0_count = channels_d0.get(channel, 0)
        d1_count = channels_d1.get(channel, 0)
        
        channel_list.append({
            "name": channel,
            "d0Orders": d0_count,
            "d1Activations": d1_count
        })
    
    # 按D0下单数量排序
    channel_list.sort(key=lambda x: x["d0Orders"], reverse=True)
    
    # 计算总计
    total_d0 = sum(item["d0Orders"] for item in channel_list)
    total_d1 = sum(item["d1Activations"] for item in channel_list)
    
    # 计算各卡类在各渠道的数量 - 当日下单
    card_types_by_channel_d0 = {}
    for order in d0_orders:
        channel = order.get("channel_description", "未知渠道")
        card_type = order.get("card_typ", "未知卡类型")
        product_name = order.get("productName_alias", "未知产品")
        
        if channel not in card_types_by_channel_d0:
            card_types_by_channel_d0[channel] = {}
        
        product_key = f"{card_type}-{product_name}"
        if product_key not in card_types_by_channel_d0[channel]:
            card_types_by_channel_d0[channel][product_key] = 0
        
        card_types_by_channel_d0[channel][product_key] += 1
    
    # 计算各卡类在各渠道的数量 - 当日激活
    card_types_by_channel_d1 = {}
    for order in d1_activations:
        channel = order.get("channel_description", "未知渠道")
        card_type = order.get("card_typ", "未知卡类型")
        product_name = order.get("productName_alias", "未知产品")
        
        if channel not in card_types_by_channel_d1:
            card_types_by_channel_d1[channel] = {}
        
        product_key = f"{card_type}-{product_name}"
        if product_key not in card_types_by_channel_d1[channel]:
            card_types_by_channel_d1[channel][product_key] = 0
        
        card_types_by_channel_d1[channel][product_key] += 1
    
    # 添加卡类和产品明细到渠道数据中 - 当日下单
    for channel_data in channel_list:
        channel_name = channel_data["name"]
        # 当日下单产品明细
        if channel_name in card_types_by_channel_d0:
            # 排序产品，按数量从多到少
            sorted_products = sorted(
                card_types_by_channel_d0[channel_name].items(),
                key=lambda x: x[1],
                reverse=True
            )
            channel_data["products_d0"] = [
                {"name": product[0], "count": product[1]}
                for product in sorted_products
            ]
        else:
            channel_data["products_d0"] = []
            
        # 当日激活产品明细
        if channel_name in card_types_by_channel_d1:
            # 排序产品，按数量从多到少
            sorted_products = sorted(
                card_types_by_channel_d1[channel_name].items(),
                key=lambda x: x[1],
                reverse=True
            )
            channel_data["products_d1"] = [
                {"name": product[0], "count": product[1]}
                for product in sorted_products
            ]
        else:
            channel_data["products_d1"] = []
    
    return {
        "channels": channel_list,
        "totals": {
            "totalD0": total_d0,
            "totalD1": total_d1
        },
        "activation_age": activation_age_distribution,
        "rider_card_activation_age": rider_card_activation_age,
        "data_card_activation_age": data_card_activation_age,
        "rider_card_count": rider_card_count,
        "data_card_count": data_card_count,
        "card_types_by_channel_d0": card_types_by_channel_d0,
        "card_types_by_channel_d1": card_types_by_channel_d1
    }

def generate_report_data(date_str=None):
    """
    生成报表数据
    date_str: 报表日期，格式为YYYY-MM-DD，默认为昨天
    """
    # 设置默认日期为昨天
    if not date_str:
        china_tz = pytz.timezone('Asia/Shanghai')
        yesterday = (datetime.datetime.now(china_tz) - datetime.timedelta(days=1)).date()
        date_str = yesterday.strftime("%Y-%m-%d")
    
    logger.info(f"开始生成 {date_str} 的报表数据...")
    
    # 获取下单时间维度的订单数据
    order_data_list = get_order_data_by_createtime(date_str)
    
    if not order_data_list:
        logger.warning(f"未获取到 {date_str} 的订单数据，取消生成报表")
        return None
    
    # 处理订单数据
    order_stats = process_order_data(order_data_list)
    
    # 获取渠道统计
    channel_stats = get_channel_statistics(order_stats, date_str)
    
    # 获取环比分析数据
    day_over_day_data = get_day_over_day_comparison(date_str)
    
    # 获取一周订单趋势数据
    weekly_trend_data = get_weekly_orders_trend(date_str)
    
    # 构建报表数据
    report_data = {
        "reportDate": date_str,
        "summary": {
            "totalOrders": order_stats["total_valid_orders"],
            "riderCardOrders": order_stats["rider_card_orders"],
            "dataCardOrders": order_stats["other_orders"]
        },
        "channels": channel_stats["channels"],
        "totals": channel_stats["totals"],
        "activation_age": channel_stats["activation_age"],  # 添加熟龄度数据
        "rider_card_activation_age": channel_stats["rider_card_activation_age"],  # 添加骑手卡熟龄度数据
        "data_card_activation_age": channel_stats["data_card_activation_age"],  # 添加流量卡熟龄度数据
        "rider_card_count": channel_stats["rider_card_count"],  # 添加骑手卡总数
        "data_card_count": channel_stats["data_card_count"],  # 添加流量卡总数
        "day_over_day": day_over_day_data,  # 添加环比分析数据
        "weekly_trend": weekly_trend_data  # 添加周订单趋势数据
    }
    
    # 打印调试信息
    logger.info(f"熟龄度数据: {channel_stats['activation_age']}")
    logger.info(f"骑手卡熟龄度数据: {channel_stats['rider_card_activation_age']}, 总数: {channel_stats['rider_card_count']}")
    logger.info(f"流量卡熟龄度数据: {channel_stats['data_card_activation_age']}, 总数: {channel_stats['data_card_count']}")
    
    total_activations = sum(channel_stats["activation_age"].values())
    if total_activations > 0:
        same_day_percent = round((channel_stats["activation_age"]["same_day"] / total_activations * 100), 1)
        logger.info(f"当日下单当日激活比例: {same_day_percent}%（{channel_stats['activation_age']['same_day']}/{total_activations}）")
        logger.info(f"样本订单日期检查 (共5个): {total_activations}，熟龄度分布: 当天={channel_stats['activation_age']['same_day']}, 昨天={channel_stats['activation_age']['one_day']}, 前天={channel_stats['activation_age']['two_days']}, 更早={channel_stats['activation_age']['more_days']}")
    
    # 输出环比分析和周趋势数据调试信息
    logger.info(f"环比分析: 当前={day_over_day_data['current']['total']}单, 前一日={day_over_day_data['previous']['total']}单, 变化={day_over_day_data['change']['total_change_percent']}%")
    if weekly_trend_data:
        logger.info(f"周趋势数据获取成功，共{len(weekly_trend_data)}天数据")
    else:
        logger.info("未获取到周趋势数据")
    
    return report_data

def generate_html_report(report_data, output_file=None):
    """生成HTML报表（重构版，所有表格和JS数据提前拼好，模板无复杂表达式）"""
    import json
    import datetime
    import os
    # 1. 输出文件名
    if not output_file:
        report_date = report_data["reportDate"].replace("-", "")
        reports_dir = "reports"
        if not os.path.exists(reports_dir):
            os.makedirs(reports_dir)
        output_file = os.path.join(reports_dir, f"report_{report_date}.html")
    date_obj = datetime.datetime.strptime(report_data["reportDate"], "%Y-%m-%d")
    month = date_obj.month
    day = date_obj.day

    # 保留原有的变量定义
    # 准备渠道数据，用于生成HTML表格和图表
    channels_html = ""
    channel_names = []
    d0_orders_data = []
    d1_activations_data = []
    
    # 用于存储每个渠道的产品明细
    channels_products = {}
    
    # 处理渠道数据
    for index, channel in enumerate(report_data["channels"]):
        # 收集图表数据
        channel_names.append(channel["name"])
        d0_orders_data.append(channel["d0Orders"])
        d1_activations_data.append(channel["d1Activations"])
        
        # 准备产品明细，处理当日下单和当日激活的数据
        product_details = {
            "d0": {"骑手卡": [], "流量卡": []},
            "d1": {"骑手卡": [], "流量卡": []}
        }
        
        # 处理当日下单数据
        if "products_d0" in channel and channel["products_d0"]:
            for product in channel["products_d0"]:
                # 提取产品信息，不包含卡类型
                product_parts = product["name"].split('-')
                if len(product_parts) > 1:
                    card_type = product_parts[0].strip()
                    product_info = product_parts[1].strip()  # 只取"-"后面的产品名称
                else:
                    card_type = "其他卡"
                    product_info = product["name"]  # 如果没有"-"，则使用原名
                
                product_count = product["count"]
                product_str = f"{product_info}×{product_count}"
                
                # 分类保存
                if "骑手卡" in card_type:
                    product_details["d0"]["骑手卡"].append(product_str)
                else:
                    product_details["d0"]["流量卡"].append(product_str)
        
        # 处理当日激活数据
        if "products_d1" in channel and channel["products_d1"]:
            for product in channel["products_d1"]:
                # 提取产品信息，不包含卡类型
                product_parts = product["name"].split('-')
                if len(product_parts) > 1:
                    card_type = product_parts[0].strip()
                    product_info = product_parts[1].strip()  # 只取"-"后面的产品名称
                else:
                    card_type = "其他卡"
                    product_info = product["name"]  # 如果没有"-"，则使用原名
                
                product_count = product["count"]
                product_str = f"{product_info}×{product_count}"
                
                # 分类保存
                if "骑手卡" in card_type:
                    product_details["d1"]["骑手卡"].append(product_str)
                else:
                    product_details["d1"]["流量卡"].append(product_str)
        
        channels_products[channel["name"]] = product_details
        
        # 生成表格行HTML
        channels_html += f"""
        <tr>
            <td>{channel["name"]}</td>
            <td style="text-align:center">{channel["d0Orders"]}</td>
            <td style="text-align:center">{channel["d1Activations"]}</td>
            <td style="text-align:center"><button class="detail-btn" onclick="showDetails('channel-{index}')">明细</button></td>
        </tr>
        """
    
    # 转换产品明细为JSON以在JavaScript中使用
    channels_products_js = json.dumps(channels_products)
    
    # 准备激活熟龄度数据
    activation_age = report_data.get("activation_age", {"same_day": 0, "one_day": 0, "two_days": 0, "more_days": 0})
    total_activations = sum(activation_age.values())
    
    # 获取骑手卡和流量卡的熟龄度数据
    rider_card_activation_age = report_data.get("rider_card_activation_age", {"same_day": 0, "one_day": 0, "two_days": 0, "more_days": 0})
    data_card_activation_age = report_data.get("data_card_activation_age", {"same_day": 0, "one_day": 0, "two_days": 0, "more_days": 0})
    rider_card_count = report_data.get("rider_card_count", 0)
    data_card_count = report_data.get("data_card_count", 0)
    
    # 计算百分比
    same_day_percent = round((activation_age["same_day"] / total_activations * 100), 1) if total_activations > 0 else 0
    one_day_percent = round((activation_age["one_day"] / total_activations * 100), 1) if total_activations > 0 else 0
    two_days_percent = round((activation_age["two_days"] / total_activations * 100), 1) if total_activations > 0 else 0
    more_days_percent = round((activation_age["more_days"] / total_activations * 100), 1) if total_activations > 0 else 0
    
    # 骑手卡百分比
    rider_same_day_percent = round((rider_card_activation_age["same_day"] / rider_card_count * 100), 1) if rider_card_count > 0 else 0
    rider_one_day_percent = round((rider_card_activation_age["one_day"] / rider_card_count * 100), 1) if rider_card_count > 0 else 0
    rider_two_days_percent = round((rider_card_activation_age["two_days"] / rider_card_count * 100), 1) if rider_card_count > 0 else 0
    rider_more_days_percent = round((rider_card_activation_age["more_days"] / rider_card_count * 100), 1) if rider_card_count > 0 else 0
    
    # 流量卡百分比
    data_same_day_percent = round((data_card_activation_age["same_day"] / data_card_count * 100), 1) if data_card_count > 0 else 0
    data_one_day_percent = round((data_card_activation_age["one_day"] / data_card_count * 100), 1) if data_card_count > 0 else 0
    data_two_days_percent = round((data_card_activation_age["two_days"] / data_card_count * 100), 1) if data_card_count > 0 else 0
    data_more_days_percent = round((data_card_activation_age["more_days"] / data_card_count * 100), 1) if data_card_count > 0 else 0

    # 计算近三日激活量占比（正确的计算方式）
    rider_recent_three_days = rider_card_activation_age["same_day"] + rider_card_activation_age["one_day"] + rider_card_activation_age["two_days"]
    rider_recent_three_days_percent = round(rider_recent_three_days / rider_card_count * 100, 1) if rider_card_count > 0 else 0

    data_recent_three_days = data_card_activation_age["same_day"] + data_card_activation_age["one_day"] + data_card_activation_age["two_days"]
    data_recent_three_days_percent = round(data_recent_three_days / data_card_count * 100, 1) if data_card_count > 0 else 0
    
    # 将Python列表转换为JavaScript数组字符串
    channel_names_js = json.dumps(channel_names)
    d0_orders_data_js = json.dumps(d0_orders_data)
    d1_activations_data_js = json.dumps(d1_activations_data)
    
    # 激活熟龄度数据
    age_labels_js = json.dumps(["当天", "昨天", "前天", "更早"])
    age_data_js = json.dumps([activation_age["same_day"], activation_age["one_day"], activation_age["two_days"], activation_age["more_days"]])
    age_percent_js = json.dumps([same_day_percent, one_day_percent, two_days_percent, more_days_percent])
    
    # 骑手卡熟龄度数据
    rider_age_data_js = json.dumps([rider_card_activation_age["same_day"], rider_card_activation_age["one_day"], rider_card_activation_age["two_days"], rider_card_activation_age["more_days"]])
    rider_age_percent_js = json.dumps([rider_same_day_percent, rider_one_day_percent, rider_two_days_percent, rider_more_days_percent])
    
    # 流量卡熟龄度数据
    data_age_data_js = json.dumps([data_card_activation_age["same_day"], data_card_activation_age["one_day"], data_card_activation_age["two_days"], data_card_activation_age["more_days"]])
    data_age_percent_js = json.dumps([data_same_day_percent, data_one_day_percent, data_two_days_percent, data_more_days_percent])
    
    # 检查数据是否成功加载到报表
    logger.info(f"生成HTML报表模板，加载数据: 总活跃={total_activations}, 骑手卡={rider_card_count}, 流量卡={data_card_count}")
    logger.info(f"骑手卡数据: {rider_age_data_js}")
    logger.info(f"流量卡数据: {data_age_data_js}")

    # 2. 渠道环比表格
    channel_rows = ""
    for i, channel in enumerate(report_data["day_over_day"]["channels"][:10]):
        change = channel["change"]
        channel_rows += f'''
            <tr>
                <td>{channel["channel"]}</td>
                <td style="text-align:center">{channel["current"]["total"]}</td>
                <td style="text-align:center">{channel["previous"]["total"]}</td>
                <td style="text-align:center">{'+' if change["total"] >= 0 else ''}{change["total"]}</td>
                <td class="{'positive' if change["total_percent"] >= 0 else 'negative'}" style="text-align:center">{'+' if change["total_percent"] >= 0 else ''}{change["total_percent"]}%</td>
            </tr>
        '''
    # 3. 周趋势表格
    weekly_rows = ""
    for i, day_item in enumerate(report_data["weekly_trend"]):
        weekly_rows += f'''
            <tr>
                <td>{day_item["date"]}</td>
                <td style="text-align:center">{day_item["total_count"]}</td>
                <td style="text-align:center">{day_item["rider_card_count"]}</td>
                <td style="text-align:center">{day_item["data_card_count"]}</td>
            </tr>
        '''
    # 4. JS数据
    weekly_dates = json.dumps([day["date"] for day in report_data["weekly_trend"]])
    weekly_total = json.dumps([day["total_count"] for day in report_data["weekly_trend"]])
    weekly_rider = json.dumps([day["rider_card_count"] for day in report_data["weekly_trend"]])
    weekly_data = json.dumps([day["data_card_count"] for day in report_data["weekly_trend"]])
    comparison_labels = json.dumps(["总订单", "骑手卡", "流量卡"])
    current_data = json.dumps([
        report_data["day_over_day"]["current"]["total"],
        report_data["day_over_day"]["current"]["rider_card"],
        report_data["day_over_day"]["current"]["data_card"]
    ])
    previous_data = json.dumps([
        report_data["day_over_day"]["previous"]["total"],
        report_data["day_over_day"]["previous"]["rider_card"],
        report_data["day_over_day"]["previous"]["data_card"]
    ])

    html_template = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{month}月{day}日报表</title>
    <script src="https://b.zhoumeiren.cn/chart.min.js"></script>
    <style>
        body {{
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 15px;
            background-color: #f5f5f5;
            color: #333;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }}
        h1, h2, h3 {{
            color: #333;
            margin-top: 0;
        }}
        h1 {{
            font-size: 1.8rem;
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }}
        h2 {{
            font-size: 1.5rem;
            margin-top: 25px;
            padding-bottom: 8px;
            border-bottom: 1px solid #eee;
        }}
        .card {{
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            padding: 15px;
            margin-bottom: 25px;
            border: 1px solid #eee;
        }}
        .row {{
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 25px;
        }}
        .col {{
            flex: 1;
            min-width: 300px;
        }}
        .summary {{
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 25px;
        }}
        .summary-card {{
            flex: 1 1 200px;
            background: linear-gradient(135deg, #6A82FB, #FC5C7D);
            color: white;
            border-radius: 8px;
            padding: 18px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.15);
            transition: transform 0.3s ease;
        }}
        .summary-card:hover {{
            transform: translateY(-3px);
        }}
        .summary-card:nth-child(2) {{
            background: linear-gradient(135deg, #11998e, #38ef7d);
        }}
        .summary-card:nth-child(3) {{
            background: linear-gradient(135deg, #FF8008, #FFC837);
        }}
        .summary-card h3 {{
            margin: 0;
            font-size: 1.1rem;
            color: rgba(255,255,255,0.9);
        }}
        .summary-card p {{
            margin: 12px 0 0;
            font-size: 2rem;
            font-weight: bold;
        }}
        .summary-card .subtitle {{
            font-size: 0.9rem;
            margin-top: 8px;
            opacity: 0.9;
        }}
        
        /* 序号样式 */
        .num-cell {{
            background-color: #f0f2f5;
            color: #666;
            font-weight: bold;
            border-radius: 50%;
            width: 28px;
            height: 28px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-right: 5px;
        }}
        
        /* 数字标记样式 */
        .num-badge {{
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 30px;
            height: 30px;
            margin-right: 10px;
            background-color: #6A82FB;
            color: white;
            border-radius: 50%;
            font-size: 18px;
            font-weight: bold;
            line-height: 1;
        }}
        .chart-container {{
            position: relative;
            height: 400px;
            margin: 20px 0;
        }}
        .chart-container.half-height {{
            height: 300px;
        }}
        table {{
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            font-size: 0.9rem;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            border-radius: 4px;
            overflow: hidden;
        }}
        th, td {{
            padding: 10px 8px;
            text-align: left;
            border-bottom: 1px solid #e0e0e0;
        }}
        th {{
            background-color: #f0f2f5;
            font-weight: 600;
            color: #444;
        }}
        tr:nth-child(even) {{
            background-color: #f9f9f9;
        }}
        tr:hover {{
            background-color: #f0f4ff;
        }}
        tfoot tr {{
            background-color: #f0f2f5;
            font-weight: bold;
        }}
        .positive {{
            background-color: rgba(40, 167, 69, 0.1);
            color: #28a745;
            border-radius: 3px;
            padding: 2px 6px;
            font-weight: bold;
        }}
        .negative {{
            background-color: rgba(220, 53, 69, 0.1);
            color: #dc3545;
            border-radius: 3px;
            padding: 2px 6px;
            font-weight: bold;
        }}
        .age-stat {{
            display: flex;
            margin: 10px 0;
            border-radius: 4px;
            overflow: hidden;
            min-height: 30px;
        }}
        .age-stat-item {{
            padding: 8px 4px;
            text-align: center;
            color: white;
            font-size: 0.9rem;
            min-width: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
        }}
        .age-stat-labels {{
            display: flex;
            margin-top: 5px;
            font-size: 0.85rem;
        }}
        .age-stat-label {{
            text-align: center;
            padding: 0 2px;
            white-space: nowrap;
            overflow: hidden;
            min-width: 30px;
        }}
        .explanation {{
            margin: 15px 0;
            padding: 12px;
            background-color: #f8f9fa;
            border-left: 3px solid #6A82FB;
            font-size: 0.9rem;
            line-height: 1.5;
        }}
        .footnote {{
            font-size: 0.8rem;
            color: #777;
            text-align: center;
            margin-top: 30px;
            padding-top: 10px;
            border-top: 1px solid #eee;
        }}
        .detail-btn {{
            background-color: #6A82FB;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 5px 10px;
            font-size: 0.8rem;
            cursor: pointer;
        }}
        .detail-btn:hover {{
            background-color: #5A72EB;
        }}
        .modal {{
            display: none;
            position: fixed;
            z-index: 100;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.4);
        }}
        .modal-content {{
            background-color: #fff;
            margin: 15% auto;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            width: 80%;
            max-width: 500px;
        }}
        .modal-title {{
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }}
        .product-category {{
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #f0f0f0;
        }}
        .product-category:last-child {{
            border-bottom: none;
            padding-bottom: 0;
        }}
        .product-category-title {{
            font-weight: bold;
            margin-bottom: 5px;
        }}
        .product-list {{
            line-height: 1.6;
        }}
        .close {{
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }}
        .close:hover {{
            color: #000;
        }}
        
        /* 手机端适配 */
        @media (max-width: 768px) {{
            body {{
                padding: 10px;
            }}
            .container {{
                padding: 15px;
            }}
            h1 {{
                font-size: 1.5rem;
            }}
            h2 {{
                font-size: 1.3rem;
            }}
            .summary-card {{
                flex: 1 1 100%;
            }}
            .chart-container {{
                height: 300px;
            }}
            .col {{
                flex: 1 1 100%;
            }}
            table {{
                font-size: 0.8rem;
            }}
            th, td {{
                padding: 8px 3px;
            }}
            .age-stat {{
                height: 40px;
            }}
            .age-stat-item {{
                font-size: 0.8rem;
                min-width: 30px;
            }}
            .age-stat-labels {{
                display: flex;
                margin-top: 10px;
            }}
            .age-stat-label {{
                text-align: center;
                font-size: 0.8rem;
                padding: 0;
            }}
            .modal-content {{
                width: 90%;
                margin: 30% auto;
            }}
            .row{{
                margin-bottom: 10px;
            }}
        }}
        .product-section {{
            margin-bottom: 20px;
        }}
        .product-section h3 {{
            font-size: 1rem;
            margin-bottom: 10px;
            color: #333;
            font-weight: bold;
        }}
        .mt-20 {{
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }}
        .chart-filter .filter-btn {{
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            color: #333;
            padding: 6px 12px;
            margin-right: 5px;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
        }}
        
        .chart-filter .filter-btn.active {{
            background-color: #4bc0c0;
            color: white;
            border-color: #4bc0c0;
        }}
        
        .chart-filter .filter-btn:hover {{
            background-color: #e9ecef;
        }}
        
        .chart-filter .filter-btn.active:hover {{
            background-color: #3aafaf;
        }}
        
        /* 环比分析样式 */
        .comparison-summary {{
            margin: 15px 0;
        }}
        .comparison-card {{
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }}
        .comparison-title {{
            font-weight: bold;
            font-size: 1rem;
            margin-bottom: 10px;
            color: #333;
        }}
        .comparison-values {{
            display: flex;
            align-items: center;
            gap: 15px;
        }}
        .current-value {{
            font-size: 1.6rem;
            font-weight: bold;
            color: #4bc0c0;
        }}
        .previous-value {{
            font-size: 1.2rem;
            color: #777;
            position: relative;
        }}
        .previous-value:after {{
            content: "";
            position: absolute;
            left: -5px;
            right: -5px;
            top: 50%;
            height: 1px;
            background-color: #ddd;
        }}
        .change-value {{
            font-size: 1rem;
            padding: 2px 8px;
            border-radius: 4px;
        }}
        .positive {{
            color: #28a745;
            background-color: rgba(40, 167, 69, 0.1);
        }}
        .negative {{
            color: #dc3545;
            background-color: rgba(220, 53, 69, 0.1);
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1>{month}月{day}日报:订单与激活一览</h1>
        
        <div class="summary">
            <div class="summary-card">
                <h3>总订单量</h3>
                <p>{report_data["day_over_day"]["current"]["total"]}</p>
                <div class="subtitle">环比昨日：{'+' if report_data["day_over_day"]["change"]["total_change"] >= 0 else ''}{report_data["day_over_day"]["change"]["total_change"]} ({'+' if report_data["day_over_day"]["change"]["total_change_percent"] >= 0 else ''}{report_data["day_over_day"]["change"]["total_change_percent"]}%)</div>
            </div>
            <div class="summary-card">
                <h3>骑手卡</h3>
                <p>{report_data["day_over_day"]["current"]["rider_card"]}</p>
                <div class="subtitle">环比昨日：{'+' if report_data["day_over_day"]["change"]["rider_card_change"] >= 0 else ''}{report_data["day_over_day"]["change"]["rider_card_change"]} ({'+' if report_data["day_over_day"]["change"]["rider_card_change_percent"] >= 0 else ''}{report_data["day_over_day"]["change"]["rider_card_change_percent"]}%)</div>
            </div>
            <div class="summary-card">
                <h3>流量卡</h3>
                <p>{report_data["day_over_day"]["current"]["data_card"]}</p>
                <div class="subtitle">环比昨日：{'+' if report_data["day_over_day"]["change"]["data_card_change"] >= 0 else ''}{report_data["day_over_day"]["change"]["data_card_change"]} ({'+' if report_data["day_over_day"]["change"]["data_card_change_percent"] >= 0 else ''}{report_data["day_over_day"]["change"]["data_card_change_percent"]}%)</div>
            </div>
        </div>
        
        <!-- 环比分析部分 -->
        <div class="card" style="margin-top: 20px;">
            <h2><span class="num-badge">1</span> 订单环比（当日 VS 昨日）</h2>
            <div class="chart-container" style="height: 300px; margin-top: 20px;">
                <canvas id="comparisonChart"></canvas>
            </div>
            
            <div style="margin-top: 20px;">
                <h3>各渠道环比变化</h3>
                <table>
                    <thead>
                        <tr>
                            <th>渠道</th>
                            <th style="text-align:center">当日订单</th>
                            <th style="text-align:center">昨日订单</th>
                            <th style="text-align:center">变化</th>
                            <th style="text-align:center">变化率</th>
                        </tr>
                    </thead>
                    <tbody>
                        {channel_rows}
                    </tbody>
                </table>
            </div>
        </div>
        
        <div class="row">
            <div class="col">
                <div class="card">
                    <h2><span class="num-badge">2</span> 渠道订单与激活数据</h2>
                    <div class="chart-container">
                        <canvas id="channelChart"></canvas>
                    </div>
                    <table>
                        <thead>
                            <tr>
                                <th>渠道</th>
                                <th style="text-align:center">当日下单</th>
                                <th style="text-align:center">当日激活</th>
                                <th style="text-align:center">产品明细</th>
                            </tr>
                        </thead>
                        <tbody>
                            {channels_html}
                        </tbody>
                        <tfoot>
                            <tr>
                                <th style="text-align:center">合计</th>
                                <th style="text-align:center">{report_data["totals"]["totalD0"]}</th>
                                <th style="text-align:center">{report_data["totals"]["totalD1"]}</th>
                                <th></th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
            
            <div class="col">
                <div class="card">
                    <h2><span class="num-badge">3</span> 当日激活订单的熟龄度分布</h2>
                    <div class="chart-filter" style="margin-bottom: 15px; text-align: center;">
                        <button class="filter-btn active" data-type="all" onclick="switchAgeChart('all')">全部</button>
                        <button class="filter-btn" data-type="rider" onclick="switchAgeChart('rider')">骑手卡</button>
                        <button class="filter-btn" data-type="data" onclick="switchAgeChart('data')">流量卡</button>
                    </div>
                    <div class="chart-container half-height">
                        <canvas id="ageDistributionChart"></canvas>
                    </div>
                    
                    <div>
                        <h3>当日激活订单的下单时间分布 <span id="ageDistributionTitle">(全部)</span></h3>
                        <div class="age-stat" id="ageStatBar">
                            <div class="age-stat-item" style="background-color: #36a2eb; width: {max(same_day_percent, 2)}%">
                                {same_day_percent}%
                            </div>
                            <div class="age-stat-item" style="background-color: #4bc0c0; width: {max(one_day_percent, 2)}%">
                                {one_day_percent}%
                            </div>
                            <div class="age-stat-item" style="background-color: #ffcd56; width: {max(two_days_percent, 2)}%">
                                {two_days_percent}%
                            </div>
                            <div class="age-stat-item" style="background-color: #ff9f40; width: {max(more_days_percent, 2)}%">
                                {more_days_percent}%
                            </div>
                        </div>
                        <div class="age-stat-labels">
                            <div class="age-stat-label" style="width: {max(same_day_percent, 2)}%">当天</div>
                            <div class="age-stat-label" style="width: {max(one_day_percent, 2)}%">昨天</div>
                            <div class="age-stat-label" style="width: {max(two_days_percent, 2)}%">前天</div>
                            <div class="age-stat-label" style="width: {max(more_days_percent, 2)}%">更早</div>
                        </div>
                        
                        <table id="ageDistributionTable" style="margin-top: 20px;">
                            <thead>
                                <tr>
                                    <th>下单时间</th>
                                    <th style="text-align:center">订单数</th>
                                    <th style="text-align:center">占比</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>当天</td>
                                    <td style="text-align:center">{activation_age["same_day"]}</td>
                                    <td style="text-align:center">{same_day_percent}%</td>
                                </tr>
                                <tr>
                                    <td>昨天</td>
                                    <td style="text-align:center">{activation_age["one_day"]}</td>
                                    <td style="text-align:center">{one_day_percent}%</td>
                                </tr>
                                <tr>
                                    <td>前天</td>
                                    <td style="text-align:center">{activation_age["two_days"]}</td>
                                    <td style="text-align:center">{two_days_percent}%</td>
                                </tr>
                                <tr>
                                    <td>更早</td>
                                    <td style="text-align:center">{activation_age["more_days"]}</td>
                                    <td style="text-align:center">{more_days_percent}%</td>
                                </tr>
                            </tbody>
                            <tfoot>
                                <tr>
                                    <th style="text-align:center">合计</th>
                                    <th style="text-align:center">{total_activations}</th>
                                    <th style="text-align:center">100%</th>
                                </tr>
                            </tfoot>
                        </table>
                            {f'''<div>
            <div class="card" style="margin-top: 20px; padding: 15px;">
                <h3>熟龄度分析总结</h3>
                <p style="font-size: 14px; line-height: 1.6;">数据表明骑手卡激活速度显著快于流量卡，尤其当天激活率达{rider_same_day_percent}%（流量卡为{data_same_day_percent}%），且近三日激活量占比{rider_recent_three_days_percent}%（流量卡{data_recent_three_days_percent}%）</p>
            </div>
        </div>''' if rider_same_day_percent > 0 else ''}
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 周订单趋势图 -->
        <div class="row">
            <div class="col">
                <div class="card" style="margin-top: 0px; padding: 15px;">
                    <h2><span class="num-badge">4</span> 最近七天订单趋势</h2>
                    <div class="chart-filter" style="margin-bottom: 15px; text-align: center;">
                        <button class="filter-btn active" data-type="all" onclick="switchTrendChart('all')">全部</button>
                        <button class="filter-btn" data-type="rider" onclick="switchTrendChart('rider')">骑手卡</button>
                        <button class="filter-btn" data-type="data" onclick="switchTrendChart('data')">流量卡</button>
                    </div>
                    <div class="chart-container" style="height: 350px;">
                        <canvas id="weeklyTrendChart"></canvas>
                    </div>
                    
                    <div style="margin-top: 20px;">
                        <table>
                            <thead>
                                <tr>
                                    <th>日期</th>
                                    <th style="text-align:center">总订单</th>
                                    <th style="text-align:center">骑手卡</th>
                                    <th style="text-align:center">流量卡</th>
                                </tr>
                            </thead>
                            <tbody>
                                {weekly_rows}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <p class="footnote">数据生成时间: {datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")}</p>
    </div>
    
    <!-- 产品明细弹窗 -->
    <div id="detailsModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal()">&times;</span>
            <div id="modalContent"></div>
        </div>
    </div>

    <script>
        // 渠道产品明细数据
        const channelsProducts = {channels_products_js};
        const channelNames = {channel_names_js};
        
        // 显示明细弹窗
        function showDetails(channelId) {{
            const index = parseInt(channelId.split('-')[1]);
            const channelName = channelNames[index];
            const products = channelsProducts[channelName];
            
            let modalHtml = '<div class="modal-title">' + channelName + ' 产品明细</div>';
            
            // 当日订单明细
            modalHtml += '<div class="product-section"><h3>当日订单明细：</h3>';
            
            // 骑手卡
            if (products.d0.骑手卡 && products.d0.骑手卡.length > 0) {{
                modalHtml += '<div class="product-category">';
                modalHtml += '<div class="product-category-title">骑手卡：</div>';
                modalHtml += '<div class="product-list">';
                modalHtml += products.d0.骑手卡.join('，');
                modalHtml += '</div></div>';
            }}
            
            // 流量卡
            if (products.d0.流量卡 && products.d0.流量卡.length > 0) {{
                modalHtml += '<div class="product-category">';
                modalHtml += '<div class="product-category-title">流量卡：</div>';
                modalHtml += '<div class="product-list">';
                modalHtml += products.d0.流量卡.join('，');
                modalHtml += '</div></div>';
            }}
            
            modalHtml += '</div>';
            
            // 当日激活明细
            modalHtml += '<div class="product-section mt-20"><h3>当日激活明细：</h3>';
            
            // 骑手卡
            if (products.d1.骑手卡 && products.d1.骑手卡.length > 0) {{
                modalHtml += '<div class="product-category">';
                modalHtml += '<div class="product-category-title">骑手卡：</div>';
                modalHtml += '<div class="product-list">';
                modalHtml += products.d1.骑手卡.join('，');
                modalHtml += '</div></div>';
            }}
            
            // 流量卡
            if (products.d1.流量卡 && products.d1.流量卡.length > 0) {{
                modalHtml += '<div class="product-category">';
                modalHtml += '<div class="product-category-title">流量卡：</div>';
                modalHtml += '<div class="product-list">';
                modalHtml += products.d1.流量卡.join('，');
                modalHtml += '</div></div>';
            }}
            
            modalHtml += '</div>';
            
            document.getElementById('modalContent').innerHTML = modalHtml;
            document.getElementById('detailsModal').style.display = 'block';
        }}
        
        // 关闭弹窗
        function closeModal() {{
            document.getElementById('detailsModal').style.display = 'none';
        }}
        
        // 点击弹窗外部关闭
        window.onclick = function(event) {{
            const modal = document.getElementById('detailsModal');
            if (event.target == modal) {{
                modal.style.display = 'none';
            }}
        }};
        
        // 渠道图表数据准备
        const d0OrdersData = {d0_orders_data_js};
        const d1ActivationsData = {d1_activations_data_js};
        
        // 渠道图表渲染
        const channelCtx = document.getElementById('channelChart').getContext('2d');
        new Chart(channelCtx, {{
            type: 'bar',
            data: {{
                labels: channelNames,
                datasets: [
                    {{
                        label: '当日下单',
                        data: d0OrdersData,
                        backgroundColor: 'rgba(54, 162, 235, 0.7)',
                        borderColor: 'rgba(54, 162, 235, 1)',
                        borderWidth: 1
                    }},
                    {{
                        label: '当日激活',
                        data: d1ActivationsData,
                        backgroundColor: 'rgba(75, 192, 192, 0.7)',
                        borderColor: 'rgba(75, 192, 192, 1)',
                        borderWidth: 1
                    }}
                ]
            }},
            options: {{
                responsive: true,
                maintainAspectRatio: false,
                scales: {{
                    y: {{
                        beginAtZero: true,
                        title: {{
                            display: true,
                            text: '订单数量'
                        }}
                    }}
                }},
                plugins: {{
                    title: {{
                        display: true,
                        text: '各渠道订单与激活数据'
                    }},
                    tooltip: {{
                        callbacks: {{
                            footer: function(tooltipItems) {{
                                return ''; // 不显示激活率
                            }}
                        }}
                    }}
                }}
            }}
        }});
        
        // 激活熟龄度分布图
        const ageDistributionCtx = document.getElementById('ageDistributionChart').getContext('2d');
        let ageChart;
        
        // 环比分析图表
        const comparisonCtx = document.getElementById('comparisonChart').getContext('2d');
        
        // 周趋势图表
        const weeklyTrendCtx = document.getElementById('weeklyTrendChart').getContext('2d');
        let trendChart;
        
        // 定义三种不同类型的数据集
        const all_data = {{
            labels: {age_labels_js},
            counts: {age_data_js},
            percentages: {age_percent_js},
            total: {total_activations}
        }};
        
        const rider_data = {{
            labels: {age_labels_js},
            counts: {rider_age_data_js},
            percentages: {rider_age_percent_js},
            total: {rider_card_count}
        }};
        
        const data_card_data = {{
            labels: {age_labels_js},
            counts: {data_age_data_js},
            percentages: {data_age_percent_js},
            total: {data_card_count}
        }};
        
        // 初始化图表
        function initAgeChart(type = 'all') {{
            const chartData = type === 'rider' ? rider_data : 
                            type === 'data' ? data_card_data : all_data;
            
            // 如果图表已存在，先销毁
            if (ageChart) {{
                ageChart.destroy();
            }}
            
            ageChart = new Chart(ageDistributionCtx, {{
                type: 'doughnut',
                data: {{
                    labels: chartData.labels,
                    datasets: [{{
                        data: chartData.counts,
                        backgroundColor: [
                            'rgba(54, 162, 235, 0.7)',  // 当天下单
                            'rgba(75, 192, 192, 0.7)',  // 昨天下单
                            'rgba(255, 205, 86, 0.7)',  // 前天下单
                            'rgba(255, 159, 64, 0.7)'   // 更早下单
                        ],
                        borderColor: [
                            'rgb(54, 162, 235)',
                            'rgb(75, 192, 192)',
                            'rgb(255, 205, 86)',
                            'rgb(255, 159, 64)'
                        ],
                        borderWidth: 1
                    }}]
                }},
                options: {{
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {{
                        title: {{
                            display: true,
                            text: '当日激活订单熟龄度分布'
                        }},
                        tooltip: {{
                            callbacks: {{
                                label: function(context) {{
                                    const label = context.label || '';
                                    const value = context.raw;
                                    const percentage = chartData.percentages[context.dataIndex];
                                    return `${{label}}: ${{value}} (${{percentage}}%)`;
                                }}
                            }}
                        }},
                        legend: {{
                            position: 'right',
                            labels: {{
                                boxWidth: 15,
                                padding: 10,
                                font: {{
                                    size: window.innerWidth < 768 ? 10 : 12
                                }}
                            }}
                        }}
                    }}
                }}
            }});
        }}
        
        // 更新进度条
        function updateAgeStatBar(percentages) {{
            const ageStatBar = document.getElementById('ageStatBar');
            ageStatBar.innerHTML = '';
            
            const colors = ['#36a2eb', '#4bc0c0', '#ffcd56', '#ff9f40'];
            const labels = ['当天', '昨天', '前天', '更早'];
            
            // 更新进度条
            percentages.forEach((percent, index) => {{
                const width = Math.max(percent, 2);
                const div = document.createElement('div');
                div.className = 'age-stat-item';
                div.style.backgroundColor = colors[index];
                div.style.width = `${{width}}%`;
                div.textContent = `${{percent}}%`;
                ageStatBar.appendChild(div);
            }});
            
            // 更新标签
            const labelsContainer = document.querySelector('.age-stat-labels');
            labelsContainer.innerHTML = '';
            
            percentages.forEach((percent, index) => {{
                const width = Math.max(percent, 2);
                const div = document.createElement('div');
                div.className = 'age-stat-label';
                div.style.width = `${{width}}%`;
                div.textContent = labels[index];
                labelsContainer.appendChild(div);
            }});
        }}
        
        // 更新表格数据
        function updateAgeTable(data) {{
            const table = document.getElementById('ageDistributionTable');
            const tbody = table.querySelector('tbody');
            const tfoot = table.querySelector('tfoot');
            
            // 清空表格内容
            tbody.innerHTML = '';
            
            // 添加新数据
            const labels = ['当天', '昨天', '前天', '更早'];
            data.counts.forEach((count, index) => {{
                const tr = document.createElement('tr');
                
                const tdLabel = document.createElement('td');
                tdLabel.textContent = labels[index];
                
                const tdCount = document.createElement('td');
                tdCount.style.textAlign = 'center';
                tdCount.textContent = count;
                
                const tdPercent = document.createElement('td');
                tdPercent.style.textAlign = 'center';
                tdPercent.textContent = `${{data.percentages[index]}}%`;
                
                tr.appendChild(tdLabel);
                tr.appendChild(tdCount);
                tr.appendChild(tdPercent);
                
                tbody.appendChild(tr);
            }});
            
            // 更新合计行
            tfoot.innerHTML = '';
            const tr = document.createElement('tr');
            
            const thLabel = document.createElement('th');
            thLabel.style.textAlign = 'center';
            thLabel.textContent = '合计';
            
            const thCount = document.createElement('th');
            thCount.style.textAlign = 'center';
            thCount.textContent = data.total;
            
            const thPercent = document.createElement('th');
            thPercent.style.textAlign = 'center';
            thPercent.textContent = '100%';
            
            tr.appendChild(thLabel);
            tr.appendChild(thCount);
            tr.appendChild(thPercent);
            
            tfoot.appendChild(tr);
        }}
        
        // 切换卡类型的函数
        function switchAgeChart(type) {{
            // 更新按钮样式
            document.querySelectorAll('.filter-btn').forEach(btn => {{
                btn.classList.remove('active');
            }});
            document.querySelector(`.filter-btn[data-type="${{type}}"]`).classList.add('active');
            
            // 设置标题
            const title = type === 'rider' ? '(骑手卡)' : 
                        type === 'data' ? '(流量卡)' : '(全部)';
            document.getElementById('ageDistributionTitle').textContent = title;
            
            // 获取相应数据
            const chartData = type === 'rider' ? rider_data : 
                            type === 'data' ? data_card_data : all_data;
            
            // 更新图表
            if (ageChart) {{
                ageChart.data.datasets[0].data = chartData.counts;
                ageChart.update();
            }} else {{
                initAgeChart(type);
            }}
            
            // 更新进度条
            updateAgeStatBar(chartData.percentages);
            
            // 更新表格
            updateAgeTable(chartData);
        }}
        
        // 初始化图表
        document.addEventListener('DOMContentLoaded', function() {{
            initAgeChart('all');
            
            // 绑定按钮点击事件
            document.querySelectorAll('.filter-btn').forEach(btn => {{
                btn.addEventListener('click', function() {{
                    const type = this.getAttribute('data-type');
                    switchAgeChart(type);
                }});
            }});
        }});
        
        // 环比分析图表数据
        const comparisonLabels = {comparison_labels};
        const currentData = {current_data};
        const previousData = {previous_data};
        
        // 渲染环比分析图表
        new Chart(comparisonCtx, {{
            type: 'bar',
            data: {{
                labels: comparisonLabels,
                datasets: [
                    {{
                        label: '当日',
                        data: currentData,
                        backgroundColor: 'rgba(75, 192, 192, 0.7)',
                        borderColor: 'rgba(75, 192, 192, 1)',
                        borderWidth: 1
                    }},
                    {{
                        label: '昨日',
                        data: previousData,
                        backgroundColor: 'rgba(54, 162, 235, 0.7)',
                        borderColor: 'rgba(54, 162, 235, 1)',
                        borderWidth: 1
                    }}
                ]
            }},
            options: {{
                responsive: true,
                maintainAspectRatio: false,
                scales: {{
                    y: {{
                        beginAtZero: true,
                        title: {{
                            display: true,
                            text: '订单数量'
                        }}
                    }}
                }},
                plugins: {{
                    title: {{
                        display: true,
                        text: '订单环比分析（当日 VS 昨日）'
                    }},
                    tooltip: {{
                        callbacks: {{
                            footer: function(tooltipItems) {{
                                return ''; // 不显示激活率
                            }}
                        }}
                    }}
                }}
            }}
        }});
        
        // 周趋势图表数据
        const weeklyDates = {weekly_dates};
        const weeklyTotalData = {weekly_total};
        const weeklyRiderData = {weekly_rider};
        const weeklyDataCardData = {weekly_data};
        
        // 初始化周趋势图表
        function initTrendChart(type = 'all') {{
            // 如果图表已存在，先销毁
            if (trendChart) {{
                trendChart.destroy();
            }}
            
            trendChart = new Chart(weeklyTrendCtx, {{
                type: 'line',
                data: {{
                    labels: weeklyDates,
                    datasets: [
                        {{
                            label: '总订单',
                            data: weeklyTotalData,
                            borderColor: 'rgba(54, 162, 235, 1)',
                            borderWidth: 2,
                            fill: false
                        }},
                        {{
                            label: '骑手卡',
                            data: weeklyRiderData,
                            borderColor: 'rgba(75, 192, 192, 1)',
                            borderWidth: 2,
                            fill: false
                        }},
                        {{
                            label: '流量卡',
                            data: weeklyDataCardData,
                            borderColor: 'rgba(255, 205, 86, 1)',
                            borderWidth: 2,
                            fill: false
                        }}
                    ]
                }},
                options: {{
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {{
                        y: {{
                            beginAtZero: true,
                            title: {{
                                display: true,
                                text: '订单数量'
                            }}
                        }}
                    }},
                    plugins: {{
                        title: {{
                            display: true,
                            text: '周订单趋势'
                        }},
                        legend: {{
                            position: 'top',
                            labels: {{
                                boxWidth: 15,
                                padding: 10,
                                font: {{
                                    size: window.innerWidth < 768 ? 10 : 12
                                }}
                            }}
                        }}
                    }}
                }}
            }});
        }}
        
        // 切换卡类型的函数
        function switchTrendChart(type) {{
            // 更新按钮样式
            document.querySelectorAll('.filter-btn').forEach(btn => {{
                btn.classList.remove('active');
            }});
            document.querySelector(`.filter-btn[data-type="${{type}}"]`).classList.add('active');
            
            if (type === 'all') {{
                trendChart.data.datasets[0].hidden = false;
                trendChart.data.datasets[1].hidden = false;
                trendChart.data.datasets[2].hidden = false;
            }} else if (type === 'rider') {{
                trendChart.data.datasets[0].hidden = true;
                trendChart.data.datasets[1].hidden = false;
                trendChart.data.datasets[2].hidden = true;
            }} else if (type === 'data') {{
                trendChart.data.datasets[0].hidden = true;
                trendChart.data.datasets[1].hidden = true;
                trendChart.data.datasets[2].hidden = false;
            }}
            
            trendChart.update();
        }}
        
        // 初始化图表
        document.addEventListener('DOMContentLoaded', function() {{
            initAgeChart('all');
            initTrendChart();
            
            // 绑定按钮点击事件 - 年龄分布图
            document.querySelectorAll('.filter-btn[data-type]').forEach(btn => {{
                btn.addEventListener('click', function() {{
                    const type = this.getAttribute('data-type');
                    if (this.closest('.chart-filter').previousElementSibling.id === 'ageDistributionChart') {{
                        switchAgeChart(type);
                    }} else {{
                        switchTrendChart(type);
                    }}
                }});
            }});
        }});
    </script>
</body>
</html>"""
    
    # 保存HTML文件
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(html_template)
    
    logger.info(f"HTML报表已生成: {output_file}")
    return output_file

def upload_to_ftp(local_file_path, remote_path=None):
    """将HTML报表上传到FTP服务器"""
    if not FTP_CONFIG:
        logger.error("FTP配置缺失，无法上传文件")
        return False
    
    # 如果没有指定远程路径，使用文件名作为远程路径
    if not remote_path:
        remote_path = os.path.basename(local_file_path)
    
    logger.info(f"准备上传文件到FTP服务器: {remote_path}")
    logger.info(f"FTP配置: 主机={FTP_CONFIG['host']}, 端口={FTP_CONFIG.get('port', 21)}, 目录={FTP_CONFIG.get('directory', '/')}") 
    logger.info(f"本地文件: {local_file_path}, 大小: {os.path.getsize(local_file_path)} 字节")
    
    try:
        # 连接FTP服务器
        ftp = FTP()
        ftp.set_debuglevel(2)  # 启用FTP详细调试
        logger.info(f"正在连接到FTP服务器: {FTP_CONFIG['host']}:{FTP_CONFIG.get('port', 21)}")
        ftp.connect(FTP_CONFIG['host'], FTP_CONFIG.get('port', 21))
        logger.info(f"正在登录FTP服务器: 用户名={FTP_CONFIG['username']}")
        ftp.login(FTP_CONFIG['username'], FTP_CONFIG['password'])
        logger.info("FTP登录成功")
        
        # 输出当前目录
        current_dir = ftp.pwd()
        logger.info(f"当前FTP目录: {current_dir}")
        
        # 切换到指定目录（如果有）
        target_dir = current_dir  # 默认使用当前目录
        if 'directory' in FTP_CONFIG and FTP_CONFIG['directory']:
            target_dir = FTP_CONFIG['directory']
            logger.info(f"尝试切换到目录: {target_dir}")
            try:
                ftp.cwd(target_dir)
                logger.info(f"成功切换到目录: {ftp.pwd()}")
            except Exception as e:
                logger.warning(f"无法切换到目录 {target_dir}: {e}")
                # 如果目录不存在，尝试创建
                try:
                    logger.info(f"尝试创建目录: {target_dir}")
                    dirs = target_dir.split('/')
                    for d in dirs:
                        if d:
                            try:
                                ftp.cwd(d)
                                logger.info(f"成功切换到已存在的目录: {d}")
                            except:
                                ftp.mkd(d)
                                logger.info(f"成功创建目录: {d}")
                                ftp.cwd(d)
                                logger.info(f"成功切换到新创建的目录: {d}")
                    logger.info(f"最终目录路径: {ftp.pwd()}")
                except Exception as e:
                    logger.error(f"创建目录失败: {e}")
                    # 如果无法创建目录，退回到根目录
                    ftp.cwd("/")
                    logger.info("已退回到根目录")
        
        # 上传前检查文件是否可读
        if not os.path.exists(local_file_path):
            logger.error(f"本地文件不存在: {local_file_path}")
            ftp.quit()
            return False
            
        if not os.access(local_file_path, os.R_OK):
            logger.error(f"没有读取本地文件的权限: {local_file_path}")
            ftp.quit()
            return False
        
        # 显示目录内容
        logger.info(f"上传前目录内容: {ftp.nlst()}")
        
        # 上传文件
        logger.info(f"开始上传文件: {local_file_path} -> {remote_path}")
        with open(local_file_path, 'rb') as file:
            upload_result = ftp.storbinary(f'STOR {remote_path}', file)
            logger.info(f"FTP上传结果: {upload_result}")
        
        # 验证上传结果
        dir_after = ftp.nlst()
        logger.info(f"上传后目录内容: {dir_after}")
        if remote_path in dir_after:
            logger.info(f"文件已成功上传并验证: {remote_path}")
        else:
            logger.warning(f"文件似乎没有上传成功，在目录中未找到: {remote_path}")
        
        # 获取URL（如果配置了基本URL）
        url = None
        if 'base_url' in FTP_CONFIG and FTP_CONFIG['base_url']:
            # 直接使用base_url和文件名，避免重复路径
            url = f"{FTP_CONFIG['base_url'].rstrip('/')}/{remote_path}"
            logger.info(f"文件访问URL: {url}")
        
        ftp.quit()
        logger.info("FTP连接已关闭")
        return url
    except Exception as e:
        logger.exception(f"上传文件到FTP服务器时出错: {e}")
        try:
            ftp.quit()
        except:
            pass
        return False

def main(date_str=None):
    """主函数"""
    logger.info("=" * 50)
    logger.info(f"开始生成日报统计数据...")
    
    # 生成报表数据
    report_data = generate_report_data(date_str)
    
    if not report_data:
        logger.error("未能生成报表数据，程序终止")
        return
    
    # 生成HTML报表
    html_file = generate_html_report(report_data)
    
    # 上传到FTP服务器
    if html_file:
        upload_result = upload_to_ftp(html_file)
        if upload_result:
            logger.info(f"报表已上传到服务器，URL: {upload_result}")
        else:
            logger.warning("报表上传失败")
    
    logger.info("日报生成完成")
    logger.info("=" * 50)

if __name__ == "__main__":
    import sys
    
    # 解析命令行参数
    date_param = None
    
    if len(sys.argv) > 1:
        date_param = sys.argv[1]
    
    main(date_param) 