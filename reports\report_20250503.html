<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>5月3日报表</title>
    <script src="https://b.zhoumeiren.cn/chart.min.js"></script>
    <style>
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 15px;
            background-color: #f5f5f5;
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        h1, h2, h3 {
            color: #333;
            margin-top: 0;
        }
        h1 {
            font-size: 1.8rem;
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        h2 {
            font-size: 1.5rem;
            margin-top: 25px;
            padding-bottom: 8px;
            border-bottom: 1px solid #eee;
        }
        .card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 6px rgba(0,0,0,0.05);
            padding: 15px;
            margin-bottom: 20px;
        }
        .row {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 20px;
        }
        .col {
            flex: 1;
            min-width: 300px;
        }
        .summary {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 20px;
        }
        .summary-card {
            flex: 1 1 200px;
            background: linear-gradient(135deg, #6A82FB, #FC5C7D);
            color: white;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .summary-card:nth-child(2) {
            background: linear-gradient(135deg, #11998e, #38ef7d);
        }
        .summary-card:nth-child(3) {
            background: linear-gradient(135deg, #FF8008, #FFC837);
        }
        .summary-card h3 {
            margin: 0;
            font-size: 1rem;
            color: rgba(255,255,255,0.9);
        }
        .summary-card p {
            margin: 10px 0 0;
            font-size: 1.8rem;
            font-weight: bold;
        }
        .summary-card .subtitle {
            font-size: 0.9rem;
            margin-top: 5px;
            opacity: 0.8;
        }
        .chart-container {
            position: relative;
            height: 400px;
            margin: 20px 0;
        }
        .chart-container.half-height {
            height: 300px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            font-size: 0.9rem;
        }
        th, td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        th {
            background-color: #f8f9fa;
            font-weight: 600;
        }
        tr:hover {
            background-color: #f8f9fa;
        }
        .age-stat {
            display: flex;
            margin: 10px 0;
            border-radius: 4px;
            overflow: hidden;
            min-height: 30px;
        }
        .age-stat-item {
            padding: 8px 4px;
            text-align: center;
            color: white;
            font-size: 0.9rem;
            min-width: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .age-stat-labels {
            display: flex;
            margin-top: 5px;
            font-size: 0.85rem;
        }
        .age-stat-label {
            text-align: center;
            padding: 0 2px;
            white-space: nowrap;
            overflow: hidden;
            min-width: 30px;
        }
        .explanation {
            margin: 15px 0;
            padding: 12px;
            background-color: #f8f9fa;
            border-left: 3px solid #6A82FB;
            font-size: 0.9rem;
            line-height: 1.5;
        }
        .footnote {
            font-size: 0.8rem;
            color: #777;
            text-align: center;
            margin-top: 30px;
            padding-top: 10px;
            border-top: 1px solid #eee;
        }
        .detail-btn {
            background-color: #6A82FB;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 5px 10px;
            font-size: 0.8rem;
            cursor: pointer;
        }
        .detail-btn:hover {
            background-color: #5A72EB;
        }
        .modal {
            display: none;
            position: fixed;
            z-index: 100;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.4);
        }
        .modal-content {
            background-color: #fff;
            margin: 15% auto;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            width: 80%;
            max-width: 500px;
        }
        .modal-title {
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        .product-category {
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #f0f0f0;
        }
        .product-category:last-child {
            border-bottom: none;
            padding-bottom: 0;
        }
        .product-category-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .product-list {
            line-height: 1.6;
        }
        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        .close:hover {
            color: #000;
        }
        
        /* 手机端适配 */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            .container {
                padding: 15px;
            }
            h1 {
                font-size: 1.5rem;
            }
            h2 {
                font-size: 1.3rem;
            }
            .summary-card {
                flex: 1 1 100%;
            }
            .chart-container {
                height: 300px;
            }
            .col {
                flex: 1 1 100%;
            }
            table {
                font-size: 0.8rem;
            }
            th, td {
                padding: 8px 5px;
            }
            .age-stat {
                height: 40px;
            }
            .age-stat-item {
                font-size: 0.8rem;
                min-width: 30px;
            }
            .age-stat-labels {
                display: flex;
                margin-top: 10px;
            }
            .age-stat-label {
                text-align: center;
                font-size: 0.8rem;
                padding: 0;
            }
            .modal-content {
                width: 90%;
                margin: 30% auto;
            }
        }
        .product-section {
            margin-bottom: 20px;
        }
        .product-section h3 {
            font-size: 1rem;
            margin-bottom: 10px;
            color: #333;
            font-weight: bold;
        }
        .mt-20 {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }
        .chart-filter .filter-btn {
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            color: #333;
            padding: 6px 12px;
            margin-right: 5px;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .chart-filter .filter-btn.active {
            background-color: #4bc0c0;
            color: white;
            border-color: #4bc0c0;
        }
        
        .chart-filter .filter-btn:hover {
            background-color: #e9ecef;
        }
        
        .chart-filter .filter-btn.active:hover {
            background-color: #3aafaf;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>5月3日报:订单与激活一览</h1>
        
        <div class="summary">
            <div class="summary-card">
                <h3>当日下单总数</h3>
                <p>202</p>
                <div class="subtitle">骑手卡: 173, 流量卡: 29</div>
            </div>
            <div class="summary-card">
                <h3>当日激活总数 (所有日期下单)</h3>
                <p>180</p>
                <div class="subtitle">所有渠道当日总激活数</div>
            </div>
            <div class="summary-card">
                <h3>当日激活下单熟龄度</h3>
                <p>3.9%</p>
                <div class="subtitle">当日激活中有3.9%是当天下单的</div>
            </div>
        </div>
        
        <div class="row">
            <div class="col">
                <div class="card">
                    <h2>渠道订单与激活数据</h2>
                    <div class="chart-container">
                        <canvas id="channelChart"></canvas>
                    </div>
                    <table>
                        <thead>
                            <tr>
                                <th>渠道</th>
                                <th>当日下单</th>
                                <th>当日激活</th>
                                <th>产品明细</th>
                            </tr>
                        </thead>
                        <tbody>
                            
        <tr>
            <td>顺丰同城</td>
            <td>80</td>
            <td>68</td>
            <td><button class="detail-btn" onclick="showDetails('channel-0')">明细</button></td>
        </tr>
        
        <tr>
            <td>拍小租</td>
            <td>43</td>
            <td>31</td>
            <td><button class="detail-btn" onclick="showDetails('channel-1')">明细</button></td>
        </tr>
        
        <tr>
            <td>顺丰驿站</td>
            <td>40</td>
            <td>34</td>
            <td><button class="detail-btn" onclick="showDetails('channel-2')">明细</button></td>
        </tr>
        
        <tr>
            <td>OPPO</td>
            <td>25</td>
            <td>35</td>
            <td><button class="detail-btn" onclick="showDetails('channel-3')">明细</button></td>
        </tr>
        
        <tr>
            <td>百世</td>
            <td>9</td>
            <td>2</td>
            <td><button class="detail-btn" onclick="showDetails('channel-4')">明细</button></td>
        </tr>
        
        <tr>
            <td>T3出行</td>
            <td>3</td>
            <td>0</td>
            <td><button class="detail-btn" onclick="showDetails('channel-5')">明细</button></td>
        </tr>
        
        <tr>
            <td>荣耀</td>
            <td>2</td>
            <td>4</td>
            <td><button class="detail-btn" onclick="showDetails('channel-6')">明细</button></td>
        </tr>
        
        <tr>
            <td>公众号</td>
            <td>0</td>
            <td>6</td>
            <td><button class="detail-btn" onclick="showDetails('channel-7')">明细</button></td>
        </tr>
        
                        </tbody>
                        <tfoot>
                            <tr>
                                <th>合计</th>
                                <th>202</th>
                                <th>180</th>
                                <th></th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
            
            <div class="col">
                <div class="card">
                    <h2>当日激活订单的熟龄度分布</h2>
                    <div class="chart-filter" style="margin-bottom: 15px; text-align: center;">
                        <button class="filter-btn active" data-type="all" onclick="switchAgeChart('all')">全部</button>
                        <button class="filter-btn" data-type="rider" onclick="switchAgeChart('rider')">骑手卡</button>
                        <button class="filter-btn" data-type="data" onclick="switchAgeChart('data')">流量卡</button>
                    </div>
                    <div class="chart-container half-height">
                        <canvas id="ageDistributionChart"></canvas>
                    </div>
                    
                    <div>
                        <h3>当日激活订单的下单时间分布 <span id="ageDistributionTitle">(全部)</span></h3>
                        <div class="age-stat" id="ageStatBar">
                            <div class="age-stat-item" style="background-color: #36a2eb; width: 3.9%">
                                3.9%
                            </div>
                            <div class="age-stat-item" style="background-color: #4bc0c0; width: 30.6%">
                                30.6%
                            </div>
                            <div class="age-stat-item" style="background-color: #ffcd56; width: 25.0%">
                                25.0%
                            </div>
                            <div class="age-stat-item" style="background-color: #ff9f40; width: 40.6%">
                                40.6%
                            </div>
                        </div>
                        <div class="age-stat-labels">
                            <div class="age-stat-label" style="width: 3.9%">当天</div>
                            <div class="age-stat-label" style="width: 30.6%">昨天</div>
                            <div class="age-stat-label" style="width: 25.0%">前天</div>
                            <div class="age-stat-label" style="width: 40.6%">更早</div>
                        </div>
                        
                        <table id="ageDistributionTable" style="margin-top: 20px;">
                            <thead>
                                <tr>
                                    <th>下单时间</th>
                                    <th>订单数</th>
                                    <th>占比</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>当天</td>
                                    <td>7</td>
                                    <td>3.9%</td>
                                </tr>
                                <tr>
                                    <td>昨天</td>
                                    <td>55</td>
                                    <td>30.6%</td>
                                </tr>
                                <tr>
                                    <td>前天</td>
                                    <td>45</td>
                                    <td>25.0%</td>
                                </tr>
                                <tr>
                                    <td>更早</td>
                                    <td>73</td>
                                    <td>40.6%</td>
                                </tr>
                            </tbody>
                            <tfoot>
                                <tr>
                                    <th>合计</th>
                                    <th>180</th>
                                    <th>100%</th>
                                </tr>
                            </tfoot>
                        </table>
                        
                        <div class="explanation">
                            <p><strong>熟龄度（Ageing）</strong>：指的是下单到激活的时间差。这个时间差越小，说明用户激活的"熟龄"越短，反之则为"熟龄"较长。</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="card" style="margin-top: 20px; padding: 15px;">
                <h3>熟龄度分析总结</h3>
                <p style="font-size: 14px; line-height: 1.6;">数据表明骑手卡激活速度显著快于流量卡，尤其当天激活率达4.3%（流量卡为2.5%），且近三日激活量占比65.7%（流量卡37.5%）</p>
            </div>
        </div>
        
        <p class="footnote">数据生成时间: 2025-05-04 08:00:03</p>
    </div>
    
    <!-- 产品明细弹窗 -->
    <div id="detailsModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal()">&times;</span>
            <div id="modalContent"></div>
        </div>
    </div>

    <script>
        // 渠道产品明细数据
        const channelsProducts = {"\u987a\u4e30\u540c\u57ce": {"d0": {"\u9a91\u624b\u5361": ["\u96c6\u8fd059\u5143\u00d771", "\u96c6\u8fd039\u5143\u00d77"], "\u6d41\u91cf\u5361": ["\u6df1\u5733\u8054\u901a59\u5143\u00d72"]}, "d1": {"\u9a91\u624b\u5361": ["\u96c6\u8fd059\u5143\u00d756", "\u96c6\u8fd039\u5143\u00d711"], "\u6d41\u91cf\u5361": ["\u6df1\u5733\u8054\u901a59\u5143\u00d71"]}}, "\u62cd\u5c0f\u79df": {"d0": {"\u9a91\u624b\u5361": ["\u96c6\u8fd059\u5143\u00d726", "\u96c6\u8fd039\u5143\u00d717"], "\u6d41\u91cf\u5361": []}, "d1": {"\u9a91\u624b\u5361": ["\u96c6\u8fd059\u5143\u00d729", "\u96c6\u8fd039\u5143\u00d72"], "\u6d41\u91cf\u5361": []}}, "\u987a\u4e30\u9a7f\u7ad9": {"d0": {"\u9a91\u624b\u5361": ["\u96c6\u8fd059\u5143\u00d740"], "\u6d41\u91cf\u5361": []}, "d1": {"\u9a91\u624b\u5361": ["\u96c6\u8fd059\u5143\u00d734"], "\u6d41\u91cf\u5361": []}}, "OPPO": {"d0": {"\u9a91\u624b\u5361": [], "\u6d41\u91cf\u5361": ["\u7535\u4fe129\u5143\u00d712", "\u7535\u4fe1\u65b0\u661f\u536129\u5143\u00d79", "\u79fb\u52a829\u5143\u00d74"]}, "d1": {"\u9a91\u624b\u5361": [], "\u6d41\u91cf\u5361": ["\u7535\u4fe1\u65b0\u661f\u536129\u5143\u00d726", "\u7535\u4fe129\u5143\u00d77", "\u79fb\u52a829\u5143\u00d72"]}}, "\u767e\u4e16": {"d0": {"\u9a91\u624b\u5361": ["\u96c6\u8fd059\u5143\u00d79"], "\u6d41\u91cf\u5361": []}, "d1": {"\u9a91\u624b\u5361": ["\u96c6\u8fd059\u5143\u00d72"], "\u6d41\u91cf\u5361": []}}, "T3\u51fa\u884c": {"d0": {"\u9a91\u624b\u5361": ["\u96c6\u8fd039\u5143\u00d72", "\u96c6\u8fd059\u5143\u00d71"], "\u6d41\u91cf\u5361": []}, "d1": {"\u9a91\u624b\u5361": [], "\u6d41\u91cf\u5361": []}}, "\u8363\u8000": {"d0": {"\u9a91\u624b\u5361": [], "\u6d41\u91cf\u5361": ["\u8054\u901a48\u5143\u00d71", "\u7535\u4fe129\u5143\u00d71"]}, "d1": {"\u9a91\u624b\u5361": [], "\u6d41\u91cf\u5361": ["\u7535\u4fe1\u65b0\u661f\u536129\u5143\u00d74"]}}, "\u516c\u4f17\u53f7": {"d0": {"\u9a91\u624b\u5361": [], "\u6d41\u91cf\u5361": []}, "d1": {"\u9a91\u624b\u5361": ["\u96c6\u8fd059\u5143\u00d75", "\u96c6\u8fd039\u5143\u00d71"], "\u6d41\u91cf\u5361": []}}};
        const channelNames = ["\u987a\u4e30\u540c\u57ce", "\u62cd\u5c0f\u79df", "\u987a\u4e30\u9a7f\u7ad9", "OPPO", "\u767e\u4e16", "T3\u51fa\u884c", "\u8363\u8000", "\u516c\u4f17\u53f7"];
        
        // 显示明细弹窗
        function showDetails(channelId) {
            const index = parseInt(channelId.split('-')[1]);
            const channelName = channelNames[index];
            const products = channelsProducts[channelName];
            
            let modalHtml = '<div class="modal-title">' + channelName + ' 产品明细</div>';
            
            // 当日订单明细
            modalHtml += '<div class="product-section"><h3>当日订单明细：</h3>';
            
            // 骑手卡
            if (products.d0.骑手卡 && products.d0.骑手卡.length > 0) {
                modalHtml += '<div class="product-category">';
                modalHtml += '<div class="product-category-title">骑手卡：</div>';
                modalHtml += '<div class="product-list">';
                modalHtml += products.d0.骑手卡.join('，');
                modalHtml += '</div></div>';
            }
            
            // 流量卡
            if (products.d0.流量卡 && products.d0.流量卡.length > 0) {
                modalHtml += '<div class="product-category">';
                modalHtml += '<div class="product-category-title">流量卡：</div>';
                modalHtml += '<div class="product-list">';
                modalHtml += products.d0.流量卡.join('，');
                modalHtml += '</div></div>';
            }
            
            modalHtml += '</div>';
            
            // 当日激活明细
            modalHtml += '<div class="product-section mt-20"><h3>当日激活明细：</h3>';
            
            // 骑手卡
            if (products.d1.骑手卡 && products.d1.骑手卡.length > 0) {
                modalHtml += '<div class="product-category">';
                modalHtml += '<div class="product-category-title">骑手卡：</div>';
                modalHtml += '<div class="product-list">';
                modalHtml += products.d1.骑手卡.join('，');
                modalHtml += '</div></div>';
            }
            
            // 流量卡
            if (products.d1.流量卡 && products.d1.流量卡.length > 0) {
                modalHtml += '<div class="product-category">';
                modalHtml += '<div class="product-category-title">流量卡：</div>';
                modalHtml += '<div class="product-list">';
                modalHtml += products.d1.流量卡.join('，');
                modalHtml += '</div></div>';
            }
            
            modalHtml += '</div>';
            
            document.getElementById('modalContent').innerHTML = modalHtml;
            document.getElementById('detailsModal').style.display = 'block';
        }
        
        // 关闭弹窗
        function closeModal() {
            document.getElementById('detailsModal').style.display = 'none';
        }
        
        // 点击弹窗外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('detailsModal');
            if (event.target == modal) {
                modal.style.display = 'none';
            }
        };
        
        // 渠道图表数据准备
        const d0OrdersData = [80, 43, 40, 25, 9, 3, 2, 0];
        const d1ActivationsData = [68, 31, 34, 35, 2, 0, 4, 6];
        
        // 渠道图表渲染
        const channelCtx = document.getElementById('channelChart').getContext('2d');
        new Chart(channelCtx, {
            type: 'bar',
            data: {
                labels: channelNames,
                datasets: [
                    {
                        label: '当日下单',
                        data: d0OrdersData,
                        backgroundColor: 'rgba(54, 162, 235, 0.7)',
                        borderColor: 'rgba(54, 162, 235, 1)',
                        borderWidth: 1
                    },
                    {
                        label: '当日激活',
                        data: d1ActivationsData,
                        backgroundColor: 'rgba(75, 192, 192, 0.7)',
                        borderColor: 'rgba(75, 192, 192, 1)',
                        borderWidth: 1
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '订单数量'
                        }
                    }
                },
                plugins: {
                    title: {
                        display: true,
                        text: '各渠道订单与激活数据'
                    },
                    tooltip: {
                        callbacks: {
                            footer: function(tooltipItems) {
                                return ''; // 不显示激活率
                            }
                        }
                    }
                }
            }
        });
        
        // 激活熟龄度分布图
        const ageDistributionCtx = document.getElementById('ageDistributionChart').getContext('2d');
        let ageChart;
        
        // 定义三种不同类型的数据集
        const all_data = {
            labels: ["\u5f53\u5929", "\u6628\u5929", "\u524d\u5929", "\u66f4\u65e9"],
            counts: [7, 55, 45, 73],
            percentages: [3.9, 30.6, 25.0, 40.6],
            total: 180
        };
        
        const rider_data = {
            labels: ["\u5f53\u5929", "\u6628\u5929", "\u524d\u5929", "\u66f4\u65e9"],
            counts: [6, 52, 34, 48],
            percentages: [4.3, 37.1, 24.3, 34.3],
            total: 140
        };
        
        const data_card_data = {
            labels: ["\u5f53\u5929", "\u6628\u5929", "\u524d\u5929", "\u66f4\u65e9"],
            counts: [1, 3, 11, 25],
            percentages: [2.5, 7.5, 27.5, 62.5],
            total: 40
        };
        
        // 初始化图表
        function initAgeChart(type = 'all') {
            const chartData = type === 'rider' ? rider_data : 
                            type === 'data' ? data_card_data : all_data;
            
            // 如果图表已存在，先销毁
            if (ageChart) {
                ageChart.destroy();
            }
            
            ageChart = new Chart(ageDistributionCtx, {
                type: 'doughnut',
                data: {
                    labels: chartData.labels,
                    datasets: [{
                        data: chartData.counts,
                        backgroundColor: [
                            'rgba(54, 162, 235, 0.7)',  // 当天下单
                            'rgba(75, 192, 192, 0.7)',  // 昨天下单
                            'rgba(255, 205, 86, 0.7)',  // 前天下单
                            'rgba(255, 159, 64, 0.7)'   // 更早下单
                        ],
                        borderColor: [
                            'rgb(54, 162, 235)',
                            'rgb(75, 192, 192)',
                            'rgb(255, 205, 86)',
                            'rgb(255, 159, 64)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: '当日激活订单熟龄度分布'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.raw;
                                    const percentage = chartData.percentages[context.dataIndex];
                                    return `${label}: ${value} (${percentage}%)`;
                                }
                            }
                        },
                        legend: {
                            position: 'right',
                            labels: {
                                boxWidth: 15,
                                padding: 10,
                                font: {
                                    size: window.innerWidth < 768 ? 10 : 12
                                }
                            }
                        }
                    }
                }
            });
        }
        
        // 更新进度条
        function updateAgeStatBar(percentages) {
            const ageStatBar = document.getElementById('ageStatBar');
            ageStatBar.innerHTML = '';
            
            const colors = ['#36a2eb', '#4bc0c0', '#ffcd56', '#ff9f40'];
            const labels = ['当天', '昨天', '前天', '更早'];
            
            // 更新进度条
            percentages.forEach((percent, index) => {
                const width = Math.max(percent, 2);
                const div = document.createElement('div');
                div.className = 'age-stat-item';
                div.style.backgroundColor = colors[index];
                div.style.width = `${width}%`;
                div.textContent = `${percent}%`;
                ageStatBar.appendChild(div);
            });
            
            // 更新标签
            const labelsContainer = document.querySelector('.age-stat-labels');
            labelsContainer.innerHTML = '';
            
            percentages.forEach((percent, index) => {
                const width = Math.max(percent, 2);
                const div = document.createElement('div');
                div.className = 'age-stat-label';
                div.style.width = `${width}%`;
                div.textContent = labels[index];
                labelsContainer.appendChild(div);
            });
        }
        
        // 更新表格数据
        function updateAgeTable(data) {
            const table = document.getElementById('ageDistributionTable');
            const tbody = table.querySelector('tbody');
            const tfoot = table.querySelector('tfoot');
            
            // 清空表格内容
            tbody.innerHTML = '';
            
            // 添加新数据
            const labels = ['当天', '昨天', '前天', '更早'];
            data.counts.forEach((count, index) => {
                const tr = document.createElement('tr');
                
                const tdLabel = document.createElement('td');
                tdLabel.textContent = labels[index];
                
                const tdCount = document.createElement('td');
                tdCount.textContent = count;
                
                const tdPercent = document.createElement('td');
                tdPercent.textContent = `${data.percentages[index]}%`;
                
                tr.appendChild(tdLabel);
                tr.appendChild(tdCount);
                tr.appendChild(tdPercent);
                
                tbody.appendChild(tr);
            });
            
            // 更新合计行
            tfoot.innerHTML = '';
            const tr = document.createElement('tr');
            
            const thLabel = document.createElement('th');
            thLabel.textContent = '合计';
            
            const thCount = document.createElement('th');
            thCount.textContent = data.total;
            
            const thPercent = document.createElement('th');
            thPercent.textContent = '100%';
            
            tr.appendChild(thLabel);
            tr.appendChild(thCount);
            tr.appendChild(thPercent);
            
            tfoot.appendChild(tr);
        }
        
        // 切换卡类型的函数
        function switchAgeChart(type) {
            // 更新按钮样式
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`.filter-btn[data-type="${type}"]`).classList.add('active');
            
            // 设置标题
            const title = type === 'rider' ? '(骑手卡)' : 
                        type === 'data' ? '(流量卡)' : '(全部)';
            document.getElementById('ageDistributionTitle').textContent = title;
            
            // 获取相应数据
            const chartData = type === 'rider' ? rider_data : 
                            type === 'data' ? data_card_data : all_data;
            
            // 更新图表
            if (ageChart) {
                ageChart.data.datasets[0].data = chartData.counts;
                ageChart.update();
            } else {
                initAgeChart(type);
            }
            
            // 更新进度条
            updateAgeStatBar(chartData.percentages);
            
            // 更新表格
            updateAgeTable(chartData);
        }
        
        // 初始化图表
        document.addEventListener('DOMContentLoaded', function() {
            initAgeChart('all');
            
            // 绑定按钮点击事件
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const type = this.getAttribute('data-type');
                    switchAgeChart(type);
                });
            });
        });
    </script>
</body>
</html>
