2025-06-01 08:00:04,821 - INFO - ==================================================
2025-06-01 08:00:04,821 - INFO - 开始生成日报统计数据...
2025-06-01 08:00:04,862 - INFO - 开始生成 2025-05-31 的报表数据...
2025-06-01 08:00:04,862 - INFO - 获取 2025-05-31 的订单数据（下单时间维度）...
2025-06-01 08:00:04,909 - INFO - 成功获取到 152 条去重后的订单数据（下单时间维度）
2025-06-01 08:00:04,909 - INFO - 开始分类并过滤订单数据...
2025-06-01 08:00:04,910 - INFO - 订单分类完成: 有效订单 152 单，其中骑手卡 149 单，流量卡 3 单
2025-06-01 08:00:04,910 - INFO - 计算各渠道的D0下单和D1激活数据...
2025-06-01 08:00:04,939 - INFO - 成功获取到 106 条当日激活订单
2025-06-01 08:00:04,943 - INFO - 样本订单日期检查 (共5个):
2025-06-01 08:00:04,943 - INFO - 订单 20250519220209817963: 下单时间=2025-05-19, 激活时间=2025-05-31, 相差=12天
2025-06-01 08:00:04,943 - INFO - 订单 20250524170847120392: 下单时间=2025-05-24, 激活时间=2025-05-31, 相差=7天
2025-06-01 08:00:04,944 - INFO - 订单 20250530092728136757: 下单时间=2025-05-30, 激活时间=2025-05-31, 相差=1天
2025-06-01 08:00:04,944 - INFO - 订单 20250530104527162702: 下单时间=2025-05-30, 激活时间=2025-05-31, 相差=1天
2025-06-01 08:00:04,944 - INFO - 订单 20250530191673198771: 下单时间=2025-05-30, 激活时间=2025-05-31, 相差=1天
2025-06-01 08:00:04,944 - INFO - 熟龄度处理统计: 总订单=106, 成功处理=106, 处理失败=0
2025-06-01 08:00:04,944 - INFO - 熟龄度分布: 当天=4, 昨天=40, 前天=17, 更早=45
2025-06-01 08:00:04,944 - INFO - 骑手卡熟龄度分布: 当天=4, 昨天=40, 前天=17, 更早=43
2025-06-01 08:00:04,944 - INFO - 流量卡熟龄度分布: 当天=0, 昨天=0, 前天=0, 更早=2
2025-06-01 08:00:04,944 - INFO - 获取报表日期与前一日订单数据对比...
2025-06-01 08:00:04,944 - INFO - 对比日期: 报表日期=2025-05-31, 前一日=2025-05-30
2025-06-01 08:00:04,945 - INFO - 获取 2025-05-31 的订单数据（下单时间维度）...
2025-06-01 08:00:04,976 - INFO - 成功获取到 152 条去重后的订单数据（下单时间维度）
2025-06-01 08:00:04,976 - INFO - 获取 2025-05-30 的订单数据（下单时间维度）...
2025-06-01 08:00:05,009 - INFO - 成功获取到 183 条去重后的订单数据（下单时间维度）
2025-06-01 08:00:05,010 - INFO - 获取周订单趋势数据...
2025-06-01 08:00:05,010 - INFO - 查询日期范围: 2025-05-25 00:00:00 至 2025-05-31 23:59:59
2025-06-01 08:00:05,038 - INFO - 成功获取到 7 天的订单趋势数据
2025-06-01 08:00:05,038 - INFO - 熟龄度数据: {'same_day': 4, 'one_day': 40, 'two_days': 17, 'more_days': 45}
2025-06-01 08:00:05,038 - INFO - 骑手卡熟龄度数据: {'same_day': 4, 'one_day': 40, 'two_days': 17, 'more_days': 43}, 总数: 104
2025-06-01 08:00:05,038 - INFO - 流量卡熟龄度数据: {'same_day': 0, 'one_day': 0, 'two_days': 0, 'more_days': 2}, 总数: 2
2025-06-01 08:00:05,038 - INFO - 当日下单当日激活比例: 3.8%（4/106）
2025-06-01 08:00:05,038 - INFO - 样本订单日期检查 (共5个): 106，熟龄度分布: 当天=4, 昨天=40, 前天=17, 更早=45
2025-06-01 08:00:05,038 - INFO - 环比分析: 当前=152单, 前一日=183单, 变化=-16.9%
2025-06-01 08:00:05,038 - INFO - 周趋势数据获取成功，共7天数据
2025-06-01 08:00:05,039 - INFO - 生成HTML报表模板，加载数据: 总活跃=106, 骑手卡=104, 流量卡=2
2025-06-01 08:00:05,039 - INFO - 骑手卡数据: [4, 40, 17, 43]
2025-06-01 08:00:05,039 - INFO - 流量卡数据: [0, 0, 0, 2]
2025-06-01 08:00:05,039 - INFO - HTML报表已生成: reports/report_20250531.html
2025-06-01 08:00:05,039 - INFO - 准备上传文件到FTP服务器: report_20250531.html
2025-06-01 08:00:05,040 - INFO - FTP配置: 主机=211.149.249.234, 端口=21, 目录=reports
2025-06-01 08:00:05,040 - INFO - 本地文件: reports/report_20250531.html, 大小: 54011 字节
2025-06-01 08:00:05,040 - INFO - 正在连接到FTP服务器: 211.149.249.234:21
2025-06-01 08:00:05,105 - INFO - 正在登录FTP服务器: 用户名=reports
2025-06-01 08:00:05,202 - INFO - FTP登录成功
2025-06-01 08:00:05,233 - INFO - 当前FTP目录: /
2025-06-01 08:00:05,233 - INFO - 尝试切换到目录: reports
2025-06-01 08:00:05,295 - INFO - 成功切换到目录: /reports
2025-06-01 08:00:05,426 - INFO - 上传前目录内容: ['report_20250506.html', 'report_20250522.html', 'report_20250511.html', 'report_20250515.html', 'report_20250518.html', 'report_20250516.html', 'report_20250512.html', 'report_20250503.html', 'report_20250508.html', 'report_20250502.html', 'report_20250519.html', 'report_20250525.html', '..', 'report_20250513.html', 'report_20250527.html', 'report_20250524.html', 'report_20250528.html', 'report_20250509.html', 'report_20250514.html', 'report_20250510.html', 'report_20250520.html', 'report_20250521.html', 'report_20250523.html', 'report_20250530.html', 'report_20250529.html', 'report_20250501.html', 'report_20250505.html', 'report_20250504.html', 'report_20250507.html', 'report_20250517.html', '.', 'report_20250526.html']
2025-06-01 08:00:05,426 - INFO - 开始上传文件: reports/report_20250531.html -> report_20250531.html
2025-06-01 08:00:05,644 - INFO - FTP上传结果: 226-File successfully transferred
226 0.093 seconds (measured here), 0.56 Mbytes per second
2025-06-01 08:00:05,782 - INFO - 上传后目录内容: ['report_20250506.html', 'report_20250522.html', 'report_20250511.html', 'report_20250515.html', 'report_20250518.html', 'report_20250516.html', 'report_20250512.html', 'report_20250503.html', 'report_20250508.html', 'report_20250502.html', 'report_20250531.html', 'report_20250519.html', 'report_20250525.html', '..', 'report_20250513.html', 'report_20250527.html', 'report_20250524.html', 'report_20250528.html', 'report_20250509.html', 'report_20250514.html', 'report_20250510.html', 'report_20250520.html', 'report_20250521.html', 'report_20250523.html', 'report_20250530.html', 'report_20250529.html', 'report_20250501.html', 'report_20250505.html', 'report_20250504.html', 'report_20250507.html', 'report_20250517.html', '.', 'report_20250526.html']
2025-06-01 08:00:05,782 - INFO - 文件已成功上传并验证: report_20250531.html
2025-06-01 08:00:05,782 - INFO - 文件访问URL: https://rep.331126.com/reports/report_20250531.html
2025-06-01 08:00:05,813 - INFO - FTP连接已关闭
2025-06-01 08:00:05,813 - INFO - 报表已上传到服务器，URL: https://rep.331126.com/reports/report_20250531.html
2025-06-01 08:00:05,813 - INFO - 日报生成完成
2025-06-01 08:00:05,813 - INFO - ==================================================
2025-06-01 08:00:06,220 - INFO - ==================================================
2025-06-01 08:00:06,220 - INFO - 开始生成并发送昨天的日报（全渠道生产环境）...
2025-06-01 08:00:06,220 - INFO - 开始执行昨天的日报发送流程（全渠道生产环境）...
2025-06-01 08:00:06,239 - INFO - 昨天日期: 2025-05-31
2025-06-01 08:00:06,239 - INFO - 开始获取昨天的订单数据（下单时间维度）...
2025-06-01 08:00:06,239 - INFO - 获取 2025-05-31 的订单数据（下单时间维度）...
2025-06-01 08:00:06,272 - INFO - 成功获取到 152 条去重后的订单数据（下单时间维度）
2025-06-01 08:00:06,272 - INFO - 开始获取昨天的订单数据（激活时间维度）...
2025-06-01 08:00:06,272 - INFO - 获取 2025-05-31 的订单数据（激活时间维度）...
2025-06-01 08:00:06,302 - INFO - 成功获取到 106 条去重后的订单数据（激活时间维度）
2025-06-01 08:00:06,302 - INFO - 成功获取到订单数据，继续生成日报
2025-06-01 08:00:06,303 - INFO - 开始生成昨天的日报...
2025-06-01 08:00:06,303 - INFO - 开始生成日报...
2025-06-01 08:00:06,303 - INFO - 开始分类并过滤订单数据...
2025-06-01 08:00:06,303 - INFO - 订单分类完成: 有效订单 152 单，其中骑手卡 149 单，大流量卡 3 单
2025-06-01 08:00:06,303 - INFO - 统计各类订单按渠道分类...
2025-06-01 08:00:06,303 - INFO - 开始分类并过滤订单数据...
2025-06-01 08:00:06,303 - INFO - 订单分类完成: 有效订单 106 单，其中骑手卡 104 单，大流量卡 2 单
2025-06-01 08:00:06,303 - INFO - 统计各类订单按渠道分类...
2025-06-01 08:00:06,304 - INFO - 日报生成完成
2025-06-01 08:00:06,304 - INFO - 开始分类并过滤订单数据...
2025-06-01 08:00:06,304 - INFO - 订单分类完成: 有效订单 152 单，其中骑手卡 149 单，大流量卡 3 单
2025-06-01 08:00:06,304 - INFO - 统计各类订单按渠道分类...
2025-06-01 08:00:06,304 - INFO - 开始分类并过滤订单数据...
2025-06-01 08:00:06,304 - INFO - 订单分类完成: 有效订单 106 单，其中骑手卡 104 单，大流量卡 2 单
2025-06-01 08:00:06,304 - INFO - 统计各类订单按渠道分类...
2025-06-01 08:00:06,306 - INFO - 开始生成 2025-05-31 的报表数据...
2025-06-01 08:00:06,306 - INFO - 获取 2025-05-31 的订单数据（下单时间维度）...
2025-06-01 08:00:06,338 - INFO - 成功获取到 152 条去重后的订单数据（下单时间维度）
2025-06-01 08:00:06,338 - INFO - 开始分类并过滤订单数据...
2025-06-01 08:00:06,338 - INFO - 订单分类完成: 有效订单 152 单，其中骑手卡 149 单，流量卡 3 单
2025-06-01 08:00:06,338 - INFO - 计算各渠道的D0下单和D1激活数据...
2025-06-01 08:00:06,368 - INFO - 成功获取到 106 条当日激活订单
2025-06-01 08:00:06,370 - INFO - 样本订单日期检查 (共5个):
2025-06-01 08:00:06,370 - INFO - 订单 20250519220209817963: 下单时间=2025-05-19, 激活时间=2025-05-31, 相差=12天
2025-06-01 08:00:06,370 - INFO - 订单 20250524170847120392: 下单时间=2025-05-24, 激活时间=2025-05-31, 相差=7天
2025-06-01 08:00:06,370 - INFO - 订单 20250530092728136757: 下单时间=2025-05-30, 激活时间=2025-05-31, 相差=1天
2025-06-01 08:00:06,370 - INFO - 订单 20250530104527162702: 下单时间=2025-05-30, 激活时间=2025-05-31, 相差=1天
2025-06-01 08:00:06,370 - INFO - 订单 20250530191673198771: 下单时间=2025-05-30, 激活时间=2025-05-31, 相差=1天
2025-06-01 08:00:06,370 - INFO - 熟龄度处理统计: 总订单=106, 成功处理=106, 处理失败=0
2025-06-01 08:00:06,370 - INFO - 熟龄度分布: 当天=4, 昨天=40, 前天=17, 更早=45
2025-06-01 08:00:06,370 - INFO - 骑手卡熟龄度分布: 当天=4, 昨天=40, 前天=17, 更早=43
2025-06-01 08:00:06,370 - INFO - 流量卡熟龄度分布: 当天=0, 昨天=0, 前天=0, 更早=2
2025-06-01 08:00:06,371 - INFO - 获取报表日期与前一日订单数据对比...
2025-06-01 08:00:06,371 - INFO - 对比日期: 报表日期=2025-05-31, 前一日=2025-05-30
2025-06-01 08:00:06,371 - INFO - 获取 2025-05-31 的订单数据（下单时间维度）...
2025-06-01 08:00:06,403 - INFO - 成功获取到 152 条去重后的订单数据（下单时间维度）
2025-06-01 08:00:06,403 - INFO - 获取 2025-05-30 的订单数据（下单时间维度）...
2025-06-01 08:00:06,436 - INFO - 成功获取到 183 条去重后的订单数据（下单时间维度）
2025-06-01 08:00:06,437 - INFO - 获取周订单趋势数据...
2025-06-01 08:00:06,437 - INFO - 查询日期范围: 2025-05-25 00:00:00 至 2025-05-31 23:59:59
2025-06-01 08:00:06,463 - INFO - 成功获取到 7 天的订单趋势数据
2025-06-01 08:00:06,463 - INFO - 熟龄度数据: {'same_day': 4, 'one_day': 40, 'two_days': 17, 'more_days': 45}
2025-06-01 08:00:06,464 - INFO - 骑手卡熟龄度数据: {'same_day': 4, 'one_day': 40, 'two_days': 17, 'more_days': 43}, 总数: 104
2025-06-01 08:00:06,464 - INFO - 流量卡熟龄度数据: {'same_day': 0, 'one_day': 0, 'two_days': 0, 'more_days': 2}, 总数: 2
2025-06-01 08:00:06,464 - INFO - 当日下单当日激活比例: 3.8%（4/106）
2025-06-01 08:00:06,464 - INFO - 样本订单日期检查 (共5个): 106，熟龄度分布: 当天=4, 昨天=40, 前天=17, 更早=45
2025-06-01 08:00:06,464 - INFO - 环比分析: 当前=152单, 前一日=183单, 变化=-16.9%
2025-06-01 08:00:06,464 - INFO - 周趋势数据获取成功，共7天数据
2025-06-01 08:00:06,556 - INFO - 开始发送模板卡片消息到企业微信...
2025-06-01 08:00:06,556 - INFO - 开始发送消息到企业微信（全渠道生产环境）...
2025-06-01 08:00:06,845 - INFO - 企业微信API响应状态码: 200
2025-06-01 08:00:06,845 - INFO - 企业微信消息发送成功
2025-06-01 08:00:06,845 - INFO - 昨天的日报发送成功
2025-06-01 08:00:06,845 - INFO - 昨天的日报处理完成。
2025-06-01 08:00:06,845 - INFO - ==================================================
