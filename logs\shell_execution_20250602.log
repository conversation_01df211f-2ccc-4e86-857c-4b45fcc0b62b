[2025-06-02 08:00:02] 开始执行日报脚本
[2025-06-02 08:00:02] 工作目录: /volume3/SSD-soft/fzsrb
[2025-06-02 08:00:02] 检查Python依赖...
WARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager, possibly rendering your system unusable. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv. Use the --root-user-action option if you know what you are doing and want to suppress this warning.
[2025-06-02 08:00:02] 开始执行简单日报脚本...
2025-06-02 08:00:05,105 - INFO - ==================================================
2025-06-02 08:00:05,105 - INFO - 开始生成日报统计数据...
2025-06-02 08:00:05,158 - INFO - 开始生成 2025-06-01 的报表数据...
2025-06-02 08:00:05,158 - INFO - 获取 2025-06-01 的订单数据（下单时间维度）...
2025-06-02 08:00:05,208 - INFO - 成功获取到 156 条去重后的订单数据（下单时间维度）
2025-06-02 08:00:05,208 - INFO - 开始分类并过滤订单数据...
2025-06-02 08:00:05,209 - INFO - 订单分类完成: 有效订单 156 单，其中骑手卡 155 单，流量卡 1 单
2025-06-02 08:00:05,209 - INFO - 计算各渠道的D0下单和D1激活数据...
2025-06-02 08:00:05,247 - INFO - 成功获取到 236 条当日激活订单
2025-06-02 08:00:05,252 - INFO - 样本订单日期检查 (共5个):
2025-06-02 08:00:05,252 - INFO - 订单 20250531201926312733: 下单时间=2025-05-31, 激活时间=2025-06-01, 相差=1天
2025-06-02 08:00:05,252 - INFO - 订单 20250531101250913625: 下单时间=2025-05-31, 激活时间=2025-06-01, 相差=1天
2025-06-02 08:00:05,252 - INFO - 订单 20250526090223792586: 下单时间=2025-05-26, 激活时间=2025-06-01, 相差=6天
2025-06-02 08:00:05,252 - INFO - 订单 20250527231418325581: 下单时间=2025-05-27, 激活时间=2025-06-01, 相差=5天
2025-06-02 08:00:05,252 - INFO - 订单 20250530190997874565: 下单时间=2025-05-30, 激活时间=2025-06-01, 相差=2天
2025-06-02 08:00:05,252 - INFO - 熟龄度处理统计: 总订单=236, 成功处理=236, 处理失败=0
2025-06-02 08:00:05,252 - INFO - 熟龄度分布: 当天=7, 昨天=62, 前天=55, 更早=112
2025-06-02 08:00:05,252 - INFO - 骑手卡熟龄度分布: 当天=7, 昨天=62, 前天=55, 更早=110
2025-06-02 08:00:05,252 - INFO - 流量卡熟龄度分布: 当天=0, 昨天=0, 前天=0, 更早=2
2025-06-02 08:00:05,253 - INFO - 获取报表日期与前一日订单数据对比...
2025-06-02 08:00:05,253 - INFO - 对比日期: 报表日期=2025-06-01, 前一日=2025-05-31
2025-06-02 08:00:05,253 - INFO - 获取 2025-06-01 的订单数据（下单时间维度）...
2025-06-02 08:00:05,289 - INFO - 成功获取到 156 条去重后的订单数据（下单时间维度）
2025-06-02 08:00:05,289 - INFO - 获取 2025-05-31 的订单数据（下单时间维度）...
2025-06-02 08:00:05,322 - INFO - 成功获取到 148 条去重后的订单数据（下单时间维度）
2025-06-02 08:00:05,323 - INFO - 获取周订单趋势数据...
2025-06-02 08:00:05,323 - INFO - 查询日期范围: 2025-05-26 00:00:00 至 2025-06-01 23:59:59
2025-06-02 08:00:05,353 - INFO - 成功获取到 7 天的订单趋势数据
2025-06-02 08:00:05,353 - INFO - 熟龄度数据: {'same_day': 7, 'one_day': 62, 'two_days': 55, 'more_days': 112}
2025-06-02 08:00:05,353 - INFO - 骑手卡熟龄度数据: {'same_day': 7, 'one_day': 62, 'two_days': 55, 'more_days': 110}, 总数: 234
2025-06-02 08:00:05,353 - INFO - 流量卡熟龄度数据: {'same_day': 0, 'one_day': 0, 'two_days': 0, 'more_days': 2}, 总数: 2
2025-06-02 08:00:05,353 - INFO - 当日下单当日激活比例: 3.0%（7/236）
2025-06-02 08:00:05,354 - INFO - 样本订单日期检查 (共5个): 236，熟龄度分布: 当天=7, 昨天=62, 前天=55, 更早=112
2025-06-02 08:00:05,354 - INFO - 环比分析: 当前=156单, 前一日=148单, 变化=5.4%
2025-06-02 08:00:05,354 - INFO - 周趋势数据获取成功，共7天数据
2025-06-02 08:00:05,354 - INFO - 生成HTML报表模板，加载数据: 总活跃=236, 骑手卡=234, 流量卡=2
2025-06-02 08:00:05,354 - INFO - 骑手卡数据: [7, 62, 55, 110]
2025-06-02 08:00:05,354 - INFO - 流量卡数据: [0, 0, 0, 2]
2025-06-02 08:00:05,355 - INFO - HTML报表已生成: reports/report_20250601.html
2025-06-02 08:00:05,355 - INFO - 准备上传文件到FTP服务器: report_20250601.html
2025-06-02 08:00:05,355 - INFO - FTP配置: 主机=211.149.249.234, 端口=21, 目录=reports
2025-06-02 08:00:05,355 - INFO - 本地文件: reports/report_20250601.html, 大小: 53323 字节
2025-06-02 08:00:05,355 - INFO - 正在连接到FTP服务器: 211.149.249.234:21
2025-06-02 08:00:05,419 - INFO - 正在登录FTP服务器: 用户名=reports
2025-06-02 08:00:05,505 - INFO - FTP登录成功
2025-06-02 08:00:05,535 - INFO - 当前FTP目录: /
2025-06-02 08:00:05,535 - INFO - 尝试切换到目录: reports
2025-06-02 08:00:05,597 - INFO - 成功切换到目录: /reports
2025-06-02 08:00:05,725 - INFO - 上传前目录内容: ['report_20250506.html', 'report_20250522.html', 'report_20250511.html', 'report_20250515.html', 'report_20250518.html', 'report_20250516.html', 'report_20250512.html', 'report_20250503.html', 'report_20250508.html', 'report_20250502.html', 'report_20250531.html', 'report_20250519.html', 'report_20250525.html', '..', 'report_20250513.html', 'report_20250527.html', 'report_20250524.html', 'report_20250528.html', 'report_20250509.html', 'report_20250514.html', 'report_20250510.html', 'report_20250520.html', 'report_20250521.html', 'report_20250523.html', 'report_20250530.html', 'report_20250529.html', 'report_20250501.html', 'report_20250505.html', 'report_20250504.html', 'report_20250507.html', 'report_20250517.html', '.', 'report_20250526.html']
2025-06-02 08:00:05,725 - INFO - 开始上传文件: reports/report_20250601.html -> report_20250601.html
2025-06-02 08:00:05,932 - INFO - FTP上传结果: 226-File successfully transferred
226 0.084 seconds (measured here), 0.60 Mbytes per second
2025-06-02 08:00:06,057 - INFO - 上传后目录内容: ['report_20250506.html', 'report_20250522.html', 'report_20250511.html', 'report_20250515.html', 'report_20250518.html', 'report_20250516.html', 'report_20250512.html', 'report_20250503.html', 'report_20250508.html', 'report_20250502.html', 'report_20250601.html', 'report_20250531.html', 'report_20250519.html', 'report_20250525.html', '..', 'report_20250513.html', 'report_20250527.html', 'report_20250524.html', 'report_20250528.html', 'report_20250509.html', 'report_20250514.html', 'report_20250510.html', 'report_20250520.html', 'report_20250521.html', 'report_20250523.html', 'report_20250530.html', 'report_20250529.html', 'report_20250501.html', 'report_20250505.html', 'report_20250504.html', 'report_20250507.html', 'report_20250517.html', '.', 'report_20250526.html']
2025-06-02 08:00:06,057 - INFO - 文件已成功上传并验证: report_20250601.html
2025-06-02 08:00:06,057 - INFO - 文件访问URL: https://rep.331126.com/reports/report_20250601.html
2025-06-02 08:00:06,087 - INFO - FTP连接已关闭
2025-06-02 08:00:06,088 - INFO - 报表已上传到服务器，URL: https://rep.331126.com/reports/report_20250601.html
2025-06-02 08:00:06,088 - INFO - 日报生成完成
2025-06-02 08:00:06,088 - INFO - ==================================================
*get* '220---------- Welcome to Pure-FTPd [privsep] [TLS] ----------\n'
*get* '220-You are user number 1 of 50 allowed.\n'
*get* '220-Local time is now 08:00. Server port: 21.\n'
*get* '220-This is a private system - No anonymous login\n'
*get* '220-IPv6 connections are also welcome on this server.\n'
*get* '220 You will be disconnected after 15 minutes of inactivity.\n'
*resp* '220---------- Welcome to Pure-FTPd [privsep] [TLS] ----------\n220-You are user number 1 of 50 allowed.\n220-Local time is now 08:00. Server port: 21.\n220-This is a private system - No anonymous login\n220-IPv6 connections are also welcome on this server.\n220 You will be disconnected after 15 minutes of inactivity.'
*cmd* 'USER reports'
*put* 'USER reports\r\n'
*get* '331 User reports OK. Password required\n'
*resp* '331 User reports OK. Password required'
*cmd* 'PASS ****************'
*put* 'PASS ****************\r\n'
*get* '230 OK. Current directory is /\n'
*resp* '230 OK. Current directory is /'
*cmd* 'PWD'
*put* 'PWD\r\n'
*get* '257 "/" is your current location\n'
*resp* '257 "/" is your current location'
*cmd* 'CWD reports'
*put* 'CWD reports\r\n'
*get* '250 OK. Current directory is /reports\n'
*resp* '250 OK. Current directory is /reports'
*cmd* 'PWD'
*put* 'PWD\r\n'
*get* '257 "/reports" is your current location\n'
*resp* '257 "/reports" is your current location'
*cmd* 'TYPE A'
*put* 'TYPE A\r\n'
*get* '200 TYPE is now ASCII\n'
*resp* '200 TYPE is now ASCII'
*cmd* 'PASV'
*put* 'PASV\r\n'
*get* '227 Entering Passive Mode (211,149,249,234,156,60)\n'
*resp* '227 Entering Passive Mode (211,149,249,234,156,60)'
*cmd* 'NLST'
*put* 'NLST\r\n'
*get* '150 Accepted data connection\n'
*resp* '150 Accepted data connection'
*get* '226 33 matches total\n'
*resp* '226 33 matches total'
*cmd* 'TYPE I'
*put* 'TYPE I\r\n'
*get* '200 TYPE is now 8-bit binary\n'
*resp* '200 TYPE is now 8-bit binary'
*cmd* 'PASV'
*put* 'PASV\r\n'
*get* '227 Entering Passive Mode (211,149,249,234,155,89)\n'
*resp* '227 Entering Passive Mode (211,149,249,234,155,89)'
*cmd* 'STOR report_20250601.html'
*put* 'STOR report_20250601.html\r\n'
*get* '150 Accepted data connection\n'
*resp* '150 Accepted data connection'
*get* '226-File successfully transferred\n'
*get* '226 0.084 seconds (measured here), 0.60 Mbytes per second\n'
*resp* '226-File successfully transferred\n226 0.084 seconds (measured here), 0.60 Mbytes per second'
*cmd* 'TYPE A'
*put* 'TYPE A\r\n'
*get* '200 TYPE is now ASCII\n'
*resp* '200 TYPE is now ASCII'
*cmd* 'PASV'
*put* 'PASV\r\n'
*get* '227 Entering Passive Mode (211,149,249,234,154,139)\n'
*resp* '227 Entering Passive Mode (211,149,249,234,154,139)'
*cmd* 'NLST'
*put* 'NLST\r\n'
*get* '150 Accepted data connection\n'
*resp* '150 Accepted data connection'
*get* '226 34 matches total\n'
*resp* '226 34 matches total'
*cmd* 'QUIT'
*put* 'QUIT\r\n'
*get* '221-Goodbye. You uploaded 53 and downloaded 0 kbytes.\n'
*get* '221 Logout.\n'
*resp* '221-Goodbye. You uploaded 53 and downloaded 0 kbytes.\n221 Logout.'
[2025-06-02 08:00:02] 简单日报脚本执行成功
[2025-06-02 08:00:02] 开始执行全渠道日报脚本...
2025-06-02 08:00:06,339 - INFO - ==================================================
2025-06-02 08:00:06,339 - INFO - 开始生成并发送昨天的日报（全渠道生产环境）...
2025-06-02 08:00:06,339 - INFO - 开始执行昨天的日报发送流程（全渠道生产环境）...
2025-06-02 08:00:06,358 - INFO - 昨天日期: 2025-06-01
2025-06-02 08:00:06,359 - INFO - 开始获取昨天的订单数据（下单时间维度）...
2025-06-02 08:00:06,359 - INFO - 获取 2025-06-01 的订单数据（下单时间维度）...
2025-06-02 08:00:06,393 - INFO - 成功获取到 156 条去重后的订单数据（下单时间维度）
2025-06-02 08:00:06,393 - INFO - 开始获取昨天的订单数据（激活时间维度）...
2025-06-02 08:00:06,393 - INFO - 获取 2025-06-01 的订单数据（激活时间维度）...
2025-06-02 08:00:06,431 - INFO - 成功获取到 236 条去重后的订单数据（激活时间维度）
2025-06-02 08:00:06,431 - INFO - 成功获取到订单数据，继续生成日报
2025-06-02 08:00:06,431 - INFO - 开始生成昨天的日报...
2025-06-02 08:00:06,431 - INFO - 开始生成日报...
2025-06-02 08:00:06,431 - INFO - 开始分类并过滤订单数据...
2025-06-02 08:00:06,431 - INFO - 订单分类完成: 有效订单 156 单，其中骑手卡 155 单，大流量卡 1 单
2025-06-02 08:00:06,432 - INFO - 统计各类订单按渠道分类...
2025-06-02 08:00:06,432 - INFO - 开始分类并过滤订单数据...
2025-06-02 08:00:06,432 - INFO - 订单分类完成: 有效订单 236 单，其中骑手卡 234 单，大流量卡 2 单
2025-06-02 08:00:06,432 - INFO - 统计各类订单按渠道分类...
2025-06-02 08:00:06,433 - INFO - 日报生成完成
2025-06-02 08:00:06,433 - INFO - 开始分类并过滤订单数据...
2025-06-02 08:00:06,433 - INFO - 订单分类完成: 有效订单 156 单，其中骑手卡 155 单，大流量卡 1 单
2025-06-02 08:00:06,433 - INFO - 统计各类订单按渠道分类...
2025-06-02 08:00:06,433 - INFO - 开始分类并过滤订单数据...
2025-06-02 08:00:06,433 - INFO - 订单分类完成: 有效订单 236 单，其中骑手卡 234 单，大流量卡 2 单
2025-06-02 08:00:06,433 - INFO - 统计各类订单按渠道分类...
2025-06-02 08:00:06,436 - INFO - 开始生成 2025-06-01 的报表数据...
2025-06-02 08:00:06,436 - INFO - 获取 2025-06-01 的订单数据（下单时间维度）...
2025-06-02 08:00:06,470 - INFO - 成功获取到 156 条去重后的订单数据（下单时间维度）
2025-06-02 08:00:06,470 - INFO - 开始分类并过滤订单数据...
2025-06-02 08:00:06,470 - INFO - 订单分类完成: 有效订单 156 单，其中骑手卡 155 单，流量卡 1 单
2025-06-02 08:00:06,470 - INFO - 计算各渠道的D0下单和D1激活数据...
2025-06-02 08:00:06,509 - INFO - 成功获取到 236 条当日激活订单
2025-06-02 08:00:06,511 - INFO - 样本订单日期检查 (共5个):
2025-06-02 08:00:06,511 - INFO - 订单 20250531201926312733: 下单时间=2025-05-31, 激活时间=2025-06-01, 相差=1天
2025-06-02 08:00:06,511 - INFO - 订单 20250531101250913625: 下单时间=2025-05-31, 激活时间=2025-06-01, 相差=1天
2025-06-02 08:00:06,511 - INFO - 订单 20250526090223792586: 下单时间=2025-05-26, 激活时间=2025-06-01, 相差=6天
2025-06-02 08:00:06,511 - INFO - 订单 20250527231418325581: 下单时间=2025-05-27, 激活时间=2025-06-01, 相差=5天
2025-06-02 08:00:06,511 - INFO - 订单 20250530190997874565: 下单时间=2025-05-30, 激活时间=2025-06-01, 相差=2天
2025-06-02 08:00:06,511 - INFO - 熟龄度处理统计: 总订单=236, 成功处理=236, 处理失败=0
2025-06-02 08:00:06,511 - INFO - 熟龄度分布: 当天=7, 昨天=62, 前天=55, 更早=112
2025-06-02 08:00:06,512 - INFO - 骑手卡熟龄度分布: 当天=7, 昨天=62, 前天=55, 更早=110
2025-06-02 08:00:06,512 - INFO - 流量卡熟龄度分布: 当天=0, 昨天=0, 前天=0, 更早=2
2025-06-02 08:00:06,512 - INFO - 获取报表日期与前一日订单数据对比...
2025-06-02 08:00:06,512 - INFO - 对比日期: 报表日期=2025-06-01, 前一日=2025-05-31
2025-06-02 08:00:06,512 - INFO - 获取 2025-06-01 的订单数据（下单时间维度）...
2025-06-02 08:00:06,546 - INFO - 成功获取到 156 条去重后的订单数据（下单时间维度）
2025-06-02 08:00:06,546 - INFO - 获取 2025-05-31 的订单数据（下单时间维度）...
2025-06-02 08:00:06,578 - INFO - 成功获取到 148 条去重后的订单数据（下单时间维度）
2025-06-02 08:00:06,579 - INFO - 获取周订单趋势数据...
2025-06-02 08:00:06,579 - INFO - 查询日期范围: 2025-05-26 00:00:00 至 2025-06-01 23:59:59
2025-06-02 08:00:06,606 - INFO - 成功获取到 7 天的订单趋势数据
2025-06-02 08:00:06,606 - INFO - 熟龄度数据: {'same_day': 7, 'one_day': 62, 'two_days': 55, 'more_days': 112}
2025-06-02 08:00:06,607 - INFO - 骑手卡熟龄度数据: {'same_day': 7, 'one_day': 62, 'two_days': 55, 'more_days': 110}, 总数: 234
2025-06-02 08:00:06,607 - INFO - 流量卡熟龄度数据: {'same_day': 0, 'one_day': 0, 'two_days': 0, 'more_days': 2}, 总数: 2
2025-06-02 08:00:06,607 - INFO - 当日下单当日激活比例: 3.0%（7/236）
2025-06-02 08:00:06,607 - INFO - 样本订单日期检查 (共5个): 236，熟龄度分布: 当天=7, 昨天=62, 前天=55, 更早=112
2025-06-02 08:00:06,607 - INFO - 环比分析: 当前=156单, 前一日=148单, 变化=5.4%
2025-06-02 08:00:06,607 - INFO - 周趋势数据获取成功，共7天数据
2025-06-02 08:00:06,642 - INFO - 开始发送模板卡片消息到企业微信...
2025-06-02 08:00:06,642 - INFO - 开始发送消息到企业微信（全渠道生产环境）...
2025-06-02 08:00:06,976 - INFO - 企业微信API响应状态码: 200
2025-06-02 08:00:06,976 - INFO - 企业微信消息发送成功
2025-06-02 08:00:06,976 - INFO - 昨天的日报发送成功
2025-06-02 08:00:06,977 - INFO - 昨天的日报处理完成。
2025-06-02 08:00:06,977 - INFO - ==================================================
[2025-06-02 08:00:02] 全渠道日报脚本执行成功
[2025-06-02 08:00:02] 开始执行OPPO渠道日报脚本...
2025-06-02 08:00:07,172 - INFO - ==================================================
2025-06-02 08:00:07,172 - INFO - 开始生成并发送OPPO和荣耀渠道日报（OPPO生产环境）...
2025-06-02 08:00:07,172 - INFO - 开始执行OPPO和荣耀渠道日报发送流程（OPPO生产环境）...
2025-06-02 08:00:07,191 - INFO - 查询日期: 2025-06-01
2025-06-02 08:00:07,192 - INFO - 获取 2025-06-01 的OPPO和荣耀渠道订单数据（下单时间维度）...
2025-06-02 08:00:07,217 - INFO - 查询到 1 条去重后的订单数据
2025-06-02 08:00:07,217 - INFO - 开始生成OPPO和荣耀渠道Markdown格式日报...
2025-06-02 08:00:07,217 - INFO - 开始处理订单数据，总数据条数: 1
2025-06-02 08:00:07,217 - INFO - 统计结果: 总订单 1
2025-06-02 08:00:07,217 - INFO -   荣耀: 1单
2025-06-02 08:00:07,217 - INFO - Markdown格式日报生成完成
2025-06-02 08:00:07,218 - INFO - 开始发送消息到企业微信（OPPO生产环境）...
2025-06-02 08:00:07,495 - INFO - 企业微信API响应状态码: 200
2025-06-02 08:00:07,495 - INFO - 企业微信消息发送成功
2025-06-02 08:00:07,495 - INFO - OPPO和荣耀渠道日报发送成功
2025-06-02 08:00:07,495 - INFO - OPPO和荣耀渠道日报处理完成。
2025-06-02 08:00:07,496 - INFO - ==================================================
[2025-06-02 08:00:02] OPPO渠道日报脚本执行成功
[2025-06-02 08:00:02] 开始执行驿舟渠道日报脚本...
2025-06-02 08:00:07,694 - INFO - ==================================================
2025-06-02 08:00:07,694 - INFO - 开始生成并发送驿舟相关渠道日报（驿舟日报环境）...
2025-06-02 08:00:07,694 - INFO - 开始执行驿舟相关渠道日报发送流程（驿舟日报环境）...
2025-06-02 08:00:07,714 - INFO - 获取 2025-06-01 的驿舟相关渠道订单数据（下单时间维度）...
2025-06-02 08:00:07,743 - INFO - 查询到 39 条去重后的订单数据
2025-06-02 08:00:07,743 - INFO - 获取 2025-06-01 的驿舟相关渠道订单数据（激活时间维度）...
2025-06-02 08:00:07,772 - INFO - 查询到 62 条去重后的订单数据
2025-06-02 08:00:07,822 - INFO - 开始发送消息到企业微信（驿舟日报环境）...
2025-06-02 08:00:08,051 - INFO - 企业微信API响应状态码: 200
2025-06-02 08:00:08,052 - INFO - 企业微信消息发送成功
2025-06-02 08:00:08,052 - INFO - 驿舟相关渠道日报发送成功
2025-06-02 08:00:08,052 - INFO - 驿舟相关渠道日报处理完成。
2025-06-02 08:00:08,052 - INFO - ==================================================
[2025-06-02 08:00:02] 驿舟渠道日报脚本执行成功
[2025-06-02 08:00:08] 所有日报脚本执行完成
----------------------------------------
