[2025-05-25 08:00:02] 开始执行日报脚本
[2025-05-25 08:00:02] 工作目录: /volume3/SSD-soft/fzsrb
[2025-05-25 08:00:02] 检查Python依赖...
WARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager, possibly rendering your system unusable. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv. Use the --root-user-action option if you know what you are doing and want to suppress this warning.
[2025-05-25 08:00:02] 开始执行简单日报脚本...
2025-05-25 08:00:03,715 - INFO - ==================================================
2025-05-25 08:00:03,715 - INFO - 开始生成日报统计数据...
2025-05-25 08:00:03,741 - INFO - 开始生成 2025-05-24 的报表数据...
2025-05-25 08:00:03,741 - INFO - 获取 2025-05-24 的订单数据（下单时间维度）...
2025-05-25 08:00:03,783 - INFO - 成功获取到 221 条去重后的订单数据（下单时间维度）
2025-05-25 08:00:03,783 - INFO - 开始分类并过滤订单数据...
2025-05-25 08:00:03,783 - INFO - 订单分类完成: 有效订单 221 单，其中骑手卡 218 单，流量卡 3 单
2025-05-25 08:00:03,783 - INFO - 计算各渠道的D0下单和D1激活数据...
2025-05-25 08:00:03,811 - INFO - 成功获取到 152 条当日激活订单
2025-05-25 08:00:03,820 - INFO - 样本订单日期检查 (共5个):
2025-05-25 08:00:03,820 - INFO - 订单 20250523162181438870: 下单时间=2025-05-23, 激活时间=2025-05-24, 相差=1天
2025-05-25 08:00:03,820 - INFO - 订单 20250523203450729703: 下单时间=2025-05-23, 激活时间=2025-05-24, 相差=1天
2025-05-25 08:00:03,820 - INFO - 订单 20250519012703505486: 下单时间=2025-05-19, 激活时间=2025-05-24, 相差=5天
2025-05-25 08:00:03,820 - INFO - 订单 20250522184920892304: 下单时间=2025-05-22, 激活时间=2025-05-24, 相差=2天
2025-05-25 08:00:03,820 - INFO - 订单 20250523130381434199: 下单时间=2025-05-23, 激活时间=2025-05-24, 相差=1天
2025-05-25 08:00:03,821 - INFO - 熟龄度处理统计: 总订单=152, 成功处理=152, 处理失败=0
2025-05-25 08:00:03,821 - INFO - 熟龄度分布: 当天=10, 昨天=81, 前天=37, 更早=24
2025-05-25 08:00:03,821 - INFO - 骑手卡熟龄度分布: 当天=10, 昨天=81, 前天=37, 更早=24
2025-05-25 08:00:03,821 - INFO - 流量卡熟龄度分布: 当天=0, 昨天=0, 前天=0, 更早=0
2025-05-25 08:00:03,821 - INFO - 获取报表日期与前一日订单数据对比...
2025-05-25 08:00:03,821 - INFO - 对比日期: 报表日期=2025-05-24, 前一日=2025-05-23
2025-05-25 08:00:03,821 - INFO - 获取 2025-05-24 的订单数据（下单时间维度）...
2025-05-25 08:00:03,853 - INFO - 成功获取到 221 条去重后的订单数据（下单时间维度）
2025-05-25 08:00:03,853 - INFO - 获取 2025-05-23 的订单数据（下单时间维度）...
2025-05-25 08:00:03,886 - INFO - 成功获取到 217 条去重后的订单数据（下单时间维度）
2025-05-25 08:00:03,886 - INFO - 获取周订单趋势数据...
2025-05-25 08:00:03,886 - INFO - 查询日期范围: 2025-05-18 00:00:00 至 2025-05-24 23:59:59
2025-05-25 08:00:03,913 - INFO - 成功获取到 7 天的订单趋势数据
2025-05-25 08:00:03,913 - INFO - 熟龄度数据: {'same_day': 10, 'one_day': 81, 'two_days': 37, 'more_days': 24}
2025-05-25 08:00:03,913 - INFO - 骑手卡熟龄度数据: {'same_day': 10, 'one_day': 81, 'two_days': 37, 'more_days': 24}, 总数: 152
2025-05-25 08:00:03,913 - INFO - 流量卡熟龄度数据: {'same_day': 0, 'one_day': 0, 'two_days': 0, 'more_days': 0}, 总数: 0
2025-05-25 08:00:03,914 - INFO - 当日下单当日激活比例: 6.6%（10/152）
2025-05-25 08:00:03,914 - INFO - 样本订单日期检查 (共5个): 152，熟龄度分布: 当天=10, 昨天=81, 前天=37, 更早=24
2025-05-25 08:00:03,914 - INFO - 环比分析: 当前=221单, 前一日=217单, 变化=1.8%
2025-05-25 08:00:03,914 - INFO - 周趋势数据获取成功，共7天数据
2025-05-25 08:00:03,914 - INFO - 生成HTML报表模板，加载数据: 总活跃=152, 骑手卡=152, 流量卡=0
2025-05-25 08:00:03,914 - INFO - 骑手卡数据: [10, 81, 37, 24]
2025-05-25 08:00:03,914 - INFO - 流量卡数据: [0, 0, 0, 0]
2025-05-25 08:00:03,915 - INFO - HTML报表已生成: reports/report_20250524.html
2025-05-25 08:00:03,915 - INFO - 准备上传文件到FTP服务器: report_20250524.html
2025-05-25 08:00:03,915 - INFO - FTP配置: 主机=211.149.249.234, 端口=21, 目录=reports
2025-05-25 08:00:03,915 - INFO - 本地文件: reports/report_20250524.html, 大小: 52729 字节
2025-05-25 08:00:03,916 - INFO - 正在连接到FTP服务器: 211.149.249.234:21
2025-05-25 08:00:03,981 - INFO - 正在登录FTP服务器: 用户名=reports
2025-05-25 08:00:04,075 - INFO - FTP登录成功
2025-05-25 08:00:04,107 - INFO - 当前FTP目录: /
2025-05-25 08:00:04,107 - INFO - 尝试切换到目录: reports
2025-05-25 08:00:04,172 - INFO - 成功切换到目录: /reports
2025-05-25 08:00:04,298 - INFO - 上传前目录内容: ['report_20250506.html', 'report_20250522.html', 'report_20250511.html', 'report_20250515.html', 'report_20250518.html', 'report_20250516.html', 'report_20250512.html', 'report_20250503.html', 'report_20250508.html', 'report_20250502.html', 'report_20250519.html', '..', 'report_20250513.html', 'report_20250509.html', 'report_20250514.html', 'report_20250510.html', 'report_20250520.html', 'report_20250521.html', 'report_20250523.html', 'report_20250501.html', 'report_20250505.html', 'report_20250504.html', 'report_20250507.html', 'report_20250517.html', '.']
2025-05-25 08:00:04,298 - INFO - 开始上传文件: reports/report_20250524.html -> report_20250524.html
2025-05-25 08:00:04,520 - INFO - FTP上传结果: 226-File successfully transferred
226 0.095 seconds (measured here), 0.53 Mbytes per second
2025-05-25 08:00:04,651 - INFO - 上传后目录内容: ['report_20250506.html', 'report_20250522.html', 'report_20250511.html', 'report_20250515.html', 'report_20250518.html', 'report_20250516.html', 'report_20250512.html', 'report_20250503.html', 'report_20250508.html', 'report_20250502.html', 'report_20250519.html', '..', 'report_20250513.html', 'report_20250524.html', 'report_20250509.html', 'report_20250514.html', 'report_20250510.html', 'report_20250520.html', 'report_20250521.html', 'report_20250523.html', 'report_20250501.html', 'report_20250505.html', 'report_20250504.html', 'report_20250507.html', 'report_20250517.html', '.']
2025-05-25 08:00:04,651 - INFO - 文件已成功上传并验证: report_20250524.html
2025-05-25 08:00:04,651 - INFO - 文件访问URL: https://rep.331126.com/reports/report_20250524.html
2025-05-25 08:00:04,683 - INFO - FTP连接已关闭
2025-05-25 08:00:04,683 - INFO - 报表已上传到服务器，URL: https://rep.331126.com/reports/report_20250524.html
2025-05-25 08:00:04,683 - INFO - 日报生成完成
2025-05-25 08:00:04,683 - INFO - ==================================================
*get* '220---------- Welcome to Pure-FTPd [privsep] [TLS] ----------\n'
*get* '220-You are user number 1 of 50 allowed.\n'
*get* '220-Local time is now 08:00. Server port: 21.\n'
*get* '220-This is a private system - No anonymous login\n'
*get* '220-IPv6 connections are also welcome on this server.\n'
*get* '220 You will be disconnected after 15 minutes of inactivity.\n'
*resp* '220---------- Welcome to Pure-FTPd [privsep] [TLS] ----------\n220-You are user number 1 of 50 allowed.\n220-Local time is now 08:00. Server port: 21.\n220-This is a private system - No anonymous login\n220-IPv6 connections are also welcome on this server.\n220 You will be disconnected after 15 minutes of inactivity.'
*cmd* 'USER reports'
*put* 'USER reports\r\n'
*get* '331 User reports OK. Password required\n'
*resp* '331 User reports OK. Password required'
*cmd* 'PASS ****************'
*put* 'PASS ****************\r\n'
*get* '230 OK. Current directory is /\n'
*resp* '230 OK. Current directory is /'
*cmd* 'PWD'
*put* 'PWD\r\n'
*get* '257 "/" is your current location\n'
*resp* '257 "/" is your current location'
*cmd* 'CWD reports'
*put* 'CWD reports\r\n'
*get* '250 OK. Current directory is /reports\n'
*resp* '250 OK. Current directory is /reports'
*cmd* 'PWD'
*put* 'PWD\r\n'
*get* '257 "/reports" is your current location\n'
*resp* '257 "/reports" is your current location'
*cmd* 'TYPE A'
*put* 'TYPE A\r\n'
*get* '200 TYPE is now ASCII\n'
*resp* '200 TYPE is now ASCII'
*cmd* 'PASV'
*put* 'PASV\r\n'
*get* '227 Entering Passive Mode (211,149,249,234,154,41)\n'
*resp* '227 Entering Passive Mode (211,149,249,234,154,41)'
*cmd* 'NLST'
*put* 'NLST\r\n'
*get* '150 Accepted data connection\n'
*resp* '150 Accepted data connection'
*get* '226 25 matches total\n'
*resp* '226 25 matches total'
*cmd* 'TYPE I'
*put* 'TYPE I\r\n'
*get* '200 TYPE is now 8-bit binary\n'
*resp* '200 TYPE is now 8-bit binary'
*cmd* 'PASV'
*put* 'PASV\r\n'
*get* '227 Entering Passive Mode (211,149,249,234,152,188)\n'
*resp* '227 Entering Passive Mode (211,149,249,234,152,188)'
*cmd* 'STOR report_20250524.html'
*put* 'STOR report_20250524.html\r\n'
*get* '150 Accepted data connection\n'
*resp* '150 Accepted data connection'
*get* '226-File successfully transferred\n'
*get* '226 0.095 seconds (measured here), 0.53 Mbytes per second\n'
*resp* '226-File successfully transferred\n226 0.095 seconds (measured here), 0.53 Mbytes per second'
*cmd* 'TYPE A'
*put* 'TYPE A\r\n'
*get* '200 TYPE is now ASCII\n'
*resp* '200 TYPE is now ASCII'
*cmd* 'PASV'
*put* 'PASV\r\n'
*get* '227 Entering Passive Mode (211,149,249,234,154,57)\n'
*resp* '227 Entering Passive Mode (211,149,249,234,154,57)'
*cmd* 'NLST'
*put* 'NLST\r\n'
*get* '150 Accepted data connection\n'
*resp* '150 Accepted data connection'
*get* '226 26 matches total\n'
*resp* '226 26 matches total'
*cmd* 'QUIT'
*put* 'QUIT\r\n'
*get* '221-Goodbye. You uploaded 52 and downloaded 0 kbytes.\n'
*get* '221 Logout.\n'
*resp* '221-Goodbye. You uploaded 52 and downloaded 0 kbytes.\n221 Logout.'
[2025-05-25 08:00:02] 简单日报脚本执行成功
[2025-05-25 08:00:02] 开始执行全渠道日报脚本...
2025-05-25 08:00:04,847 - INFO - ==================================================
2025-05-25 08:00:04,847 - INFO - 开始生成并发送昨天的日报（全渠道生产环境）...
2025-05-25 08:00:04,847 - INFO - 开始执行昨天的日报发送流程（全渠道生产环境）...
2025-05-25 08:00:04,863 - INFO - 昨天日期: 2025-05-24
2025-05-25 08:00:04,863 - INFO - 开始获取昨天的订单数据（下单时间维度）...
2025-05-25 08:00:04,863 - INFO - 获取 2025-05-24 的订单数据（下单时间维度）...
2025-05-25 08:00:04,896 - INFO - 成功获取到 221 条去重后的订单数据（下单时间维度）
2025-05-25 08:00:04,897 - INFO - 开始获取昨天的订单数据（激活时间维度）...
2025-05-25 08:00:04,897 - INFO - 获取 2025-05-24 的订单数据（激活时间维度）...
2025-05-25 08:00:04,925 - INFO - 成功获取到 152 条去重后的订单数据（激活时间维度）
2025-05-25 08:00:04,925 - INFO - 成功获取到订单数据，继续生成日报
2025-05-25 08:00:04,925 - INFO - 开始生成昨天的日报...
2025-05-25 08:00:04,925 - INFO - 开始生成日报...
2025-05-25 08:00:04,925 - INFO - 开始分类并过滤订单数据...
2025-05-25 08:00:04,926 - INFO - 订单分类完成: 有效订单 221 单，其中骑手卡 218 单，大流量卡 3 单
2025-05-25 08:00:04,926 - INFO - 统计各类订单按渠道分类...
2025-05-25 08:00:04,926 - INFO - 开始分类并过滤订单数据...
2025-05-25 08:00:04,926 - INFO - 订单分类完成: 有效订单 152 单，其中骑手卡 152 单，大流量卡 0 单
2025-05-25 08:00:04,926 - INFO - 统计各类订单按渠道分类...
2025-05-25 08:00:04,926 - INFO - 日报生成完成
2025-05-25 08:00:04,926 - INFO - 开始分类并过滤订单数据...
2025-05-25 08:00:04,927 - INFO - 订单分类完成: 有效订单 221 单，其中骑手卡 218 单，大流量卡 3 单
2025-05-25 08:00:04,927 - INFO - 统计各类订单按渠道分类...
2025-05-25 08:00:04,927 - INFO - 开始分类并过滤订单数据...
2025-05-25 08:00:04,927 - INFO - 订单分类完成: 有效订单 152 单，其中骑手卡 152 单，大流量卡 0 单
2025-05-25 08:00:04,927 - INFO - 统计各类订单按渠道分类...
2025-05-25 08:00:04,929 - INFO - 开始生成 2025-05-24 的报表数据...
2025-05-25 08:00:04,929 - INFO - 获取 2025-05-24 的订单数据（下单时间维度）...
2025-05-25 08:00:04,961 - INFO - 成功获取到 221 条去重后的订单数据（下单时间维度）
2025-05-25 08:00:04,961 - INFO - 开始分类并过滤订单数据...
2025-05-25 08:00:04,961 - INFO - 订单分类完成: 有效订单 221 单，其中骑手卡 218 单，流量卡 3 单
2025-05-25 08:00:04,961 - INFO - 计算各渠道的D0下单和D1激活数据...
2025-05-25 08:00:04,989 - INFO - 成功获取到 152 条当日激活订单
2025-05-25 08:00:04,990 - INFO - 样本订单日期检查 (共5个):
2025-05-25 08:00:04,991 - INFO - 订单 20250523162181438870: 下单时间=2025-05-23, 激活时间=2025-05-24, 相差=1天
2025-05-25 08:00:04,991 - INFO - 订单 20250523203450729703: 下单时间=2025-05-23, 激活时间=2025-05-24, 相差=1天
2025-05-25 08:00:04,991 - INFO - 订单 20250519012703505486: 下单时间=2025-05-19, 激活时间=2025-05-24, 相差=5天
2025-05-25 08:00:04,991 - INFO - 订单 20250522184920892304: 下单时间=2025-05-22, 激活时间=2025-05-24, 相差=2天
2025-05-25 08:00:04,991 - INFO - 订单 20250523130381434199: 下单时间=2025-05-23, 激活时间=2025-05-24, 相差=1天
2025-05-25 08:00:04,991 - INFO - 熟龄度处理统计: 总订单=152, 成功处理=152, 处理失败=0
2025-05-25 08:00:04,991 - INFO - 熟龄度分布: 当天=10, 昨天=81, 前天=37, 更早=24
2025-05-25 08:00:04,991 - INFO - 骑手卡熟龄度分布: 当天=10, 昨天=81, 前天=37, 更早=24
2025-05-25 08:00:04,991 - INFO - 流量卡熟龄度分布: 当天=0, 昨天=0, 前天=0, 更早=0
2025-05-25 08:00:04,992 - INFO - 获取报表日期与前一日订单数据对比...
2025-05-25 08:00:04,992 - INFO - 对比日期: 报表日期=2025-05-24, 前一日=2025-05-23
2025-05-25 08:00:04,992 - INFO - 获取 2025-05-24 的订单数据（下单时间维度）...
2025-05-25 08:00:05,024 - INFO - 成功获取到 221 条去重后的订单数据（下单时间维度）
2025-05-25 08:00:05,024 - INFO - 获取 2025-05-23 的订单数据（下单时间维度）...
2025-05-25 08:00:05,056 - INFO - 成功获取到 217 条去重后的订单数据（下单时间维度）
2025-05-25 08:00:05,057 - INFO - 获取周订单趋势数据...
2025-05-25 08:00:05,057 - INFO - 查询日期范围: 2025-05-18 00:00:00 至 2025-05-24 23:59:59
2025-05-25 08:00:05,080 - INFO - 成功获取到 7 天的订单趋势数据
2025-05-25 08:00:05,080 - INFO - 熟龄度数据: {'same_day': 10, 'one_day': 81, 'two_days': 37, 'more_days': 24}
2025-05-25 08:00:05,080 - INFO - 骑手卡熟龄度数据: {'same_day': 10, 'one_day': 81, 'two_days': 37, 'more_days': 24}, 总数: 152
2025-05-25 08:00:05,081 - INFO - 流量卡熟龄度数据: {'same_day': 0, 'one_day': 0, 'two_days': 0, 'more_days': 0}, 总数: 0
2025-05-25 08:00:05,081 - INFO - 当日下单当日激活比例: 6.6%（10/152）
2025-05-25 08:00:05,081 - INFO - 样本订单日期检查 (共5个): 152，熟龄度分布: 当天=10, 昨天=81, 前天=37, 更早=24
2025-05-25 08:00:05,081 - INFO - 环比分析: 当前=221单, 前一日=217单, 变化=1.8%
2025-05-25 08:00:05,081 - INFO - 周趋势数据获取成功，共7天数据
2025-05-25 08:00:05,153 - INFO - 开始发送模板卡片消息到企业微信...
2025-05-25 08:00:05,154 - INFO - 开始发送消息到企业微信（全渠道生产环境）...
2025-05-25 08:00:05,430 - INFO - 企业微信API响应状态码: 200
2025-05-25 08:00:05,430 - INFO - 企业微信消息发送成功
2025-05-25 08:00:05,431 - INFO - 昨天的日报发送成功
2025-05-25 08:00:05,431 - INFO - 昨天的日报处理完成。
2025-05-25 08:00:05,431 - INFO - ==================================================
[2025-05-25 08:00:02] 全渠道日报脚本执行成功
[2025-05-25 08:00:02] 开始执行OPPO渠道日报脚本...
2025-05-25 08:00:05,593 - INFO - ==================================================
2025-05-25 08:00:05,594 - INFO - 开始生成并发送OPPO和荣耀渠道日报（OPPO生产环境）...
2025-05-25 08:00:05,594 - INFO - 开始执行OPPO和荣耀渠道日报发送流程（OPPO生产环境）...
2025-05-25 08:00:05,612 - INFO - 查询日期: 2025-05-24
2025-05-25 08:00:05,612 - INFO - 获取 2025-05-24 的OPPO和荣耀渠道订单数据（下单时间维度）...
2025-05-25 08:00:05,635 - INFO - 查询到 0 条去重后的订单数据
2025-05-25 08:00:05,635 - WARNING - 未获取到OPPO和荣耀渠道订单数据，取消发送日报
2025-05-25 08:00:05,635 - WARNING - 由于没有数据，日报未发送
2025-05-25 08:00:05,635 - INFO - ==================================================
[2025-05-25 08:00:02] OPPO渠道日报脚本执行成功
[2025-05-25 08:00:05] 所有日报脚本执行完成
----------------------------------------
