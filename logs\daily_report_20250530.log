2025-05-30 08:00:02,907 - INFO - ==================================================
2025-05-30 08:00:02,907 - INFO - 开始生成日报统计数据...
2025-05-30 08:00:02,926 - INFO - 开始生成 2025-05-29 的报表数据...
2025-05-30 08:00:02,926 - INFO - 获取 2025-05-29 的订单数据（下单时间维度）...
2025-05-30 08:00:02,958 - INFO - 成功获取到 173 条去重后的订单数据（下单时间维度）
2025-05-30 08:00:02,958 - INFO - 开始分类并过滤订单数据...
2025-05-30 08:00:02,958 - INFO - 订单分类完成: 有效订单 173 单，其中骑手卡 170 单，流量卡 3 单
2025-05-30 08:00:02,958 - INFO - 计算各渠道的D0下单和D1激活数据...
2025-05-30 08:00:02,987 - INFO - 成功获取到 153 条当日激活订单
2025-05-30 08:00:02,990 - INFO - 样本订单日期检查 (共5个):
2025-05-30 08:00:02,990 - INFO - 订单 20250528000262577255: 下单时间=2025-05-28, 激活时间=2025-05-29, 相差=1天
2025-05-30 08:00:02,991 - INFO - 订单 20250528180306374178: 下单时间=2025-05-28, 激活时间=2025-05-29, 相差=1天
2025-05-30 08:00:02,991 - INFO - 订单 20250527133382379005: 下单时间=2025-05-27, 激活时间=2025-05-29, 相差=2天
2025-05-30 08:00:02,991 - INFO - 订单 20250527194635974873: 下单时间=2025-05-27, 激活时间=2025-05-29, 相差=2天
2025-05-30 08:00:02,991 - INFO - 订单 20250528152171157034: 下单时间=2025-05-28, 激活时间=2025-05-29, 相差=1天
2025-05-30 08:00:02,991 - INFO - 熟龄度处理统计: 总订单=153, 成功处理=153, 处理失败=0
2025-05-30 08:00:02,991 - INFO - 熟龄度分布: 当天=8, 昨天=59, 前天=44, 更早=42
2025-05-30 08:00:02,991 - INFO - 骑手卡熟龄度分布: 当天=8, 昨天=59, 前天=44, 更早=41
2025-05-30 08:00:02,991 - INFO - 流量卡熟龄度分布: 当天=0, 昨天=0, 前天=0, 更早=1
2025-05-30 08:00:02,991 - INFO - 获取报表日期与前一日订单数据对比...
2025-05-30 08:00:02,992 - INFO - 对比日期: 报表日期=2025-05-29, 前一日=2025-05-28
2025-05-30 08:00:02,992 - INFO - 获取 2025-05-29 的订单数据（下单时间维度）...
2025-05-30 08:00:03,024 - INFO - 成功获取到 173 条去重后的订单数据（下单时间维度）
2025-05-30 08:00:03,024 - INFO - 获取 2025-05-28 的订单数据（下单时间维度）...
2025-05-30 08:00:03,057 - INFO - 成功获取到 184 条去重后的订单数据（下单时间维度）
2025-05-30 08:00:03,057 - INFO - 获取周订单趋势数据...
2025-05-30 08:00:03,057 - INFO - 查询日期范围: 2025-05-23 00:00:00 至 2025-05-29 23:59:59
2025-05-30 08:00:03,085 - INFO - 成功获取到 7 天的订单趋势数据
2025-05-30 08:00:03,085 - INFO - 熟龄度数据: {'same_day': 8, 'one_day': 59, 'two_days': 44, 'more_days': 42}
2025-05-30 08:00:03,085 - INFO - 骑手卡熟龄度数据: {'same_day': 8, 'one_day': 59, 'two_days': 44, 'more_days': 41}, 总数: 152
2025-05-30 08:00:03,085 - INFO - 流量卡熟龄度数据: {'same_day': 0, 'one_day': 0, 'two_days': 0, 'more_days': 1}, 总数: 1
2025-05-30 08:00:03,085 - INFO - 当日下单当日激活比例: 5.2%（8/153）
2025-05-30 08:00:03,085 - INFO - 样本订单日期检查 (共5个): 153，熟龄度分布: 当天=8, 昨天=59, 前天=44, 更早=42
2025-05-30 08:00:03,085 - INFO - 环比分析: 当前=173单, 前一日=184单, 变化=-6.0%
2025-05-30 08:00:03,085 - INFO - 周趋势数据获取成功，共7天数据
2025-05-30 08:00:03,086 - INFO - 生成HTML报表模板，加载数据: 总活跃=153, 骑手卡=152, 流量卡=1
2025-05-30 08:00:03,086 - INFO - 骑手卡数据: [8, 59, 44, 41]
2025-05-30 08:00:03,086 - INFO - 流量卡数据: [0, 0, 0, 1]
2025-05-30 08:00:03,086 - INFO - HTML报表已生成: reports/report_20250529.html
2025-05-30 08:00:03,086 - INFO - 准备上传文件到FTP服务器: report_20250529.html
2025-05-30 08:00:03,086 - INFO - FTP配置: 主机=211.149.249.234, 端口=21, 目录=reports
2025-05-30 08:00:03,086 - INFO - 本地文件: reports/report_20250529.html, 大小: 53593 字节
2025-05-30 08:00:03,086 - INFO - 正在连接到FTP服务器: 211.149.249.234:21
2025-05-30 08:00:03,153 - INFO - 正在登录FTP服务器: 用户名=reports
2025-05-30 08:00:03,246 - INFO - FTP登录成功
2025-05-30 08:00:03,275 - INFO - 当前FTP目录: /
2025-05-30 08:00:03,275 - INFO - 尝试切换到目录: reports
2025-05-30 08:00:03,334 - INFO - 成功切换到目录: /reports
2025-05-30 08:00:03,454 - INFO - 上传前目录内容: ['report_20250506.html', 'report_20250522.html', 'report_20250511.html', 'report_20250515.html', 'report_20250518.html', 'report_20250516.html', 'report_20250512.html', 'report_20250503.html', 'report_20250508.html', 'report_20250502.html', 'report_20250519.html', 'report_20250525.html', '..', 'report_20250513.html', 'report_20250527.html', 'report_20250524.html', 'report_20250528.html', 'report_20250509.html', 'report_20250514.html', 'report_20250510.html', 'report_20250520.html', 'report_20250521.html', 'report_20250523.html', 'report_20250501.html', 'report_20250505.html', 'report_20250504.html', 'report_20250507.html', 'report_20250517.html', '.', 'report_20250526.html']
2025-05-30 08:00:03,455 - INFO - 开始上传文件: reports/report_20250529.html -> report_20250529.html
2025-05-30 08:00:03,687 - INFO - FTP上传结果: 226-File successfully transferred
226 0.106 seconds (measured here), 492.68 Kbytes per second
2025-05-30 08:00:03,813 - INFO - 上传后目录内容: ['report_20250506.html', 'report_20250522.html', 'report_20250511.html', 'report_20250515.html', 'report_20250518.html', 'report_20250516.html', 'report_20250512.html', 'report_20250503.html', 'report_20250508.html', 'report_20250502.html', 'report_20250519.html', 'report_20250525.html', '..', 'report_20250513.html', 'report_20250527.html', 'report_20250524.html', 'report_20250528.html', 'report_20250509.html', 'report_20250514.html', 'report_20250510.html', 'report_20250520.html', 'report_20250521.html', 'report_20250523.html', 'report_20250529.html', 'report_20250501.html', 'report_20250505.html', 'report_20250504.html', 'report_20250507.html', 'report_20250517.html', '.', 'report_20250526.html']
2025-05-30 08:00:03,813 - INFO - 文件已成功上传并验证: report_20250529.html
2025-05-30 08:00:03,813 - INFO - 文件访问URL: https://rep.331126.com/reports/report_20250529.html
2025-05-30 08:00:03,842 - INFO - FTP连接已关闭
2025-05-30 08:00:03,842 - INFO - 报表已上传到服务器，URL: https://rep.331126.com/reports/report_20250529.html
2025-05-30 08:00:03,842 - INFO - 日报生成完成
2025-05-30 08:00:03,842 - INFO - ==================================================
2025-05-30 08:00:04,002 - INFO - ==================================================
2025-05-30 08:00:04,003 - INFO - 开始生成并发送昨天的日报（全渠道生产环境）...
2025-05-30 08:00:04,003 - INFO - 开始执行昨天的日报发送流程（全渠道生产环境）...
2025-05-30 08:00:04,021 - INFO - 昨天日期: 2025-05-29
2025-05-30 08:00:04,022 - INFO - 开始获取昨天的订单数据（下单时间维度）...
2025-05-30 08:00:04,022 - INFO - 获取 2025-05-29 的订单数据（下单时间维度）...
2025-05-30 08:00:04,054 - INFO - 成功获取到 173 条去重后的订单数据（下单时间维度）
2025-05-30 08:00:04,054 - INFO - 开始获取昨天的订单数据（激活时间维度）...
2025-05-30 08:00:04,054 - INFO - 获取 2025-05-29 的订单数据（激活时间维度）...
2025-05-30 08:00:04,084 - INFO - 成功获取到 153 条去重后的订单数据（激活时间维度）
2025-05-30 08:00:04,084 - INFO - 成功获取到订单数据，继续生成日报
2025-05-30 08:00:04,084 - INFO - 开始生成昨天的日报...
2025-05-30 08:00:04,084 - INFO - 开始生成日报...
2025-05-30 08:00:04,084 - INFO - 开始分类并过滤订单数据...
2025-05-30 08:00:04,084 - INFO - 订单分类完成: 有效订单 173 单，其中骑手卡 170 单，大流量卡 3 单
2025-05-30 08:00:04,084 - INFO - 统计各类订单按渠道分类...
2025-05-30 08:00:04,084 - INFO - 开始分类并过滤订单数据...
2025-05-30 08:00:04,084 - INFO - 订单分类完成: 有效订单 153 单，其中骑手卡 152 单，大流量卡 1 单
2025-05-30 08:00:04,085 - INFO - 统计各类订单按渠道分类...
2025-05-30 08:00:04,085 - INFO - 日报生成完成
2025-05-30 08:00:04,085 - INFO - 开始分类并过滤订单数据...
2025-05-30 08:00:04,085 - INFO - 订单分类完成: 有效订单 173 单，其中骑手卡 170 单，大流量卡 3 单
2025-05-30 08:00:04,085 - INFO - 统计各类订单按渠道分类...
2025-05-30 08:00:04,085 - INFO - 开始分类并过滤订单数据...
2025-05-30 08:00:04,085 - INFO - 订单分类完成: 有效订单 153 单，其中骑手卡 152 单，大流量卡 1 单
2025-05-30 08:00:04,086 - INFO - 统计各类订单按渠道分类...
2025-05-30 08:00:04,087 - INFO - 开始生成 2025-05-29 的报表数据...
2025-05-30 08:00:04,087 - INFO - 获取 2025-05-29 的订单数据（下单时间维度）...
2025-05-30 08:00:04,118 - INFO - 成功获取到 173 条去重后的订单数据（下单时间维度）
2025-05-30 08:00:04,118 - INFO - 开始分类并过滤订单数据...
2025-05-30 08:00:04,118 - INFO - 订单分类完成: 有效订单 173 单，其中骑手卡 170 单，流量卡 3 单
2025-05-30 08:00:04,118 - INFO - 计算各渠道的D0下单和D1激活数据...
2025-05-30 08:00:04,148 - INFO - 成功获取到 153 条当日激活订单
2025-05-30 08:00:04,150 - INFO - 样本订单日期检查 (共5个):
2025-05-30 08:00:04,150 - INFO - 订单 20250528000262577255: 下单时间=2025-05-28, 激活时间=2025-05-29, 相差=1天
2025-05-30 08:00:04,150 - INFO - 订单 20250528180306374178: 下单时间=2025-05-28, 激活时间=2025-05-29, 相差=1天
2025-05-30 08:00:04,150 - INFO - 订单 20250527133382379005: 下单时间=2025-05-27, 激活时间=2025-05-29, 相差=2天
2025-05-30 08:00:04,150 - INFO - 订单 20250527194635974873: 下单时间=2025-05-27, 激活时间=2025-05-29, 相差=2天
2025-05-30 08:00:04,150 - INFO - 订单 20250528152171157034: 下单时间=2025-05-28, 激活时间=2025-05-29, 相差=1天
2025-05-30 08:00:04,151 - INFO - 熟龄度处理统计: 总订单=153, 成功处理=153, 处理失败=0
2025-05-30 08:00:04,151 - INFO - 熟龄度分布: 当天=8, 昨天=59, 前天=44, 更早=42
2025-05-30 08:00:04,151 - INFO - 骑手卡熟龄度分布: 当天=8, 昨天=59, 前天=44, 更早=41
2025-05-30 08:00:04,151 - INFO - 流量卡熟龄度分布: 当天=0, 昨天=0, 前天=0, 更早=1
2025-05-30 08:00:04,151 - INFO - 获取报表日期与前一日订单数据对比...
2025-05-30 08:00:04,151 - INFO - 对比日期: 报表日期=2025-05-29, 前一日=2025-05-28
2025-05-30 08:00:04,151 - INFO - 获取 2025-05-29 的订单数据（下单时间维度）...
2025-05-30 08:00:04,181 - INFO - 成功获取到 173 条去重后的订单数据（下单时间维度）
2025-05-30 08:00:04,181 - INFO - 获取 2025-05-28 的订单数据（下单时间维度）...
2025-05-30 08:00:04,213 - INFO - 成功获取到 184 条去重后的订单数据（下单时间维度）
2025-05-30 08:00:04,214 - INFO - 获取周订单趋势数据...
2025-05-30 08:00:04,214 - INFO - 查询日期范围: 2025-05-23 00:00:00 至 2025-05-29 23:59:59
2025-05-30 08:00:04,241 - INFO - 成功获取到 7 天的订单趋势数据
2025-05-30 08:00:04,241 - INFO - 熟龄度数据: {'same_day': 8, 'one_day': 59, 'two_days': 44, 'more_days': 42}
2025-05-30 08:00:04,241 - INFO - 骑手卡熟龄度数据: {'same_day': 8, 'one_day': 59, 'two_days': 44, 'more_days': 41}, 总数: 152
2025-05-30 08:00:04,241 - INFO - 流量卡熟龄度数据: {'same_day': 0, 'one_day': 0, 'two_days': 0, 'more_days': 1}, 总数: 1
2025-05-30 08:00:04,241 - INFO - 当日下单当日激活比例: 5.2%（8/153）
2025-05-30 08:00:04,241 - INFO - 样本订单日期检查 (共5个): 153，熟龄度分布: 当天=8, 昨天=59, 前天=44, 更早=42
2025-05-30 08:00:04,241 - INFO - 环比分析: 当前=173单, 前一日=184单, 变化=-6.0%
2025-05-30 08:00:04,241 - INFO - 周趋势数据获取成功，共7天数据
2025-05-30 08:00:04,334 - INFO - 开始发送模板卡片消息到企业微信...
2025-05-30 08:00:04,334 - INFO - 开始发送消息到企业微信（全渠道生产环境）...
2025-05-30 08:00:04,585 - INFO - 企业微信API响应状态码: 200
2025-05-30 08:00:04,585 - INFO - 企业微信消息发送成功
2025-05-30 08:00:04,585 - INFO - 昨天的日报发送成功
2025-05-30 08:00:04,585 - INFO - 昨天的日报处理完成。
2025-05-30 08:00:04,586 - INFO - ==================================================
