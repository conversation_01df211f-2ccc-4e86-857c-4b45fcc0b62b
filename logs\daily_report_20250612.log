2025-06-12 08:00:04,004 - INFO - ==================================================
2025-06-12 08:00:04,004 - INFO - 开始生成日报统计数据...
2025-06-12 08:00:04,036 - INFO - 开始生成 2025-06-11 的报表数据...
2025-06-12 08:00:04,037 - INFO - 获取 2025-06-11 的订单数据（下单时间维度）...
2025-06-12 08:00:04,085 - INFO - 成功获取到 240 条去重后的订单数据（下单时间维度）
2025-06-12 08:00:04,085 - INFO - 开始分类并过滤订单数据...
2025-06-12 08:00:04,085 - INFO - 订单分类完成: 有效订单 240 单，其中骑手卡 237 单，流量卡 3 单
2025-06-12 08:00:04,085 - INFO - 计算各渠道的D0下单和D1激活数据...
2025-06-12 08:00:04,118 - INFO - 成功获取到 150 条当日激活订单
2025-06-12 08:00:04,122 - INFO - 样本订单日期检查 (共5个):
2025-06-12 08:00:04,122 - INFO - 订单 20250610222449921068: 下单时间=2025-06-10, 激活时间=2025-06-11, 相差=1天
2025-06-12 08:00:04,122 - INFO - 订单 20250610162376360068: 下单时间=2025-06-10, 激活时间=2025-06-11, 相差=1天
2025-06-12 08:00:04,122 - INFO - 订单 20250610174253661791: 下单时间=2025-06-10, 激活时间=2025-06-11, 相差=1天
2025-06-12 08:00:04,122 - INFO - 订单 20250611011078669207: 下单时间=2025-06-11, 激活时间=2025-06-11, 相差=0天
2025-06-12 08:00:04,122 - INFO - 订单 20250608094497086996: 下单时间=2025-06-08, 激活时间=2025-06-11, 相差=3天
2025-06-12 08:00:04,123 - INFO - 熟龄度处理统计: 总订单=150, 成功处理=150, 处理失败=0
2025-06-12 08:00:04,123 - INFO - 熟龄度分布: 当天=15, 昨天=68, 前天=31, 更早=36
2025-06-12 08:00:04,123 - INFO - 骑手卡熟龄度分布: 当天=15, 昨天=68, 前天=31, 更早=36
2025-06-12 08:00:04,123 - INFO - 流量卡熟龄度分布: 当天=0, 昨天=0, 前天=0, 更早=0
2025-06-12 08:00:04,123 - INFO - 获取报表日期与前一日订单数据对比...
2025-06-12 08:00:04,123 - INFO - 对比日期: 报表日期=2025-06-11, 前一日=2025-06-10
2025-06-12 08:00:04,123 - INFO - 获取 2025-06-11 的订单数据（下单时间维度）...
2025-06-12 08:00:04,162 - INFO - 成功获取到 240 条去重后的订单数据（下单时间维度）
2025-06-12 08:00:04,163 - INFO - 获取 2025-06-10 的订单数据（下单时间维度）...
2025-06-12 08:00:04,199 - INFO - 成功获取到 217 条去重后的订单数据（下单时间维度）
2025-06-12 08:00:04,200 - INFO - 获取周订单趋势数据...
2025-06-12 08:00:04,200 - INFO - 查询日期范围: 2025-06-05 00:00:00 至 2025-06-11 23:59:59
2025-06-12 08:00:04,225 - INFO - 成功获取到 7 天的订单趋势数据
2025-06-12 08:00:04,226 - INFO - 熟龄度数据: {'same_day': 15, 'one_day': 68, 'two_days': 31, 'more_days': 36}
2025-06-12 08:00:04,226 - INFO - 骑手卡熟龄度数据: {'same_day': 15, 'one_day': 68, 'two_days': 31, 'more_days': 36}, 总数: 150
2025-06-12 08:00:04,226 - INFO - 流量卡熟龄度数据: {'same_day': 0, 'one_day': 0, 'two_days': 0, 'more_days': 0}, 总数: 0
2025-06-12 08:00:04,226 - INFO - 当日下单当日激活比例: 10.0%（15/150）
2025-06-12 08:00:04,226 - INFO - 样本订单日期检查 (共5个): 150，熟龄度分布: 当天=15, 昨天=68, 前天=31, 更早=36
2025-06-12 08:00:04,226 - INFO - 环比分析: 当前=240单, 前一日=217单, 变化=10.6%
2025-06-12 08:00:04,226 - INFO - 周趋势数据获取成功，共7天数据
2025-06-12 08:00:04,227 - INFO - 生成HTML报表模板，加载数据: 总活跃=150, 骑手卡=150, 流量卡=0
2025-06-12 08:00:04,227 - INFO - 骑手卡数据: [15, 68, 31, 36]
2025-06-12 08:00:04,227 - INFO - 流量卡数据: [0, 0, 0, 0]
2025-06-12 08:00:04,227 - INFO - HTML报表已生成: reports/report_20250611.html
2025-06-12 08:00:04,227 - INFO - 准备上传文件到FTP服务器: report_20250611.html
2025-06-12 08:00:04,227 - INFO - FTP配置: 主机=211.149.249.234, 端口=21, 目录=reports
2025-06-12 08:00:04,227 - INFO - 本地文件: reports/report_20250611.html, 大小: 51938 字节
2025-06-12 08:00:04,228 - INFO - 正在连接到FTP服务器: 211.149.249.234:21
2025-06-12 08:00:04,301 - INFO - 正在登录FTP服务器: 用户名=reports
2025-06-12 08:00:04,405 - INFO - FTP登录成功
2025-06-12 08:00:04,441 - INFO - 当前FTP目录: /
2025-06-12 08:00:04,441 - INFO - 尝试切换到目录: reports
2025-06-12 08:00:04,512 - INFO - 成功切换到目录: /reports
2025-06-12 08:00:04,653 - INFO - 上传前目录内容: ['report_20250506.html', 'report_20250522.html', 'report_20250511.html', 'report_20250515.html', 'report_20250605.html', 'report_20250518.html', 'report_20250610.html', 'report_20250516.html', 'report_20250512.html', 'report_20250503.html', 'report_20250602.html', 'report_20250508.html', 'report_20250609.html', 'report_20250502.html', 'report_20250601.html', 'report_20250531.html', 'report_20250519.html', 'report_20250608.html', 'report_20250525.html', '..', 'report_20250513.html', 'report_20250527.html', 'report_20250524.html', 'report_20250528.html', 'report_20250606.html', 'report_20250509.html', 'report_20250514.html', 'report_20250604.html', 'report_20250510.html', 'report_20250520.html', 'report_20250521.html', 'report_20250523.html', 'report_20250530.html', 'report_20250607.html', 'report_20250529.html', 'report_20250501.html', 'report_20250505.html', 'report_20250504.html', 'report_20250507.html', 'report_20250517.html', '.', 'report_20250526.html', 'report_20250603.html']
2025-06-12 08:00:04,654 - INFO - 开始上传文件: reports/report_20250611.html -> report_20250611.html
2025-06-12 08:00:04,897 - INFO - FTP上传结果: 226-File successfully transferred
226 0.101 seconds (measured here), 500.92 Kbytes per second
2025-06-12 08:00:05,043 - INFO - 上传后目录内容: ['report_20250506.html', 'report_20250522.html', 'report_20250511.html', 'report_20250515.html', 'report_20250605.html', 'report_20250518.html', 'report_20250610.html', 'report_20250516.html', 'report_20250512.html', 'report_20250503.html', 'report_20250602.html', 'report_20250508.html', 'report_20250609.html', 'report_20250502.html', 'report_20250601.html', 'report_20250611.html', 'report_20250531.html', 'report_20250519.html', 'report_20250608.html', 'report_20250525.html', '..', 'report_20250513.html', 'report_20250527.html', 'report_20250524.html', 'report_20250528.html', 'report_20250606.html', 'report_20250509.html', 'report_20250514.html', 'report_20250604.html', 'report_20250510.html', 'report_20250520.html', 'report_20250521.html', 'report_20250523.html', 'report_20250530.html', 'report_20250607.html', 'report_20250529.html', 'report_20250501.html', 'report_20250505.html', 'report_20250504.html', 'report_20250507.html', 'report_20250517.html', '.', 'report_20250526.html', 'report_20250603.html']
2025-06-12 08:00:05,043 - INFO - 文件已成功上传并验证: report_20250611.html
2025-06-12 08:00:05,043 - INFO - 文件访问URL: https://rep.331126.com/reports/report_20250611.html
2025-06-12 08:00:05,078 - INFO - FTP连接已关闭
2025-06-12 08:00:05,079 - INFO - 报表已上传到服务器，URL: https://rep.331126.com/reports/report_20250611.html
2025-06-12 08:00:05,079 - INFO - 日报生成完成
2025-06-12 08:00:05,079 - INFO - ==================================================
2025-06-12 08:00:05,251 - INFO - ==================================================
2025-06-12 08:00:05,251 - INFO - 开始生成并发送昨天的日报（全渠道生产环境）...
2025-06-12 08:00:05,251 - INFO - 开始执行昨天的日报发送流程（全渠道生产环境）...
2025-06-12 08:00:05,267 - INFO - 昨天日期: 2025-06-11
2025-06-12 08:00:05,267 - INFO - 开始获取昨天的订单数据（下单时间维度）...
2025-06-12 08:00:05,267 - INFO - 获取 2025-06-11 的订单数据（下单时间维度）...
2025-06-12 08:00:05,304 - INFO - 成功获取到 240 条去重后的订单数据（下单时间维度）
2025-06-12 08:00:05,305 - INFO - 开始获取昨天的订单数据（激活时间维度）...
2025-06-12 08:00:05,305 - INFO - 获取 2025-06-11 的订单数据（激活时间维度）...
2025-06-12 08:00:05,337 - INFO - 成功获取到 150 条去重后的订单数据（激活时间维度）
2025-06-12 08:00:05,337 - INFO - 成功获取到订单数据，继续生成日报
2025-06-12 08:00:05,338 - INFO - 开始生成昨天的日报...
2025-06-12 08:00:05,338 - INFO - 开始生成日报...
2025-06-12 08:00:05,338 - INFO - 开始分类并过滤订单数据...
2025-06-12 08:00:05,338 - INFO - 订单分类完成: 有效订单 240 单，其中骑手卡 237 单，大流量卡 3 单
2025-06-12 08:00:05,338 - INFO - 统计各类订单按渠道分类...
2025-06-12 08:00:05,338 - INFO - 开始分类并过滤订单数据...
2025-06-12 08:00:05,338 - INFO - 订单分类完成: 有效订单 150 单，其中骑手卡 150 单，大流量卡 0 单
2025-06-12 08:00:05,338 - INFO - 统计各类订单按渠道分类...
2025-06-12 08:00:05,339 - INFO - 日报生成完成
2025-06-12 08:00:05,339 - INFO - 开始分类并过滤订单数据...
2025-06-12 08:00:05,339 - INFO - 订单分类完成: 有效订单 240 单，其中骑手卡 237 单，大流量卡 3 单
2025-06-12 08:00:05,339 - INFO - 统计各类订单按渠道分类...
2025-06-12 08:00:05,339 - INFO - 开始分类并过滤订单数据...
2025-06-12 08:00:05,339 - INFO - 订单分类完成: 有效订单 150 单，其中骑手卡 150 单，大流量卡 0 单
2025-06-12 08:00:05,339 - INFO - 统计各类订单按渠道分类...
2025-06-12 08:00:05,341 - INFO - 开始生成 2025-06-11 的报表数据...
2025-06-12 08:00:05,342 - INFO - 获取 2025-06-11 的订单数据（下单时间维度）...
2025-06-12 08:00:05,379 - INFO - 成功获取到 240 条去重后的订单数据（下单时间维度）
2025-06-12 08:00:05,379 - INFO - 开始分类并过滤订单数据...
2025-06-12 08:00:05,379 - INFO - 订单分类完成: 有效订单 240 单，其中骑手卡 237 单，流量卡 3 单
2025-06-12 08:00:05,379 - INFO - 计算各渠道的D0下单和D1激活数据...
2025-06-12 08:00:05,414 - INFO - 成功获取到 150 条当日激活订单
2025-06-12 08:00:05,416 - INFO - 样本订单日期检查 (共5个):
2025-06-12 08:00:05,416 - INFO - 订单 20250610222449921068: 下单时间=2025-06-10, 激活时间=2025-06-11, 相差=1天
2025-06-12 08:00:05,416 - INFO - 订单 20250610162376360068: 下单时间=2025-06-10, 激活时间=2025-06-11, 相差=1天
2025-06-12 08:00:05,416 - INFO - 订单 20250610174253661791: 下单时间=2025-06-10, 激活时间=2025-06-11, 相差=1天
2025-06-12 08:00:05,416 - INFO - 订单 20250611011078669207: 下单时间=2025-06-11, 激活时间=2025-06-11, 相差=0天
2025-06-12 08:00:05,416 - INFO - 订单 20250608094497086996: 下单时间=2025-06-08, 激活时间=2025-06-11, 相差=3天
2025-06-12 08:00:05,416 - INFO - 熟龄度处理统计: 总订单=150, 成功处理=150, 处理失败=0
2025-06-12 08:00:05,416 - INFO - 熟龄度分布: 当天=15, 昨天=68, 前天=31, 更早=36
2025-06-12 08:00:05,416 - INFO - 骑手卡熟龄度分布: 当天=15, 昨天=68, 前天=31, 更早=36
2025-06-12 08:00:05,417 - INFO - 流量卡熟龄度分布: 当天=0, 昨天=0, 前天=0, 更早=0
2025-06-12 08:00:05,417 - INFO - 获取报表日期与前一日订单数据对比...
2025-06-12 08:00:05,417 - INFO - 对比日期: 报表日期=2025-06-11, 前一日=2025-06-10
2025-06-12 08:00:05,417 - INFO - 获取 2025-06-11 的订单数据（下单时间维度）...
2025-06-12 08:00:05,454 - INFO - 成功获取到 240 条去重后的订单数据（下单时间维度）
2025-06-12 08:00:05,455 - INFO - 获取 2025-06-10 的订单数据（下单时间维度）...
2025-06-12 08:00:05,490 - INFO - 成功获取到 217 条去重后的订单数据（下单时间维度）
2025-06-12 08:00:05,490 - INFO - 获取周订单趋势数据...
2025-06-12 08:00:05,490 - INFO - 查询日期范围: 2025-06-05 00:00:00 至 2025-06-11 23:59:59
2025-06-12 08:00:05,515 - INFO - 成功获取到 7 天的订单趋势数据
2025-06-12 08:00:05,515 - INFO - 熟龄度数据: {'same_day': 15, 'one_day': 68, 'two_days': 31, 'more_days': 36}
2025-06-12 08:00:05,515 - INFO - 骑手卡熟龄度数据: {'same_day': 15, 'one_day': 68, 'two_days': 31, 'more_days': 36}, 总数: 150
2025-06-12 08:00:05,515 - INFO - 流量卡熟龄度数据: {'same_day': 0, 'one_day': 0, 'two_days': 0, 'more_days': 0}, 总数: 0
2025-06-12 08:00:05,515 - INFO - 当日下单当日激活比例: 10.0%（15/150）
2025-06-12 08:00:05,515 - INFO - 样本订单日期检查 (共5个): 150，熟龄度分布: 当天=15, 昨天=68, 前天=31, 更早=36
2025-06-12 08:00:05,515 - INFO - 环比分析: 当前=240单, 前一日=217单, 变化=10.6%
2025-06-12 08:00:05,515 - INFO - 周趋势数据获取成功，共7天数据
2025-06-12 08:00:05,564 - INFO - 开始发送模板卡片消息到企业微信...
2025-06-12 08:00:05,564 - INFO - 开始发送消息到企业微信（全渠道生产环境）...
2025-06-12 08:00:05,834 - INFO - 企业微信API响应状态码: 200
2025-06-12 08:00:05,834 - INFO - 企业微信消息发送成功
2025-06-12 08:00:05,834 - INFO - 昨天的日报发送成功
2025-06-12 08:00:05,835 - INFO - 昨天的日报处理完成。
2025-06-12 08:00:05,835 - INFO - ==================================================
