# 环境配置
# 切换环境只需修改以下选项为True或False
USE_TEST_ENV = False      # 是否使用测试环境 
USE_PROD_OPPO = True      # 是否使用OPPO生产环境
USE_PROD_ALL = True       # 是否使用全渠道生产环境

# 数据库配置
DB_CONFIG = {
    'test': {
        "host": "*************",
        "port": 3306,
        "user": "dtstemushop",
        "password": "BKeFSsXNry7tMkfj",
        "database": "dtstemushop",
    },
    'prod_oppo': {
        "host": "*************",  # OPPO生产环境数据库
        "port": 3306,
        "user": "dtstemushop",
        "password": "BKeFSsXNry7tMkfj",
        "database": "dtstemushop",
    },
    'prod_all': {
        "host": "*************",  # 全渠道生产环境数据库
        "port": 3306,
        "user": "dtstemushop",
        "password": "BKeFSsXNry7tMkfj",
        "database": "dtstemushop",
    }
}

# 企业微信Webhook配置
WEBHOOK_URLS = {
    'test': "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=8da0513f-d860-4ed0-ac5f-a944234e6f3e",
    'prod_oppo': "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=bd8f830f-92bc-40b8-805c-d38298d249d2",  # OPPO正式
    'prod_all': "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=11037c2e-ab32-4c03-96c1-e8080dfd2975"   # 全渠道正式
}

# 环境名称映射
ENV_NAMES = {
    'test': '测试环境',
    'prod_oppo': 'OPPO生产环境',
    'prod_all': '全渠道生产环境'
}

# FTP服务器配置
FTP_CONFIG = {
    'host': '***************',
    'port': 21,
    'username': 'reports',
    'password': 'aAaNXpnEYHkZPNL4',
    'directory': 'reports',  # FTP服务器上的目录路径
    'base_url': 'https://rep.331126.com/reports/' # 已经包含了reports目录的URL
}

# 根据Python文件名获取当前环境
def get_current_env():
    import sys
    script_name = sys.argv[0].lower()
    
    if 'oppo' in script_name:
        # OPPO渠道报表
        if USE_TEST_ENV:
            return 'test'
        elif USE_PROD_OPPO:
            return 'prod_oppo'
        else:
            return 'test'  # 默认测试环境
    else:
        # 全渠道报表
        if USE_TEST_ENV:
            return 'test'
        elif USE_PROD_ALL:
            return 'prod_all'
        else:
            return 'test'  # 默认测试环境

# 获取当前环境的配置
def get_current_db_config():
    return DB_CONFIG[get_current_env()]

def get_current_webhook_url():
    return WEBHOOK_URLS[get_current_env()]

def get_current_env_name():
    return ENV_NAMES[get_current_env()]

# 获取FTP配置
def get_ftp_config():
    return FTP_CONFIG 