[2025-05-21 08:00:02] 开始执行日报脚本
[2025-05-21 08:00:02] 工作目录: /volume3/SSD-soft/fzsrb
[2025-05-21 08:00:02] 检查Python依赖...
WARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager, possibly rendering your system unusable. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv. Use the --root-user-action option if you know what you are doing and want to suppress this warning.
[2025-05-21 08:00:02] 开始执行简单日报脚本...
2025-05-21 08:00:04,179 - INFO - ==================================================
2025-05-21 08:00:04,179 - INFO - 开始生成日报统计数据...
2025-05-21 08:00:04,211 - INFO - 开始生成 2025-05-20 的报表数据...
2025-05-21 08:00:04,211 - INFO - 获取 2025-05-20 的订单数据（下单时间维度）...
2025-05-21 08:00:04,248 - INFO - 成功获取到 230 条去重后的订单数据（下单时间维度）
2025-05-21 08:00:04,248 - INFO - 开始分类并过滤订单数据...
2025-05-21 08:00:04,248 - INFO - 订单分类完成: 有效订单 230 单，其中骑手卡 224 单，流量卡 6 单
2025-05-21 08:00:04,248 - INFO - 计算各渠道的D0下单和D1激活数据...
2025-05-21 08:00:04,276 - INFO - 成功获取到 190 条当日激活订单
2025-05-21 08:00:04,280 - INFO - 样本订单日期检查 (共5个):
2025-05-21 08:00:04,280 - INFO - 订单 20250517153711255305: 下单时间=2025-05-17, 激活时间=2025-05-20, 相差=3天
2025-05-21 08:00:04,280 - INFO - 订单 20250519212860677424: 下单时间=2025-05-19, 激活时间=2025-05-20, 相差=1天
2025-05-21 08:00:04,280 - INFO - 订单 20250518140282627291: 下单时间=2025-05-18, 激活时间=2025-05-20, 相差=2天
2025-05-21 08:00:04,280 - INFO - 订单 20250519115888350734: 下单时间=2025-05-19, 激活时间=2025-05-20, 相差=1天
2025-05-21 08:00:04,280 - INFO - 订单 20250519105626803505: 下单时间=2025-05-19, 激活时间=2025-05-20, 相差=1天
2025-05-21 08:00:04,281 - INFO - 熟龄度处理统计: 总订单=190, 成功处理=190, 处理失败=0
2025-05-21 08:00:04,281 - INFO - 熟龄度分布: 当天=7, 昨天=115, 前天=40, 更早=28
2025-05-21 08:00:04,281 - INFO - 骑手卡熟龄度分布: 当天=7, 昨天=115, 前天=40, 更早=28
2025-05-21 08:00:04,281 - INFO - 流量卡熟龄度分布: 当天=0, 昨天=0, 前天=0, 更早=0
2025-05-21 08:00:04,281 - INFO - 获取报表日期与前一日订单数据对比...
2025-05-21 08:00:04,281 - INFO - 对比日期: 报表日期=2025-05-20, 前一日=2025-05-19
2025-05-21 08:00:04,281 - INFO - 获取 2025-05-20 的订单数据（下单时间维度）...
2025-05-21 08:00:04,310 - INFO - 成功获取到 230 条去重后的订单数据（下单时间维度）
2025-05-21 08:00:04,310 - INFO - 获取 2025-05-19 的订单数据（下单时间维度）...
2025-05-21 08:00:04,343 - INFO - 成功获取到 285 条去重后的订单数据（下单时间维度）
2025-05-21 08:00:04,344 - INFO - 获取周订单趋势数据...
2025-05-21 08:00:04,344 - INFO - 查询日期范围: 2025-05-14 00:00:00 至 2025-05-20 23:59:59
2025-05-21 08:00:04,369 - INFO - 成功获取到 7 天的订单趋势数据
2025-05-21 08:00:04,369 - INFO - 熟龄度数据: {'same_day': 7, 'one_day': 115, 'two_days': 40, 'more_days': 28}
2025-05-21 08:00:04,369 - INFO - 骑手卡熟龄度数据: {'same_day': 7, 'one_day': 115, 'two_days': 40, 'more_days': 28}, 总数: 190
2025-05-21 08:00:04,369 - INFO - 流量卡熟龄度数据: {'same_day': 0, 'one_day': 0, 'two_days': 0, 'more_days': 0}, 总数: 0
2025-05-21 08:00:04,369 - INFO - 当日下单当日激活比例: 3.7%（7/190）
2025-05-21 08:00:04,369 - INFO - 样本订单日期检查 (共5个): 190，熟龄度分布: 当天=7, 昨天=115, 前天=40, 更早=28
2025-05-21 08:00:04,369 - INFO - 环比分析: 当前=230单, 前一日=285单, 变化=-19.3%
2025-05-21 08:00:04,369 - INFO - 周趋势数据获取成功，共7天数据
2025-05-21 08:00:04,370 - INFO - 生成HTML报表模板，加载数据: 总活跃=190, 骑手卡=190, 流量卡=0
2025-05-21 08:00:04,370 - INFO - 骑手卡数据: [7, 115, 40, 28]
2025-05-21 08:00:04,370 - INFO - 流量卡数据: [0, 0, 0, 0]
2025-05-21 08:00:04,370 - INFO - HTML报表已生成: reports/report_20250520.html
2025-05-21 08:00:04,370 - INFO - 准备上传文件到FTP服务器: report_20250520.html
2025-05-21 08:00:04,371 - INFO - FTP配置: 主机=211.149.249.234, 端口=21, 目录=reports
2025-05-21 08:00:04,371 - INFO - 本地文件: reports/report_20250520.html, 大小: 54362 字节
2025-05-21 08:00:04,371 - INFO - 正在连接到FTP服务器: 211.149.249.234:21
2025-05-21 08:00:04,444 - INFO - 正在登录FTP服务器: 用户名=reports
2025-05-21 08:00:04,548 - INFO - FTP登录成功
2025-05-21 08:00:04,584 - INFO - 当前FTP目录: /
2025-05-21 08:00:04,584 - INFO - 尝试切换到目录: reports
2025-05-21 08:00:04,655 - INFO - 成功切换到目录: /reports
2025-05-21 08:00:04,795 - INFO - 上传前目录内容: ['report_20250506.html', 'report_20250511.html', 'report_20250515.html', 'report_20250518.html', 'report_20250516.html', 'report_20250512.html', 'report_20250503.html', 'report_20250508.html', 'report_20250502.html', 'report_20250519.html', '..', 'report_20250513.html', 'report_20250509.html', 'report_20250514.html', 'report_20250510.html', 'report_20250501.html', 'report_20250505.html', 'report_20250504.html', 'report_20250507.html', 'report_20250517.html', '.']
2025-05-21 08:00:04,795 - INFO - 开始上传文件: reports/report_20250520.html -> report_20250520.html
2025-05-21 08:00:05,047 - INFO - FTP上传结果: 226-File successfully transferred
226 0.108 seconds (measured here), 490.77 Kbytes per second
2025-05-21 08:00:05,184 - INFO - 上传后目录内容: ['report_20250506.html', 'report_20250511.html', 'report_20250515.html', 'report_20250518.html', 'report_20250516.html', 'report_20250512.html', 'report_20250503.html', 'report_20250508.html', 'report_20250502.html', 'report_20250519.html', '..', 'report_20250513.html', 'report_20250509.html', 'report_20250514.html', 'report_20250510.html', 'report_20250520.html', 'report_20250501.html', 'report_20250505.html', 'report_20250504.html', 'report_20250507.html', 'report_20250517.html', '.']
2025-05-21 08:00:05,185 - INFO - 文件已成功上传并验证: report_20250520.html
2025-05-21 08:00:05,185 - INFO - 文件访问URL: https://rep.331126.com/reports/report_20250520.html
2025-05-21 08:00:05,220 - INFO - FTP连接已关闭
2025-05-21 08:00:05,220 - INFO - 报表已上传到服务器，URL: https://rep.331126.com/reports/report_20250520.html
2025-05-21 08:00:05,220 - INFO - 日报生成完成
2025-05-21 08:00:05,220 - INFO - ==================================================
*get* '220---------- Welcome to Pure-FTPd [privsep] [TLS] ----------\n'
*get* '220-You are user number 1 of 50 allowed.\n'
*get* '220-Local time is now 08:00. Server port: 21.\n'
*get* '220-This is a private system - No anonymous login\n'
*get* '220-IPv6 connections are also welcome on this server.\n'
*get* '220 You will be disconnected after 15 minutes of inactivity.\n'
*resp* '220---------- Welcome to Pure-FTPd [privsep] [TLS] ----------\n220-You are user number 1 of 50 allowed.\n220-Local time is now 08:00. Server port: 21.\n220-This is a private system - No anonymous login\n220-IPv6 connections are also welcome on this server.\n220 You will be disconnected after 15 minutes of inactivity.'
*cmd* 'USER reports'
*put* 'USER reports\r\n'
*get* '331 User reports OK. Password required\n'
*resp* '331 User reports OK. Password required'
*cmd* 'PASS ****************'
*put* 'PASS ****************\r\n'
*get* '230 OK. Current directory is /\n'
*resp* '230 OK. Current directory is /'
*cmd* 'PWD'
*put* 'PWD\r\n'
*get* '257 "/" is your current location\n'
*resp* '257 "/" is your current location'
*cmd* 'CWD reports'
*put* 'CWD reports\r\n'
*get* '250 OK. Current directory is /reports\n'
*resp* '250 OK. Current directory is /reports'
*cmd* 'PWD'
*put* 'PWD\r\n'
*get* '257 "/reports" is your current location\n'
*resp* '257 "/reports" is your current location'
*cmd* 'TYPE A'
*put* 'TYPE A\r\n'
*get* '200 TYPE is now ASCII\n'
*resp* '200 TYPE is now ASCII'
*cmd* 'PASV'
*put* 'PASV\r\n'
*get* '227 Entering Passive Mode (211,149,249,234,153,229)\n'
*resp* '227 Entering Passive Mode (211,149,249,234,153,229)'
*cmd* 'NLST'
*put* 'NLST\r\n'
*get* '150 Accepted data connection\n'
*resp* '150 Accepted data connection'
*get* '226 21 matches total\n'
*resp* '226 21 matches total'
*cmd* 'TYPE I'
*put* 'TYPE I\r\n'
*get* '200 TYPE is now 8-bit binary\n'
*resp* '200 TYPE is now 8-bit binary'
*cmd* 'PASV'
*put* 'PASV\r\n'
*get* '227 Entering Passive Mode (211,149,249,234,153,234)\n'
*resp* '227 Entering Passive Mode (211,149,249,234,153,234)'
*cmd* 'STOR report_20250520.html'
*put* 'STOR report_20250520.html\r\n'
*get* '150 Accepted data connection\n'
*resp* '150 Accepted data connection'
*get* '226-File successfully transferred\n'
*get* '226 0.108 seconds (measured here), 490.77 Kbytes per second\n'
*resp* '226-File successfully transferred\n226 0.108 seconds (measured here), 490.77 Kbytes per second'
*cmd* 'TYPE A'
*put* 'TYPE A\r\n'
*get* '200 TYPE is now ASCII\n'
*resp* '200 TYPE is now ASCII'
*cmd* 'PASV'
*put* 'PASV\r\n'
*get* '227 Entering Passive Mode (211,149,249,234,154,219)\n'
*resp* '227 Entering Passive Mode (211,149,249,234,154,219)'
*cmd* 'NLST'
*put* 'NLST\r\n'
*get* '150 Accepted data connection\n'
*resp* '150 Accepted data connection'
*get* '226 22 matches total\n'
*resp* '226 22 matches total'
*cmd* 'QUIT'
*put* 'QUIT\r\n'
*get* '221-Goodbye. You uploaded 54 and downloaded 0 kbytes.\n'
*get* '221 Logout.\n'
*resp* '221-Goodbye. You uploaded 54 and downloaded 0 kbytes.\n221 Logout.'
[2025-05-21 08:00:02] 简单日报脚本执行成功
[2025-05-21 08:00:02] 开始执行全渠道日报脚本...
2025-05-21 08:00:05,453 - INFO - ==================================================
2025-05-21 08:00:05,453 - INFO - 开始生成并发送昨天的日报（全渠道生产环境）...
2025-05-21 08:00:05,453 - INFO - 开始执行昨天的日报发送流程（全渠道生产环境）...
2025-05-21 08:00:05,472 - INFO - 昨天日期: 2025-05-20
2025-05-21 08:00:05,472 - INFO - 开始获取昨天的订单数据（下单时间维度）...
2025-05-21 08:00:05,472 - INFO - 获取 2025-05-20 的订单数据（下单时间维度）...
2025-05-21 08:00:05,502 - INFO - 成功获取到 230 条去重后的订单数据（下单时间维度）
2025-05-21 08:00:05,503 - INFO - 开始获取昨天的订单数据（激活时间维度）...
2025-05-21 08:00:05,503 - INFO - 获取 2025-05-20 的订单数据（激活时间维度）...
2025-05-21 08:00:05,532 - INFO - 成功获取到 190 条去重后的订单数据（激活时间维度）
2025-05-21 08:00:05,532 - INFO - 成功获取到订单数据，继续生成日报
2025-05-21 08:00:05,532 - INFO - 开始生成昨天的日报...
2025-05-21 08:00:05,532 - INFO - 开始生成日报...
2025-05-21 08:00:05,532 - INFO - 开始分类并过滤订单数据...
2025-05-21 08:00:05,532 - INFO - 订单分类完成: 有效订单 230 单，其中骑手卡 224 单，大流量卡 6 单
2025-05-21 08:00:05,532 - INFO - 统计各类订单按渠道分类...
2025-05-21 08:00:05,532 - INFO - 开始分类并过滤订单数据...
2025-05-21 08:00:05,533 - INFO - 订单分类完成: 有效订单 190 单，其中骑手卡 190 单，大流量卡 0 单
2025-05-21 08:00:05,533 - INFO - 统计各类订单按渠道分类...
2025-05-21 08:00:05,533 - INFO - 日报生成完成
2025-05-21 08:00:05,533 - INFO - 开始分类并过滤订单数据...
2025-05-21 08:00:05,533 - INFO - 订单分类完成: 有效订单 230 单，其中骑手卡 224 单，大流量卡 6 单
2025-05-21 08:00:05,533 - INFO - 统计各类订单按渠道分类...
2025-05-21 08:00:05,533 - INFO - 开始分类并过滤订单数据...
2025-05-21 08:00:05,534 - INFO - 订单分类完成: 有效订单 190 单，其中骑手卡 190 单，大流量卡 0 单
2025-05-21 08:00:05,534 - INFO - 统计各类订单按渠道分类...
2025-05-21 08:00:05,536 - INFO - 开始生成 2025-05-20 的报表数据...
2025-05-21 08:00:05,536 - INFO - 获取 2025-05-20 的订单数据（下单时间维度）...
2025-05-21 08:00:05,566 - INFO - 成功获取到 230 条去重后的订单数据（下单时间维度）
2025-05-21 08:00:05,567 - INFO - 开始分类并过滤订单数据...
2025-05-21 08:00:05,567 - INFO - 订单分类完成: 有效订单 230 单，其中骑手卡 224 单，流量卡 6 单
2025-05-21 08:00:05,567 - INFO - 计算各渠道的D0下单和D1激活数据...
2025-05-21 08:00:05,595 - INFO - 成功获取到 190 条当日激活订单
2025-05-21 08:00:05,597 - INFO - 样本订单日期检查 (共5个):
2025-05-21 08:00:05,597 - INFO - 订单 20250517153711255305: 下单时间=2025-05-17, 激活时间=2025-05-20, 相差=3天
2025-05-21 08:00:05,597 - INFO - 订单 20250519212860677424: 下单时间=2025-05-19, 激活时间=2025-05-20, 相差=1天
2025-05-21 08:00:05,597 - INFO - 订单 20250518140282627291: 下单时间=2025-05-18, 激活时间=2025-05-20, 相差=2天
2025-05-21 08:00:05,597 - INFO - 订单 20250519115888350734: 下单时间=2025-05-19, 激活时间=2025-05-20, 相差=1天
2025-05-21 08:00:05,597 - INFO - 订单 20250519105626803505: 下单时间=2025-05-19, 激活时间=2025-05-20, 相差=1天
2025-05-21 08:00:05,597 - INFO - 熟龄度处理统计: 总订单=190, 成功处理=190, 处理失败=0
2025-05-21 08:00:05,597 - INFO - 熟龄度分布: 当天=7, 昨天=115, 前天=40, 更早=28
2025-05-21 08:00:05,598 - INFO - 骑手卡熟龄度分布: 当天=7, 昨天=115, 前天=40, 更早=28
2025-05-21 08:00:05,598 - INFO - 流量卡熟龄度分布: 当天=0, 昨天=0, 前天=0, 更早=0
2025-05-21 08:00:05,598 - INFO - 获取报表日期与前一日订单数据对比...
2025-05-21 08:00:05,598 - INFO - 对比日期: 报表日期=2025-05-20, 前一日=2025-05-19
2025-05-21 08:00:05,598 - INFO - 获取 2025-05-20 的订单数据（下单时间维度）...
2025-05-21 08:00:05,629 - INFO - 成功获取到 230 条去重后的订单数据（下单时间维度）
2025-05-21 08:00:05,629 - INFO - 获取 2025-05-19 的订单数据（下单时间维度）...
2025-05-21 08:00:05,664 - INFO - 成功获取到 285 条去重后的订单数据（下单时间维度）
2025-05-21 08:00:05,665 - INFO - 获取周订单趋势数据...
2025-05-21 08:00:05,665 - INFO - 查询日期范围: 2025-05-14 00:00:00 至 2025-05-20 23:59:59
2025-05-21 08:00:05,689 - INFO - 成功获取到 7 天的订单趋势数据
2025-05-21 08:00:05,689 - INFO - 熟龄度数据: {'same_day': 7, 'one_day': 115, 'two_days': 40, 'more_days': 28}
2025-05-21 08:00:05,689 - INFO - 骑手卡熟龄度数据: {'same_day': 7, 'one_day': 115, 'two_days': 40, 'more_days': 28}, 总数: 190
2025-05-21 08:00:05,689 - INFO - 流量卡熟龄度数据: {'same_day': 0, 'one_day': 0, 'two_days': 0, 'more_days': 0}, 总数: 0
2025-05-21 08:00:05,689 - INFO - 当日下单当日激活比例: 3.7%（7/190）
2025-05-21 08:00:05,689 - INFO - 样本订单日期检查 (共5个): 190，熟龄度分布: 当天=7, 昨天=115, 前天=40, 更早=28
2025-05-21 08:00:05,689 - INFO - 环比分析: 当前=230单, 前一日=285单, 变化=-19.3%
2025-05-21 08:00:05,689 - INFO - 周趋势数据获取成功，共7天数据
2025-05-21 08:00:05,689 - INFO - 开始发送模板卡片消息到企业微信...
2025-05-21 08:00:05,690 - INFO - 开始发送消息到企业微信（全渠道生产环境）...
2025-05-21 08:00:05,976 - INFO - 企业微信API响应状态码: 200
2025-05-21 08:00:05,976 - INFO - 企业微信消息发送成功
2025-05-21 08:00:05,976 - INFO - 昨天的日报发送成功
2025-05-21 08:00:05,976 - INFO - 昨天的日报处理完成。
2025-05-21 08:00:05,976 - INFO - ==================================================
[2025-05-21 08:00:02] 全渠道日报脚本执行成功
[2025-05-21 08:00:02] 开始执行OPPO渠道日报脚本...
2025-05-21 08:00:06,142 - INFO - ==================================================
2025-05-21 08:00:06,142 - INFO - 开始生成并发送OPPO和荣耀渠道日报（OPPO生产环境）...
2025-05-21 08:00:06,142 - INFO - 开始执行OPPO和荣耀渠道日报发送流程（OPPO生产环境）...
2025-05-21 08:00:06,159 - INFO - 查询日期: 2025-05-20
2025-05-21 08:00:06,159 - INFO - 获取 2025-05-20 的OPPO和荣耀渠道订单数据（下单时间维度）...
2025-05-21 08:00:06,179 - INFO - 查询到 4 条去重后的订单数据
2025-05-21 08:00:06,180 - INFO - 开始生成OPPO和荣耀渠道Markdown格式日报...
2025-05-21 08:00:06,180 - INFO - 开始处理订单数据，总数据条数: 4
2025-05-21 08:00:06,180 - INFO - 统计结果: 总订单 4
2025-05-21 08:00:06,180 - INFO -   OPPO: 3单
2025-05-21 08:00:06,180 - INFO -   荣耀: 1单
2025-05-21 08:00:06,180 - INFO - Markdown格式日报生成完成
2025-05-21 08:00:06,180 - INFO - 开始发送消息到企业微信（OPPO生产环境）...
2025-05-21 08:00:06,439 - INFO - 企业微信API响应状态码: 200
2025-05-21 08:00:06,440 - INFO - 企业微信消息发送成功
2025-05-21 08:00:06,440 - INFO - OPPO和荣耀渠道日报发送成功
2025-05-21 08:00:06,440 - INFO - OPPO和荣耀渠道日报处理完成。
2025-05-21 08:00:06,440 - INFO - ==================================================
[2025-05-21 08:00:02] OPPO渠道日报脚本执行成功
[2025-05-21 08:00:06] 所有日报脚本执行完成
----------------------------------------
