[2025-06-10 08:00:02] 开始执行日报脚本
[2025-06-10 08:00:02] 工作目录: /volume3/SSD-soft/fzsrb
[2025-06-10 08:00:02] 检查Python依赖...
WARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager, possibly rendering your system unusable. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv. Use the --root-user-action option if you know what you are doing and want to suppress this warning.
[2025-06-10 08:00:02] 开始执行简单日报脚本...
2025-06-10 08:00:04,244 - INFO - ==================================================
2025-06-10 08:00:04,244 - INFO - 开始生成日报统计数据...
2025-06-10 08:00:04,276 - INFO - 开始生成 2025-06-09 的报表数据...
2025-06-10 08:00:04,276 - INFO - 获取 2025-06-09 的订单数据（下单时间维度）...
2025-06-10 08:00:04,327 - INFO - 成功获取到 217 条去重后的订单数据（下单时间维度）
2025-06-10 08:00:04,327 - INFO - 开始分类并过滤订单数据...
2025-06-10 08:00:04,327 - INFO - 订单分类完成: 有效订单 217 单，其中骑手卡 214 单，流量卡 3 单
2025-06-10 08:00:04,327 - INFO - 计算各渠道的D0下单和D1激活数据...
2025-06-10 08:00:04,361 - INFO - 成功获取到 151 条当日激活订单
2025-06-10 08:00:04,365 - INFO - 样本订单日期检查 (共5个):
2025-06-10 08:00:04,365 - INFO - 订单 20250608151110872509: 下单时间=2025-06-08, 激活时间=2025-06-09, 相差=1天
2025-06-10 08:00:04,365 - INFO - 订单 20250606140883045996: 下单时间=2025-06-06, 激活时间=2025-06-09, 相差=3天
2025-06-10 08:00:04,365 - INFO - 订单 20250603150532027233: 下单时间=2025-06-03, 激活时间=2025-06-09, 相差=6天
2025-06-10 08:00:04,365 - INFO - 订单 20250608210085420347: 下单时间=2025-06-08, 激活时间=2025-06-09, 相差=1天
2025-06-10 08:00:04,365 - INFO - 订单 20250606104266604923: 下单时间=2025-06-06, 激活时间=2025-06-09, 相差=3天
2025-06-10 08:00:04,366 - INFO - 熟龄度处理统计: 总订单=151, 成功处理=151, 处理失败=0
2025-06-10 08:00:04,366 - INFO - 熟龄度分布: 当天=10, 昨天=60, 前天=45, 更早=36
2025-06-10 08:00:04,366 - INFO - 骑手卡熟龄度分布: 当天=10, 昨天=60, 前天=45, 更早=36
2025-06-10 08:00:04,366 - INFO - 流量卡熟龄度分布: 当天=0, 昨天=0, 前天=0, 更早=0
2025-06-10 08:00:04,366 - INFO - 获取报表日期与前一日订单数据对比...
2025-06-10 08:00:04,366 - INFO - 对比日期: 报表日期=2025-06-09, 前一日=2025-06-08
2025-06-10 08:00:04,366 - INFO - 获取 2025-06-09 的订单数据（下单时间维度）...
2025-06-10 08:00:04,404 - INFO - 成功获取到 217 条去重后的订单数据（下单时间维度）
2025-06-10 08:00:04,405 - INFO - 获取 2025-06-08 的订单数据（下单时间维度）...
2025-06-10 08:00:04,439 - INFO - 成功获取到 153 条去重后的订单数据（下单时间维度）
2025-06-10 08:00:04,439 - INFO - 获取周订单趋势数据...
2025-06-10 08:00:04,440 - INFO - 查询日期范围: 2025-06-03 00:00:00 至 2025-06-09 23:59:59
2025-06-10 08:00:04,467 - INFO - 成功获取到 7 天的订单趋势数据
2025-06-10 08:00:04,467 - INFO - 熟龄度数据: {'same_day': 10, 'one_day': 60, 'two_days': 45, 'more_days': 36}
2025-06-10 08:00:04,467 - INFO - 骑手卡熟龄度数据: {'same_day': 10, 'one_day': 60, 'two_days': 45, 'more_days': 36}, 总数: 151
2025-06-10 08:00:04,467 - INFO - 流量卡熟龄度数据: {'same_day': 0, 'one_day': 0, 'two_days': 0, 'more_days': 0}, 总数: 0
2025-06-10 08:00:04,467 - INFO - 当日下单当日激活比例: 6.6%（10/151）
2025-06-10 08:00:04,467 - INFO - 样本订单日期检查 (共5个): 151，熟龄度分布: 当天=10, 昨天=60, 前天=45, 更早=36
2025-06-10 08:00:04,467 - INFO - 环比分析: 当前=217单, 前一日=153单, 变化=41.8%
2025-06-10 08:00:04,467 - INFO - 周趋势数据获取成功，共7天数据
2025-06-10 08:00:04,468 - INFO - 生成HTML报表模板，加载数据: 总活跃=151, 骑手卡=151, 流量卡=0
2025-06-10 08:00:04,468 - INFO - 骑手卡数据: [10, 60, 45, 36]
2025-06-10 08:00:04,468 - INFO - 流量卡数据: [0, 0, 0, 0]
2025-06-10 08:00:04,469 - INFO - HTML报表已生成: reports/report_20250609.html
2025-06-10 08:00:04,469 - INFO - 准备上传文件到FTP服务器: report_20250609.html
2025-06-10 08:00:04,469 - INFO - FTP配置: 主机=211.149.249.234, 端口=21, 目录=reports
2025-06-10 08:00:04,469 - INFO - 本地文件: reports/report_20250609.html, 大小: 52728 字节
2025-06-10 08:00:04,469 - INFO - 正在连接到FTP服务器: 211.149.249.234:21
2025-06-10 08:00:04,534 - INFO - 正在登录FTP服务器: 用户名=reports
2025-06-10 08:00:04,628 - INFO - FTP登录成功
2025-06-10 08:00:04,658 - INFO - 当前FTP目录: /
2025-06-10 08:00:04,658 - INFO - 尝试切换到目录: reports
2025-06-10 08:00:04,725 - INFO - 成功切换到目录: /reports
2025-06-10 08:00:04,854 - INFO - 上传前目录内容: ['report_20250506.html', 'report_20250522.html', 'report_20250511.html', 'report_20250515.html', 'report_20250605.html', 'report_20250518.html', 'report_20250516.html', 'report_20250512.html', 'report_20250503.html', 'report_20250602.html', 'report_20250508.html', 'report_20250502.html', 'report_20250601.html', 'report_20250531.html', 'report_20250519.html', 'report_20250608.html', 'report_20250525.html', '..', 'report_20250513.html', 'report_20250527.html', 'report_20250524.html', 'report_20250528.html', 'report_20250606.html', 'report_20250509.html', 'report_20250514.html', 'report_20250604.html', 'report_20250510.html', 'report_20250520.html', 'report_20250521.html', 'report_20250523.html', 'report_20250530.html', 'report_20250607.html', 'report_20250529.html', 'report_20250501.html', 'report_20250505.html', 'report_20250504.html', 'report_20250507.html', 'report_20250517.html', '.', 'report_20250526.html', 'report_20250603.html']
2025-06-10 08:00:04,854 - INFO - 开始上传文件: reports/report_20250609.html -> report_20250609.html
2025-06-10 08:00:05,072 - INFO - FTP上传结果: 226-File successfully transferred
226 0.093 seconds (measured here), 0.54 Mbytes per second
2025-06-10 08:00:05,196 - INFO - 上传后目录内容: ['report_20250506.html', 'report_20250522.html', 'report_20250511.html', 'report_20250515.html', 'report_20250605.html', 'report_20250518.html', 'report_20250516.html', 'report_20250512.html', 'report_20250503.html', 'report_20250602.html', 'report_20250508.html', 'report_20250609.html', 'report_20250502.html', 'report_20250601.html', 'report_20250531.html', 'report_20250519.html', 'report_20250608.html', 'report_20250525.html', '..', 'report_20250513.html', 'report_20250527.html', 'report_20250524.html', 'report_20250528.html', 'report_20250606.html', 'report_20250509.html', 'report_20250514.html', 'report_20250604.html', 'report_20250510.html', 'report_20250520.html', 'report_20250521.html', 'report_20250523.html', 'report_20250530.html', 'report_20250607.html', 'report_20250529.html', 'report_20250501.html', 'report_20250505.html', 'report_20250504.html', 'report_20250507.html', 'report_20250517.html', '.', 'report_20250526.html', 'report_20250603.html']
2025-06-10 08:00:05,196 - INFO - 文件已成功上传并验证: report_20250609.html
2025-06-10 08:00:05,196 - INFO - 文件访问URL: https://rep.331126.com/reports/report_20250609.html
2025-06-10 08:00:05,227 - INFO - FTP连接已关闭
2025-06-10 08:00:05,227 - INFO - 报表已上传到服务器，URL: https://rep.331126.com/reports/report_20250609.html
2025-06-10 08:00:05,227 - INFO - 日报生成完成
2025-06-10 08:00:05,227 - INFO - ==================================================
*get* '220---------- Welcome to Pure-FTPd [privsep] [TLS] ----------\n'
*get* '220-You are user number 1 of 50 allowed.\n'
*get* '220-Local time is now 08:00. Server port: 21.\n'
*get* '220-This is a private system - No anonymous login\n'
*get* '220-IPv6 connections are also welcome on this server.\n'
*get* '220 You will be disconnected after 15 minutes of inactivity.\n'
*resp* '220---------- Welcome to Pure-FTPd [privsep] [TLS] ----------\n220-You are user number 1 of 50 allowed.\n220-Local time is now 08:00. Server port: 21.\n220-This is a private system - No anonymous login\n220-IPv6 connections are also welcome on this server.\n220 You will be disconnected after 15 minutes of inactivity.'
*cmd* 'USER reports'
*put* 'USER reports\r\n'
*get* '331 User reports OK. Password required\n'
*resp* '331 User reports OK. Password required'
*cmd* 'PASS ****************'
*put* 'PASS ****************\r\n'
*get* '230 OK. Current directory is /\n'
*resp* '230 OK. Current directory is /'
*cmd* 'PWD'
*put* 'PWD\r\n'
*get* '257 "/" is your current location\n'
*resp* '257 "/" is your current location'
*cmd* 'CWD reports'
*put* 'CWD reports\r\n'
*get* '250 OK. Current directory is /reports\n'
*resp* '250 OK. Current directory is /reports'
*cmd* 'PWD'
*put* 'PWD\r\n'
*get* '257 "/reports" is your current location\n'
*resp* '257 "/reports" is your current location'
*cmd* 'TYPE A'
*put* 'TYPE A\r\n'
*get* '200 TYPE is now ASCII\n'
*resp* '200 TYPE is now ASCII'
*cmd* 'PASV'
*put* 'PASV\r\n'
*get* '227 Entering Passive Mode (211,149,249,234,155,118)\n'
*resp* '227 Entering Passive Mode (211,149,249,234,155,118)'
*cmd* 'NLST'
*put* 'NLST\r\n'
*get* '150 Accepted data connection\n'
*resp* '150 Accepted data connection'
*get* '226 41 matches total\n'
*resp* '226 41 matches total'
*cmd* 'TYPE I'
*put* 'TYPE I\r\n'
*get* '200 TYPE is now 8-bit binary\n'
*resp* '200 TYPE is now 8-bit binary'
*cmd* 'PASV'
*put* 'PASV\r\n'
*get* '227 Entering Passive Mode (211,149,249,234,154,134)\n'
*resp* '227 Entering Passive Mode (211,149,249,234,154,134)'
*cmd* 'STOR report_20250609.html'
*put* 'STOR report_20250609.html\r\n'
*get* '150 Accepted data connection\n'
*resp* '150 Accepted data connection'
*get* '226-File successfully transferred\n'
*get* '226 0.093 seconds (measured here), 0.54 Mbytes per second\n'
*resp* '226-File successfully transferred\n226 0.093 seconds (measured here), 0.54 Mbytes per second'
*cmd* 'TYPE A'
*put* 'TYPE A\r\n'
*get* '200 TYPE is now ASCII\n'
*resp* '200 TYPE is now ASCII'
*cmd* 'PASV'
*put* 'PASV\r\n'
*get* '227 Entering Passive Mode (211,149,249,234,154,167)\n'
*resp* '227 Entering Passive Mode (211,149,249,234,154,167)'
*cmd* 'NLST'
*put* 'NLST\r\n'
*get* '150 Accepted data connection\n'
*resp* '150 Accepted data connection'
*get* '226 42 matches total\n'
*resp* '226 42 matches total'
*cmd* 'QUIT'
*put* 'QUIT\r\n'
*get* '221-Goodbye. You uploaded 52 and downloaded 0 kbytes.\n'
*get* '221 Logout.\n'
*resp* '221-Goodbye. You uploaded 52 and downloaded 0 kbytes.\n221 Logout.'
[2025-06-10 08:00:02] 简单日报脚本执行成功
[2025-06-10 08:00:02] 开始执行全渠道日报脚本...
2025-06-10 08:00:05,425 - INFO - ==================================================
2025-06-10 08:00:05,425 - INFO - 开始生成并发送昨天的日报（全渠道生产环境）...
2025-06-10 08:00:05,425 - INFO - 开始执行昨天的日报发送流程（全渠道生产环境）...
2025-06-10 08:00:05,444 - INFO - 昨天日期: 2025-06-09
2025-06-10 08:00:05,444 - INFO - 开始获取昨天的订单数据（下单时间维度）...
2025-06-10 08:00:05,444 - INFO - 获取 2025-06-09 的订单数据（下单时间维度）...
2025-06-10 08:00:05,482 - INFO - 成功获取到 217 条去重后的订单数据（下单时间维度）
2025-06-10 08:00:05,482 - INFO - 开始获取昨天的订单数据（激活时间维度）...
2025-06-10 08:00:05,482 - INFO - 获取 2025-06-09 的订单数据（激活时间维度）...
2025-06-10 08:00:05,516 - INFO - 成功获取到 151 条去重后的订单数据（激活时间维度）
2025-06-10 08:00:05,516 - INFO - 成功获取到订单数据，继续生成日报
2025-06-10 08:00:05,516 - INFO - 开始生成昨天的日报...
2025-06-10 08:00:05,516 - INFO - 开始生成日报...
2025-06-10 08:00:05,516 - INFO - 开始分类并过滤订单数据...
2025-06-10 08:00:05,516 - INFO - 订单分类完成: 有效订单 217 单，其中骑手卡 214 单，大流量卡 3 单
2025-06-10 08:00:05,516 - INFO - 统计各类订单按渠道分类...
2025-06-10 08:00:05,517 - INFO - 开始分类并过滤订单数据...
2025-06-10 08:00:05,517 - INFO - 订单分类完成: 有效订单 151 单，其中骑手卡 151 单，大流量卡 0 单
2025-06-10 08:00:05,517 - INFO - 统计各类订单按渠道分类...
2025-06-10 08:00:05,517 - INFO - 日报生成完成
2025-06-10 08:00:05,517 - INFO - 开始分类并过滤订单数据...
2025-06-10 08:00:05,517 - INFO - 订单分类完成: 有效订单 217 单，其中骑手卡 214 单，大流量卡 3 单
2025-06-10 08:00:05,517 - INFO - 统计各类订单按渠道分类...
2025-06-10 08:00:05,518 - INFO - 开始分类并过滤订单数据...
2025-06-10 08:00:05,518 - INFO - 订单分类完成: 有效订单 151 单，其中骑手卡 151 单，大流量卡 0 单
2025-06-10 08:00:05,518 - INFO - 统计各类订单按渠道分类...
2025-06-10 08:00:05,520 - INFO - 开始生成 2025-06-09 的报表数据...
2025-06-10 08:00:05,520 - INFO - 获取 2025-06-09 的订单数据（下单时间维度）...
2025-06-10 08:00:05,556 - INFO - 成功获取到 217 条去重后的订单数据（下单时间维度）
2025-06-10 08:00:05,557 - INFO - 开始分类并过滤订单数据...
2025-06-10 08:00:05,557 - INFO - 订单分类完成: 有效订单 217 单，其中骑手卡 214 单，流量卡 3 单
2025-06-10 08:00:05,557 - INFO - 计算各渠道的D0下单和D1激活数据...
2025-06-10 08:00:05,590 - INFO - 成功获取到 151 条当日激活订单
2025-06-10 08:00:05,592 - INFO - 样本订单日期检查 (共5个):
2025-06-10 08:00:05,592 - INFO - 订单 20250608151110872509: 下单时间=2025-06-08, 激活时间=2025-06-09, 相差=1天
2025-06-10 08:00:05,592 - INFO - 订单 20250606140883045996: 下单时间=2025-06-06, 激活时间=2025-06-09, 相差=3天
2025-06-10 08:00:05,593 - INFO - 订单 20250603150532027233: 下单时间=2025-06-03, 激活时间=2025-06-09, 相差=6天
2025-06-10 08:00:05,593 - INFO - 订单 20250608210085420347: 下单时间=2025-06-08, 激活时间=2025-06-09, 相差=1天
2025-06-10 08:00:05,593 - INFO - 订单 20250606104266604923: 下单时间=2025-06-06, 激活时间=2025-06-09, 相差=3天
2025-06-10 08:00:05,593 - INFO - 熟龄度处理统计: 总订单=151, 成功处理=151, 处理失败=0
2025-06-10 08:00:05,593 - INFO - 熟龄度分布: 当天=10, 昨天=60, 前天=45, 更早=36
2025-06-10 08:00:05,593 - INFO - 骑手卡熟龄度分布: 当天=10, 昨天=60, 前天=45, 更早=36
2025-06-10 08:00:05,593 - INFO - 流量卡熟龄度分布: 当天=0, 昨天=0, 前天=0, 更早=0
2025-06-10 08:00:05,593 - INFO - 获取报表日期与前一日订单数据对比...
2025-06-10 08:00:05,593 - INFO - 对比日期: 报表日期=2025-06-09, 前一日=2025-06-08
2025-06-10 08:00:05,594 - INFO - 获取 2025-06-09 的订单数据（下单时间维度）...
2025-06-10 08:00:05,630 - INFO - 成功获取到 217 条去重后的订单数据（下单时间维度）
2025-06-10 08:00:05,630 - INFO - 获取 2025-06-08 的订单数据（下单时间维度）...
2025-06-10 08:00:05,662 - INFO - 成功获取到 153 条去重后的订单数据（下单时间维度）
2025-06-10 08:00:05,663 - INFO - 获取周订单趋势数据...
2025-06-10 08:00:05,663 - INFO - 查询日期范围: 2025-06-03 00:00:00 至 2025-06-09 23:59:59
2025-06-10 08:00:05,688 - INFO - 成功获取到 7 天的订单趋势数据
2025-06-10 08:00:05,689 - INFO - 熟龄度数据: {'same_day': 10, 'one_day': 60, 'two_days': 45, 'more_days': 36}
2025-06-10 08:00:05,689 - INFO - 骑手卡熟龄度数据: {'same_day': 10, 'one_day': 60, 'two_days': 45, 'more_days': 36}, 总数: 151
2025-06-10 08:00:05,689 - INFO - 流量卡熟龄度数据: {'same_day': 0, 'one_day': 0, 'two_days': 0, 'more_days': 0}, 总数: 0
2025-06-10 08:00:05,689 - INFO - 当日下单当日激活比例: 6.6%（10/151）
2025-06-10 08:00:05,689 - INFO - 样本订单日期检查 (共5个): 151，熟龄度分布: 当天=10, 昨天=60, 前天=45, 更早=36
2025-06-10 08:00:05,689 - INFO - 环比分析: 当前=217单, 前一日=153单, 变化=41.8%
2025-06-10 08:00:05,689 - INFO - 周趋势数据获取成功，共7天数据
2025-06-10 08:00:05,736 - INFO - 开始发送模板卡片消息到企业微信...
2025-06-10 08:00:05,737 - INFO - 开始发送消息到企业微信（全渠道生产环境）...
2025-06-10 08:00:06,041 - INFO - 企业微信API响应状态码: 200
2025-06-10 08:00:06,042 - INFO - 企业微信消息发送成功
2025-06-10 08:00:06,042 - INFO - 昨天的日报发送成功
2025-06-10 08:00:06,042 - INFO - 昨天的日报处理完成。
2025-06-10 08:00:06,042 - INFO - ==================================================
[2025-06-10 08:00:02] 全渠道日报脚本执行成功
[2025-06-10 08:00:02] 开始执行OPPO渠道日报脚本...
2025-06-10 08:00:06,219 - INFO - ==================================================
2025-06-10 08:00:06,219 - INFO - 开始生成并发送OPPO和荣耀渠道日报（OPPO生产环境）...
2025-06-10 08:00:06,219 - INFO - 开始执行OPPO和荣耀渠道日报发送流程（OPPO生产环境）...
2025-06-10 08:00:06,238 - INFO - 查询日期: 2025-06-09
2025-06-10 08:00:06,239 - INFO - 获取 2025-06-09 的OPPO和荣耀渠道订单数据（下单时间维度）...
2025-06-10 08:00:06,265 - INFO - 查询到 0 条去重后的订单数据
2025-06-10 08:00:06,265 - WARNING - 未获取到OPPO和荣耀渠道订单数据，取消发送日报
2025-06-10 08:00:06,265 - WARNING - 由于没有数据，日报未发送
2025-06-10 08:00:06,265 - INFO - ==================================================
[2025-06-10 08:00:02] OPPO渠道日报脚本执行成功
[2025-06-10 08:00:02] 开始执行驿舟渠道日报脚本...
2025-06-10 08:00:06,437 - INFO - ==================================================
2025-06-10 08:00:06,437 - INFO - 开始生成并发送驿舟相关渠道日报（驿舟日报环境）...
2025-06-10 08:00:06,437 - INFO - 开始执行驿舟相关渠道日报发送流程（驿舟日报环境）...
2025-06-10 08:00:06,454 - INFO - 获取 2025-06-09 的驿舟相关渠道订单数据（下单时间维度）...
2025-06-10 08:00:06,484 - INFO - 查询到 54 条去重后的订单数据
2025-06-10 08:00:06,485 - INFO - 获取 2025-06-09 的驿舟相关渠道订单数据（激活时间维度）...
2025-06-10 08:00:06,513 - INFO - 查询到 45 条去重后的订单数据
2025-06-10 08:00:06,572 - INFO - 开始发送消息到企业微信（驿舟日报环境）...
2025-06-10 08:00:06,761 - INFO - 企业微信API响应状态码: 200
2025-06-10 08:00:06,762 - INFO - 企业微信消息发送成功
2025-06-10 08:00:06,762 - INFO - 驿舟相关渠道日报发送成功
2025-06-10 08:00:06,762 - INFO - 驿舟相关渠道日报处理完成。
2025-06-10 08:00:06,762 - INFO - ==================================================
[2025-06-10 08:00:02] 驿舟渠道日报脚本执行成功
[2025-06-10 08:00:06] 所有日报脚本执行完成
----------------------------------------
