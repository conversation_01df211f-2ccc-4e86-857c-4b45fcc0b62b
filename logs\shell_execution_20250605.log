[2025-06-05 08:00:02] 开始执行日报脚本
[2025-06-05 08:00:02] 工作目录: /volume3/SSD-soft/fzsrb
[2025-06-05 08:00:02] 检查Python依赖...
WARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager, possibly rendering your system unusable. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv. Use the --root-user-action option if you know what you are doing and want to suppress this warning.
[2025-06-05 08:00:02] 开始执行简单日报脚本...
2025-06-05 08:00:04,124 - INFO - ==================================================
2025-06-05 08:00:04,124 - INFO - 开始生成日报统计数据...
2025-06-05 08:00:04,156 - INFO - 开始生成 2025-06-04 的报表数据...
2025-06-05 08:00:04,156 - INFO - 获取 2025-06-04 的订单数据（下单时间维度）...
2025-06-05 08:00:04,202 - INFO - 成功获取到 195 条去重后的订单数据（下单时间维度）
2025-06-05 08:00:04,203 - INFO - 开始分类并过滤订单数据...
2025-06-05 08:00:04,203 - INFO - 订单分类完成: 有效订单 195 单，其中骑手卡 193 单，流量卡 2 单
2025-06-05 08:00:04,203 - INFO - 计算各渠道的D0下单和D1激活数据...
2025-06-05 08:00:04,237 - INFO - 成功获取到 153 条当日激活订单
2025-06-05 08:00:04,241 - INFO - 样本订单日期检查 (共5个):
2025-06-05 08:00:04,241 - INFO - 订单 20250603103137893072: 下单时间=2025-06-03, 激活时间=2025-06-04, 相差=1天
2025-06-05 08:00:04,241 - INFO - 订单 20250603095059097456: 下单时间=2025-06-03, 激活时间=2025-06-04, 相差=1天
2025-06-05 08:00:04,241 - INFO - 订单 20250604072245351349: 下单时间=2025-06-04, 激活时间=2025-06-04, 相差=0天
2025-06-05 08:00:04,241 - INFO - 订单 20250602203784645358: 下单时间=2025-06-02, 激活时间=2025-06-04, 相差=2天
2025-06-05 08:00:04,241 - INFO - 订单 20250604080821264865: 下单时间=2025-06-04, 激活时间=2025-06-04, 相差=0天
2025-06-05 08:00:04,241 - INFO - 熟龄度处理统计: 总订单=153, 成功处理=153, 处理失败=0
2025-06-05 08:00:04,241 - INFO - 熟龄度分布: 当天=17, 昨天=75, 前天=26, 更早=35
2025-06-05 08:00:04,242 - INFO - 骑手卡熟龄度分布: 当天=17, 昨天=75, 前天=26, 更早=35
2025-06-05 08:00:04,242 - INFO - 流量卡熟龄度分布: 当天=0, 昨天=0, 前天=0, 更早=0
2025-06-05 08:00:04,242 - INFO - 获取报表日期与前一日订单数据对比...
2025-06-05 08:00:04,242 - INFO - 对比日期: 报表日期=2025-06-04, 前一日=2025-06-03
2025-06-05 08:00:04,242 - INFO - 获取 2025-06-04 的订单数据（下单时间维度）...
2025-06-05 08:00:04,277 - INFO - 成功获取到 195 条去重后的订单数据（下单时间维度）
2025-06-05 08:00:04,278 - INFO - 获取 2025-06-03 的订单数据（下单时间维度）...
2025-06-05 08:00:04,312 - INFO - 成功获取到 191 条去重后的订单数据（下单时间维度）
2025-06-05 08:00:04,313 - INFO - 获取周订单趋势数据...
2025-06-05 08:00:04,313 - INFO - 查询日期范围: 2025-05-29 00:00:00 至 2025-06-04 23:59:59
2025-06-05 08:00:04,339 - INFO - 成功获取到 7 天的订单趋势数据
2025-06-05 08:00:04,339 - INFO - 熟龄度数据: {'same_day': 17, 'one_day': 75, 'two_days': 26, 'more_days': 35}
2025-06-05 08:00:04,339 - INFO - 骑手卡熟龄度数据: {'same_day': 17, 'one_day': 75, 'two_days': 26, 'more_days': 35}, 总数: 153
2025-06-05 08:00:04,339 - INFO - 流量卡熟龄度数据: {'same_day': 0, 'one_day': 0, 'two_days': 0, 'more_days': 0}, 总数: 0
2025-06-05 08:00:04,339 - INFO - 当日下单当日激活比例: 11.1%（17/153）
2025-06-05 08:00:04,339 - INFO - 样本订单日期检查 (共5个): 153，熟龄度分布: 当天=17, 昨天=75, 前天=26, 更早=35
2025-06-05 08:00:04,339 - INFO - 环比分析: 当前=195单, 前一日=191单, 变化=2.1%
2025-06-05 08:00:04,339 - INFO - 周趋势数据获取成功，共7天数据
2025-06-05 08:00:04,340 - INFO - 生成HTML报表模板，加载数据: 总活跃=153, 骑手卡=153, 流量卡=0
2025-06-05 08:00:04,340 - INFO - 骑手卡数据: [17, 75, 26, 35]
2025-06-05 08:00:04,340 - INFO - 流量卡数据: [0, 0, 0, 0]
2025-06-05 08:00:04,340 - INFO - HTML报表已生成: reports/report_20250604.html
2025-06-05 08:00:04,341 - INFO - 准备上传文件到FTP服务器: report_20250604.html
2025-06-05 08:00:04,341 - INFO - FTP配置: 主机=211.149.249.234, 端口=21, 目录=reports
2025-06-05 08:00:04,341 - INFO - 本地文件: reports/report_20250604.html, 大小: 50943 字节
2025-06-05 08:00:04,341 - INFO - 正在连接到FTP服务器: 211.149.249.234:21
2025-06-05 08:00:04,403 - INFO - 正在登录FTP服务器: 用户名=reports
2025-06-05 08:00:04,493 - INFO - FTP登录成功
2025-06-05 08:00:04,522 - INFO - 当前FTP目录: /
2025-06-05 08:00:04,522 - INFO - 尝试切换到目录: reports
2025-06-05 08:00:04,582 - INFO - 成功切换到目录: /reports
2025-06-05 08:00:04,709 - INFO - 上传前目录内容: ['report_20250506.html', 'report_20250522.html', 'report_20250511.html', 'report_20250515.html', 'report_20250518.html', 'report_20250516.html', 'report_20250512.html', 'report_20250503.html', 'report_20250602.html', 'report_20250508.html', 'report_20250502.html', 'report_20250601.html', 'report_20250531.html', 'report_20250519.html', 'report_20250525.html', '..', 'report_20250513.html', 'report_20250527.html', 'report_20250524.html', 'report_20250528.html', 'report_20250509.html', 'report_20250514.html', 'report_20250510.html', 'report_20250520.html', 'report_20250521.html', 'report_20250523.html', 'report_20250530.html', 'report_20250529.html', 'report_20250501.html', 'report_20250505.html', 'report_20250504.html', 'report_20250507.html', 'report_20250517.html', '.', 'report_20250526.html', 'report_20250603.html']
2025-06-05 08:00:04,709 - INFO - 开始上传文件: reports/report_20250604.html -> report_20250604.html
2025-06-05 08:00:04,928 - INFO - FTP上传结果: 226-File successfully transferred
226 0.096 seconds (measured here), 0.50 Mbytes per second
2025-06-05 08:00:05,054 - INFO - 上传后目录内容: ['report_20250506.html', 'report_20250522.html', 'report_20250511.html', 'report_20250515.html', 'report_20250518.html', 'report_20250516.html', 'report_20250512.html', 'report_20250503.html', 'report_20250602.html', 'report_20250508.html', 'report_20250502.html', 'report_20250601.html', 'report_20250531.html', 'report_20250519.html', 'report_20250525.html', '..', 'report_20250513.html', 'report_20250527.html', 'report_20250524.html', 'report_20250528.html', 'report_20250509.html', 'report_20250514.html', 'report_20250604.html', 'report_20250510.html', 'report_20250520.html', 'report_20250521.html', 'report_20250523.html', 'report_20250530.html', 'report_20250529.html', 'report_20250501.html', 'report_20250505.html', 'report_20250504.html', 'report_20250507.html', 'report_20250517.html', '.', 'report_20250526.html', 'report_20250603.html']
2025-06-05 08:00:05,054 - INFO - 文件已成功上传并验证: report_20250604.html
2025-06-05 08:00:05,054 - INFO - 文件访问URL: https://rep.331126.com/reports/report_20250604.html
2025-06-05 08:00:05,084 - INFO - FTP连接已关闭
2025-06-05 08:00:05,084 - INFO - 报表已上传到服务器，URL: https://rep.331126.com/reports/report_20250604.html
2025-06-05 08:00:05,084 - INFO - 日报生成完成
2025-06-05 08:00:05,084 - INFO - ==================================================
*get* '220---------- Welcome to Pure-FTPd [privsep] [TLS] ----------\n'
*get* '220-You are user number 1 of 50 allowed.\n'
*get* '220-Local time is now 08:00. Server port: 21.\n'
*get* '220-This is a private system - No anonymous login\n'
*get* '220-IPv6 connections are also welcome on this server.\n'
*get* '220 You will be disconnected after 15 minutes of inactivity.\n'
*resp* '220---------- Welcome to Pure-FTPd [privsep] [TLS] ----------\n220-You are user number 1 of 50 allowed.\n220-Local time is now 08:00. Server port: 21.\n220-This is a private system - No anonymous login\n220-IPv6 connections are also welcome on this server.\n220 You will be disconnected after 15 minutes of inactivity.'
*cmd* 'USER reports'
*put* 'USER reports\r\n'
*get* '331 User reports OK. Password required\n'
*resp* '331 User reports OK. Password required'
*cmd* 'PASS ****************'
*put* 'PASS ****************\r\n'
*get* '230 OK. Current directory is /\n'
*resp* '230 OK. Current directory is /'
*cmd* 'PWD'
*put* 'PWD\r\n'
*get* '257 "/" is your current location\n'
*resp* '257 "/" is your current location'
*cmd* 'CWD reports'
*put* 'CWD reports\r\n'
*get* '250 OK. Current directory is /reports\n'
*resp* '250 OK. Current directory is /reports'
*cmd* 'PWD'
*put* 'PWD\r\n'
*get* '257 "/reports" is your current location\n'
*resp* '257 "/reports" is your current location'
*cmd* 'TYPE A'
*put* 'TYPE A\r\n'
*get* '200 TYPE is now ASCII\n'
*resp* '200 TYPE is now ASCII'
*cmd* 'PASV'
*put* 'PASV\r\n'
*get* '227 Entering Passive Mode (211,149,249,234,154,227)\n'
*resp* '227 Entering Passive Mode (211,149,249,234,154,227)'
*cmd* 'NLST'
*put* 'NLST\r\n'
*get* '150 Accepted data connection\n'
*resp* '150 Accepted data connection'
*get* '226 36 matches total\n'
*resp* '226 36 matches total'
*cmd* 'TYPE I'
*put* 'TYPE I\r\n'
*get* '200 TYPE is now 8-bit binary\n'
*resp* '200 TYPE is now 8-bit binary'
*cmd* 'PASV'
*put* 'PASV\r\n'
*get* '227 Entering Passive Mode (211,149,249,234,153,188)\n'
*resp* '227 Entering Passive Mode (211,149,249,234,153,188)'
*cmd* 'STOR report_20250604.html'
*put* 'STOR report_20250604.html\r\n'
*get* '150 Accepted data connection\n'
*resp* '150 Accepted data connection'
*get* '226-File successfully transferred\n'
*get* '226 0.096 seconds (measured here), 0.50 Mbytes per second\n'
*resp* '226-File successfully transferred\n226 0.096 seconds (measured here), 0.50 Mbytes per second'
*cmd* 'TYPE A'
*put* 'TYPE A\r\n'
*get* '200 TYPE is now ASCII\n'
*resp* '200 TYPE is now ASCII'
*cmd* 'PASV'
*put* 'PASV\r\n'
*get* '227 Entering Passive Mode (211,149,249,234,155,106)\n'
*resp* '227 Entering Passive Mode (211,149,249,234,155,106)'
*cmd* 'NLST'
*put* 'NLST\r\n'
*get* '150 Accepted data connection\n'
*resp* '150 Accepted data connection'
*get* '226 37 matches total\n'
*resp* '226 37 matches total'
*cmd* 'QUIT'
*put* 'QUIT\r\n'
*get* '221-Goodbye. You uploaded 50 and downloaded 0 kbytes.\n'
*get* '221 Logout.\n'
*resp* '221-Goodbye. You uploaded 50 and downloaded 0 kbytes.\n221 Logout.'
[2025-06-05 08:00:02] 简单日报脚本执行成功
[2025-06-05 08:00:02] 开始执行全渠道日报脚本...
2025-06-05 08:00:05,291 - INFO - ==================================================
2025-06-05 08:00:05,292 - INFO - 开始生成并发送昨天的日报（全渠道生产环境）...
2025-06-05 08:00:05,292 - INFO - 开始执行昨天的日报发送流程（全渠道生产环境）...
2025-06-05 08:00:05,310 - INFO - 昨天日期: 2025-06-04
2025-06-05 08:00:05,311 - INFO - 开始获取昨天的订单数据（下单时间维度）...
2025-06-05 08:00:05,311 - INFO - 获取 2025-06-04 的订单数据（下单时间维度）...
2025-06-05 08:00:05,346 - INFO - 成功获取到 195 条去重后的订单数据（下单时间维度）
2025-06-05 08:00:05,347 - INFO - 开始获取昨天的订单数据（激活时间维度）...
2025-06-05 08:00:05,347 - INFO - 获取 2025-06-04 的订单数据（激活时间维度）...
2025-06-05 08:00:05,380 - INFO - 成功获取到 153 条去重后的订单数据（激活时间维度）
2025-06-05 08:00:05,380 - INFO - 成功获取到订单数据，继续生成日报
2025-06-05 08:00:05,380 - INFO - 开始生成昨天的日报...
2025-06-05 08:00:05,380 - INFO - 开始生成日报...
2025-06-05 08:00:05,380 - INFO - 开始分类并过滤订单数据...
2025-06-05 08:00:05,381 - INFO - 订单分类完成: 有效订单 195 单，其中骑手卡 193 单，大流量卡 2 单
2025-06-05 08:00:05,381 - INFO - 统计各类订单按渠道分类...
2025-06-05 08:00:05,381 - INFO - 开始分类并过滤订单数据...
2025-06-05 08:00:05,381 - INFO - 订单分类完成: 有效订单 153 单，其中骑手卡 153 单，大流量卡 0 单
2025-06-05 08:00:05,381 - INFO - 统计各类订单按渠道分类...
2025-06-05 08:00:05,381 - INFO - 日报生成完成
2025-06-05 08:00:05,382 - INFO - 开始分类并过滤订单数据...
2025-06-05 08:00:05,382 - INFO - 订单分类完成: 有效订单 195 单，其中骑手卡 193 单，大流量卡 2 单
2025-06-05 08:00:05,382 - INFO - 统计各类订单按渠道分类...
2025-06-05 08:00:05,382 - INFO - 开始分类并过滤订单数据...
2025-06-05 08:00:05,382 - INFO - 订单分类完成: 有效订单 153 单，其中骑手卡 153 单，大流量卡 0 单
2025-06-05 08:00:05,382 - INFO - 统计各类订单按渠道分类...
2025-06-05 08:00:05,384 - INFO - 开始生成 2025-06-04 的报表数据...
2025-06-05 08:00:05,384 - INFO - 获取 2025-06-04 的订单数据（下单时间维度）...
2025-06-05 08:00:05,420 - INFO - 成功获取到 195 条去重后的订单数据（下单时间维度）
2025-06-05 08:00:05,421 - INFO - 开始分类并过滤订单数据...
2025-06-05 08:00:05,421 - INFO - 订单分类完成: 有效订单 195 单，其中骑手卡 193 单，流量卡 2 单
2025-06-05 08:00:05,421 - INFO - 计算各渠道的D0下单和D1激活数据...
2025-06-05 08:00:05,455 - INFO - 成功获取到 153 条当日激活订单
2025-06-05 08:00:05,457 - INFO - 样本订单日期检查 (共5个):
2025-06-05 08:00:05,457 - INFO - 订单 20250603103137893072: 下单时间=2025-06-03, 激活时间=2025-06-04, 相差=1天
2025-06-05 08:00:05,457 - INFO - 订单 20250603095059097456: 下单时间=2025-06-03, 激活时间=2025-06-04, 相差=1天
2025-06-05 08:00:05,457 - INFO - 订单 20250604072245351349: 下单时间=2025-06-04, 激活时间=2025-06-04, 相差=0天
2025-06-05 08:00:05,458 - INFO - 订单 20250602203784645358: 下单时间=2025-06-02, 激活时间=2025-06-04, 相差=2天
2025-06-05 08:00:05,458 - INFO - 订单 20250604080821264865: 下单时间=2025-06-04, 激活时间=2025-06-04, 相差=0天
2025-06-05 08:00:05,458 - INFO - 熟龄度处理统计: 总订单=153, 成功处理=153, 处理失败=0
2025-06-05 08:00:05,458 - INFO - 熟龄度分布: 当天=17, 昨天=75, 前天=26, 更早=35
2025-06-05 08:00:05,458 - INFO - 骑手卡熟龄度分布: 当天=17, 昨天=75, 前天=26, 更早=35
2025-06-05 08:00:05,458 - INFO - 流量卡熟龄度分布: 当天=0, 昨天=0, 前天=0, 更早=0
2025-06-05 08:00:05,458 - INFO - 获取报表日期与前一日订单数据对比...
2025-06-05 08:00:05,458 - INFO - 对比日期: 报表日期=2025-06-04, 前一日=2025-06-03
2025-06-05 08:00:05,459 - INFO - 获取 2025-06-04 的订单数据（下单时间维度）...
2025-06-05 08:00:05,494 - INFO - 成功获取到 195 条去重后的订单数据（下单时间维度）
2025-06-05 08:00:05,494 - INFO - 获取 2025-06-03 的订单数据（下单时间维度）...
2025-06-05 08:00:05,531 - INFO - 成功获取到 191 条去重后的订单数据（下单时间维度）
2025-06-05 08:00:05,531 - INFO - 获取周订单趋势数据...
2025-06-05 08:00:05,531 - INFO - 查询日期范围: 2025-05-29 00:00:00 至 2025-06-04 23:59:59
2025-06-05 08:00:05,556 - INFO - 成功获取到 7 天的订单趋势数据
2025-06-05 08:00:05,556 - INFO - 熟龄度数据: {'same_day': 17, 'one_day': 75, 'two_days': 26, 'more_days': 35}
2025-06-05 08:00:05,556 - INFO - 骑手卡熟龄度数据: {'same_day': 17, 'one_day': 75, 'two_days': 26, 'more_days': 35}, 总数: 153
2025-06-05 08:00:05,556 - INFO - 流量卡熟龄度数据: {'same_day': 0, 'one_day': 0, 'two_days': 0, 'more_days': 0}, 总数: 0
2025-06-05 08:00:05,556 - INFO - 当日下单当日激活比例: 11.1%（17/153）
2025-06-05 08:00:05,557 - INFO - 样本订单日期检查 (共5个): 153，熟龄度分布: 当天=17, 昨天=75, 前天=26, 更早=35
2025-06-05 08:00:05,557 - INFO - 环比分析: 当前=195单, 前一日=191单, 变化=2.1%
2025-06-05 08:00:05,557 - INFO - 周趋势数据获取成功，共7天数据
2025-06-05 08:00:05,595 - INFO - 开始发送模板卡片消息到企业微信...
2025-06-05 08:00:05,596 - INFO - 开始发送消息到企业微信（全渠道生产环境）...
2025-06-05 08:00:05,908 - INFO - 企业微信API响应状态码: 200
2025-06-05 08:00:05,909 - INFO - 企业微信消息发送成功
2025-06-05 08:00:05,909 - INFO - 昨天的日报发送成功
2025-06-05 08:00:05,909 - INFO - 昨天的日报处理完成。
2025-06-05 08:00:05,909 - INFO - ==================================================
[2025-06-05 08:00:02] 全渠道日报脚本执行成功
[2025-06-05 08:00:02] 开始执行OPPO渠道日报脚本...
2025-06-05 08:00:06,097 - INFO - ==================================================
2025-06-05 08:00:06,098 - INFO - 开始生成并发送OPPO和荣耀渠道日报（OPPO生产环境）...
2025-06-05 08:00:06,098 - INFO - 开始执行OPPO和荣耀渠道日报发送流程（OPPO生产环境）...
2025-06-05 08:00:06,117 - INFO - 查询日期: 2025-06-04
2025-06-05 08:00:06,117 - INFO - 获取 2025-06-04 的OPPO和荣耀渠道订单数据（下单时间维度）...
2025-06-05 08:00:06,141 - INFO - 查询到 0 条去重后的订单数据
2025-06-05 08:00:06,141 - WARNING - 未获取到OPPO和荣耀渠道订单数据，取消发送日报
2025-06-05 08:00:06,141 - WARNING - 由于没有数据，日报未发送
2025-06-05 08:00:06,141 - INFO - ==================================================
[2025-06-05 08:00:02] OPPO渠道日报脚本执行成功
[2025-06-05 08:00:02] 开始执行驿舟渠道日报脚本...
2025-06-05 08:00:06,327 - INFO - ==================================================
2025-06-05 08:00:06,327 - INFO - 开始生成并发送驿舟相关渠道日报（驿舟日报环境）...
2025-06-05 08:00:06,327 - INFO - 开始执行驿舟相关渠道日报发送流程（驿舟日报环境）...
2025-06-05 08:00:06,346 - INFO - 获取 2025-06-04 的驿舟相关渠道订单数据（下单时间维度）...
2025-06-05 08:00:06,374 - INFO - 查询到 52 条去重后的订单数据
2025-06-05 08:00:06,375 - INFO - 获取 2025-06-04 的驿舟相关渠道订单数据（激活时间维度）...
2025-06-05 08:00:06,401 - INFO - 查询到 44 条去重后的订单数据
2025-06-05 08:00:06,453 - INFO - 开始发送消息到企业微信（驿舟日报环境）...
2025-06-05 08:00:06,669 - INFO - 企业微信API响应状态码: 200
2025-06-05 08:00:06,669 - INFO - 企业微信消息发送成功
2025-06-05 08:00:06,669 - INFO - 驿舟相关渠道日报发送成功
2025-06-05 08:00:06,669 - INFO - 驿舟相关渠道日报处理完成。
2025-06-05 08:00:06,669 - INFO - ==================================================
[2025-06-05 08:00:02] 驿舟渠道日报脚本执行成功
[2025-06-05 08:00:06] 所有日报脚本执行完成
----------------------------------------
