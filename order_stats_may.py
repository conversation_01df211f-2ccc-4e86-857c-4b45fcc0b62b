import pymysql
import pandas as pd
import datetime
import os
import logging
from typing import Dict, List, Any
import config  # 导入配置文件

# 配置日志
log_dir = "logs"
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

log_file = os.path.join(log_dir, f"order_stats_may_{datetime.datetime.now().strftime('%Y%m%d')}.log")
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file, encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 从配置文件获取当前环境的配置
MYSQL_CONFIG = {**config.get_current_db_config(), "cursorclass": pymysql.cursors.DictCursor}

def debug_database_info():
    """
    调试函数，获取数据库表信息和示例数据
    """
    logger.info("获取数据库调试信息...")
    
    try:
        conn = pymysql.connect(**MYSQL_CONFIG)
        with conn.cursor() as cursor:
            # 获取数据库表信息
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            logger.info(f"数据库中的表: {[list(table.values())[0] for table in tables]}")
            
            # 检查forders表是否存在
            if any('forders' in list(table.values())[0].lower() for table in tables):
                # 获取forders表的结构
                cursor.execute("DESCRIBE forders")
                columns = cursor.fetchall()
                logger.info(f"forders表的列: {[col['Field'] for col in columns]}")
                
                # 获取forders表中的示例数据
                cursor.execute("SELECT * FROM forders LIMIT 5")
                sample_data = cursor.fetchall()
                if sample_data:
                    logger.info(f"forders表示例数据（前5条）: {sample_data}")
                else:
                    logger.warning("forders表中没有数据")
                
                # 检查最近的订单记录
                cursor.execute("SELECT createTime, logisticsStatus FROM forders ORDER BY createTime DESC LIMIT 5")
                recent_orders = cursor.fetchall()
                if recent_orders:
                    logger.info(f"最近的订单记录: {recent_orders}")
                    
                # 检查5月份是否有订单记录
                cursor.execute("SELECT COUNT(*) as count FROM forders WHERE createTime BETWEEN '2025-05-01 00:00:00' AND '2025-05-31 23:59:59'")
                may_orders_count = cursor.fetchone()
                logger.info(f"2025年5月份订单记录数: {may_orders_count['count'] if may_orders_count else 0}")
            else:
                logger.warning("数据库中没有forders表")
            
            # 检查phone_card表
            if any('phone_card' in list(table.values())[0].lower() for table in tables):
                cursor.execute("SELECT DISTINCT card_typ FROM phone_card")
                card_types = cursor.fetchall()
                logger.info(f"phone_card表中的卡类型: {[card['card_typ'] for card in card_types]}")
            else:
                logger.warning("数据库中没有phone_card表")
                
        conn.close()
    except Exception as e:
        logger.exception(f"获取数据库调试信息时出错: {e}")

def get_may_order_data():
    """
    获取5月份的订单数据，包括有效订单和无效订单
    有效订单：logisticsStatus为"发货中"或"发货成功"
    无效订单：logisticsStatus为"发货失败"
    只统计骑手卡的订单数据
    """
    logger.info("获取2025年5月份的订单数据...")
    
    start_time = "2025-05-01 00:00:00"
    end_time = "2025-05-31 23:59:59"
    
    query = """
    SELECT 
        DATE(f.createTime) as order_date,
        f.orderNo,
        f.target,
        cc.description AS channel_description,
        pc.card_typ,
        pc.productName_alias,
        f.businessStatus,
        f.outStatus,
        f.send_goods_result_Alias,
        f.logisticsStatus,
        f.createTime,
        f.updateTime
    FROM 
        forders f
    LEFT JOIN 
        channel_config cc ON f.channel_config = cc.id
    LEFT JOIN 
        phone_card pc ON f.Phone_Card = pc.skuCode
    WHERE
        f.createTime BETWEEN %s AND %s
        AND pc.card_typ = '骑手卡'
        AND f.logisticsStatus IN ('发货中', '发货成功', '发货失败')
    ORDER BY
        f.createTime
    """
    
    try:
        conn = pymysql.connect(**MYSQL_CONFIG)
        with conn.cursor() as cursor:
            cursor.execute(query, (start_time, end_time))
            data = cursor.fetchall()
        conn.close()
        
        logger.info(f"成功获取到 {len(data)} 条订单数据")
        return data
    except Exception as e:
        logger.exception(f"获取订单数据时出错: {e}")
        return []

def process_order_data(data):
    """
    处理订单数据，按日期统计有效和无效订单数
    """
    logger.info("开始处理订单数据...")
    
    # 按日期进行统计
    date_stats = {}
    
    for order in data:
        order_date = order["order_date"]
        logistics_status = order.get("logisticsStatus", "Unknown")
        
        if order_date not in date_stats:
            date_stats[order_date] = {
                "valid_orders": 0,   # 有效订单数（发货中、发货成功）
                "invalid_orders": 0, # 无效订单数（发货失败）
                "total_orders": 0,   # 总订单数
                "channels": {},      # 按渠道统计
                "products": {}       # 按产品统计
            }
        
        # 统计订单状态
        if logistics_status in ["发货中", "发货成功"]:
            date_stats[order_date]["valid_orders"] += 1
        elif logistics_status == "发货失败":
            date_stats[order_date]["invalid_orders"] += 1
        
        date_stats[order_date]["total_orders"] += 1
        
        # 按渠道统计
        channel = order.get("channel_description", "未知渠道")
        if channel not in date_stats[order_date]["channels"]:
            date_stats[order_date]["channels"][channel] = 0
        date_stats[order_date]["channels"][channel] += 1
        
        # 按产品统计
        product = order.get("productName_alias", "未知产品")
        if product not in date_stats[order_date]["products"]:
            date_stats[order_date]["products"][product] = 0
        date_stats[order_date]["products"][product] += 1
    
    logger.info(f"订单数据处理完成，共有 {len(date_stats)} 天有订单记录")
    return date_stats

def generate_excel_report(date_stats):
    """
    生成Excel报表
    """
    logger.info("开始生成Excel报表...")
    
    # 创建一个空的DataFrame
    df_daily = []
    df_channel = []
    df_product = []
    
    # 按日期统计数据
    for date, stats in sorted(date_stats.items()):
        # 每日订单统计
        df_daily.append({
            "日期": date,
            "有效订单数": stats["valid_orders"],
            "无效订单数": stats["invalid_orders"],
            "总订单数": stats["total_orders"],
            "有效订单占比": f"{stats['valid_orders']/stats['total_orders']*100:.2f}%" if stats["total_orders"] > 0 else "0.00%"
        })
        
        # 按渠道统计
        for channel, count in stats["channels"].items():
            df_channel.append({
                "日期": date,
                "渠道": channel,
                "订单数": count
            })
        
        # 按产品统计
        for product, count in stats["products"].items():
            df_product.append({
                "日期": date,
                "产品": product,
                "订单数": count
            })
    
    # 创建Excel文件
    report_dir = "reports"
    if not os.path.exists(report_dir):
        os.makedirs(report_dir)
    
    excel_file = os.path.join(report_dir, f"骑手卡订单统计_2025年5月_{datetime.datetime.now().strftime('%Y%m%d')}.xlsx")
    
    # 创建Excel写入器
    with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
        # 写入每日统计表
        pd.DataFrame(df_daily).to_excel(writer, sheet_name="每日订单统计", index=False)
        
        # 写入渠道统计表
        pd.DataFrame(df_channel).to_excel(writer, sheet_name="渠道订单统计", index=False)
        
        # 写入产品统计表
        pd.DataFrame(df_product).to_excel(writer, sheet_name="产品订单统计", index=False)
    
    logger.info(f"Excel报表生成完成: {excel_file}")
    return excel_file

def main():
    """
    主函数
    """
    logger.info("=" * 50)
    logger.info("开始统计2025年5月份骑手卡订单数据...")
    
    # 首先运行调试信息收集
    debug_database_info()
    
    # 获取订单数据
    order_data = get_may_order_data()
    
    if not order_data:
        logger.warning("未获取到订单数据，程序退出")
        return
    
    # 处理订单数据
    date_stats = process_order_data(order_data)
    
    # 生成Excel报表
    excel_file = generate_excel_report(date_stats)
    
    logger.info(f"2025年5月份骑手卡订单统计完成，Excel报表已生成: {excel_file}")
    logger.info("=" * 50)
    
    print(f"\n统计完成! Excel报表已生成: {excel_file}\n")

if __name__ == "__main__":
    main() 