[2025-05-11 08:00:01] 开始执行日报脚本
[2025-05-11 08:00:01] 工作目录: /volume3/SSD-soft/fzsrb
[2025-05-11 08:00:01] 检查Python依赖...
WARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager, possibly rendering your system unusable. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv. Use the --root-user-action option if you know what you are doing and want to suppress this warning.
[2025-05-11 08:00:01] 开始执行简单日报脚本...
2025-05-11 08:00:03,212 - INFO - ==================================================
2025-05-11 08:00:03,212 - INFO - 开始生成日报统计数据...
2025-05-11 08:00:03,244 - INFO - 开始生成 2025-05-10 的报表数据...
2025-05-11 08:00:03,244 - INFO - 获取 2025-05-10 的订单数据（下单时间维度）...
2025-05-11 08:00:03,279 - INFO - 成功获取到 214 条去重后的订单数据（下单时间维度）
2025-05-11 08:00:03,279 - INFO - 开始分类并过滤订单数据...
2025-05-11 08:00:03,279 - INFO - 订单分类完成: 有效订单 214 单，其中骑手卡 182 单，流量卡 32 单
2025-05-11 08:00:03,279 - INFO - 计算各渠道的D0下单和D1激活数据...
2025-05-11 08:00:03,303 - INFO - 成功获取到 163 条当日激活订单
2025-05-11 08:00:03,307 - INFO - 样本订单日期检查 (共5个):
2025-05-11 08:00:03,307 - INFO - 订单 20250509151174322140: 下单时间=2025-05-09, 激活时间=2025-05-10, 相差=1天
2025-05-11 08:00:03,307 - INFO - 订单 20250508171922395679: 下单时间=2025-05-08, 激活时间=2025-05-10, 相差=2天
2025-05-11 08:00:03,307 - INFO - 订单 20250506154670225778: 下单时间=2025-05-06, 激活时间=2025-05-10, 相差=4天
2025-05-11 08:00:03,307 - INFO - 订单 20250509162792053203: 下单时间=2025-05-09, 激活时间=2025-05-10, 相差=1天
2025-05-11 08:00:03,307 - INFO - 订单 20250509152754825469: 下单时间=2025-05-09, 激活时间=2025-05-10, 相差=1天
2025-05-11 08:00:03,307 - INFO - 熟龄度处理统计: 总订单=163, 成功处理=163, 处理失败=0
2025-05-11 08:00:03,307 - INFO - 熟龄度分布: 当天=7, 昨天=49, 前天=60, 更早=47
2025-05-11 08:00:03,307 - INFO - 骑手卡熟龄度分布: 当天=7, 昨天=48, 前天=55, 更早=40
2025-05-11 08:00:03,307 - INFO - 流量卡熟龄度分布: 当天=0, 昨天=1, 前天=5, 更早=7
2025-05-11 08:00:03,308 - INFO - 熟龄度数据: {'same_day': 7, 'one_day': 49, 'two_days': 60, 'more_days': 47}
2025-05-11 08:00:03,308 - INFO - 骑手卡熟龄度数据: {'same_day': 7, 'one_day': 48, 'two_days': 55, 'more_days': 40}, 总数: 150
2025-05-11 08:00:03,308 - INFO - 流量卡熟龄度数据: {'same_day': 0, 'one_day': 1, 'two_days': 5, 'more_days': 7}, 总数: 13
2025-05-11 08:00:03,308 - INFO - 当日下单当日激活比例: 4.3%（7/163）
2025-05-11 08:00:03,308 - INFO - 样本订单日期检查 (共5个): 163，熟龄度分布: 当天=7, 昨天=49, 前天=60, 更早=47
2025-05-11 08:00:03,308 - INFO - 生成HTML报表模板，加载数据: 总活跃=163, 骑手卡=150, 流量卡=13
2025-05-11 08:00:03,308 - INFO - 骑手卡数据: [7, 48, 55, 40]
2025-05-11 08:00:03,309 - INFO - 流量卡数据: [0, 1, 5, 7]
2025-05-11 08:00:03,309 - INFO - HTML报表已生成: reports/report_20250510.html
2025-05-11 08:00:03,309 - INFO - 准备上传文件到FTP服务器: report_20250510.html
2025-05-11 08:00:03,309 - INFO - FTP配置: 主机=211.149.249.234, 端口=21, 目录=reports
2025-05-11 08:00:03,309 - INFO - 本地文件: reports/report_20250510.html, 大小: 33552 字节
2025-05-11 08:00:03,309 - INFO - 正在连接到FTP服务器: 211.149.249.234:21
2025-05-11 08:00:03,374 - INFO - 正在登录FTP服务器: 用户名=reports
2025-05-11 08:00:03,457 - INFO - FTP登录成功
2025-05-11 08:00:03,487 - INFO - 当前FTP目录: /
2025-05-11 08:00:03,488 - INFO - 尝试切换到目录: reports
2025-05-11 08:00:03,552 - INFO - 成功切换到目录: /reports
2025-05-11 08:00:03,680 - INFO - 上传前目录内容: ['report_20250506.html', 'report_20250503.html', 'report_20250508.html', 'report_20250502.html', '..', 'report_20250509.html', 'report_20250501.html', 'report_20250505.html', 'report_20250504.html', 'report_20250507.html', '.']
2025-05-11 08:00:03,680 - INFO - 开始上传文件: reports/report_20250510.html -> report_20250510.html
2025-05-11 08:00:03,877 - INFO - FTP上传结果: 226-File successfully transferred
226 0.068 seconds (measured here), 478.48 Kbytes per second
2025-05-11 08:00:04,013 - INFO - 上传后目录内容: ['report_20250506.html', 'report_20250503.html', 'report_20250508.html', 'report_20250502.html', '..', 'report_20250509.html', 'report_20250510.html', 'report_20250501.html', 'report_20250505.html', 'report_20250504.html', 'report_20250507.html', '.']
2025-05-11 08:00:04,014 - INFO - 文件已成功上传并验证: report_20250510.html
2025-05-11 08:00:04,014 - INFO - 文件访问URL: https://rep.331126.com/reports/report_20250510.html
2025-05-11 08:00:04,044 - INFO - FTP连接已关闭
2025-05-11 08:00:04,045 - INFO - 报表已上传到服务器，URL: https://rep.331126.com/reports/report_20250510.html
2025-05-11 08:00:04,045 - INFO - 日报生成完成
2025-05-11 08:00:04,045 - INFO - ==================================================
*get* '220---------- Welcome to Pure-FTPd [privsep] [TLS] ----------\n'
*get* '220-You are user number 1 of 50 allowed.\n'
*get* '220-Local time is now 08:00. Server port: 21.\n'
*get* '220-This is a private system - No anonymous login\n'
*get* '220-IPv6 connections are also welcome on this server.\n'
*get* '220 You will be disconnected after 15 minutes of inactivity.\n'
*resp* '220---------- Welcome to Pure-FTPd [privsep] [TLS] ----------\n220-You are user number 1 of 50 allowed.\n220-Local time is now 08:00. Server port: 21.\n220-This is a private system - No anonymous login\n220-IPv6 connections are also welcome on this server.\n220 You will be disconnected after 15 minutes of inactivity.'
*cmd* 'USER reports'
*put* 'USER reports\r\n'
*get* '331 User reports OK. Password required\n'
*resp* '331 User reports OK. Password required'
*cmd* 'PASS ****************'
*put* 'PASS ****************\r\n'
*get* '230 OK. Current directory is /\n'
*resp* '230 OK. Current directory is /'
*cmd* 'PWD'
*put* 'PWD\r\n'
*get* '257 "/" is your current location\n'
*resp* '257 "/" is your current location'
*cmd* 'CWD reports'
*put* 'CWD reports\r\n'
*get* '250 OK. Current directory is /reports\n'
*resp* '250 OK. Current directory is /reports'
*cmd* 'PWD'
*put* 'PWD\r\n'
*get* '257 "/reports" is your current location\n'
*resp* '257 "/reports" is your current location'
*cmd* 'TYPE A'
*put* 'TYPE A\r\n'
*get* '200 TYPE is now ASCII\n'
*resp* '200 TYPE is now ASCII'
*cmd* 'PASV'
*put* 'PASV\r\n'
*get* '227 Entering Passive Mode (211,149,249,234,155,75)\n'
*resp* '227 Entering Passive Mode (211,149,249,234,155,75)'
*cmd* 'NLST'
*put* 'NLST\r\n'
*get* '150 Accepted data connection\n'
*resp* '150 Accepted data connection'
*get* '226 11 matches total\n'
*resp* '226 11 matches total'
*cmd* 'TYPE I'
*put* 'TYPE I\r\n'
*get* '200 TYPE is now 8-bit binary\n'
*resp* '200 TYPE is now 8-bit binary'
*cmd* 'PASV'
*put* 'PASV\r\n'
*get* '227 Entering Passive Mode (211,149,249,234,154,86)\n'
*resp* '227 Entering Passive Mode (211,149,249,234,154,86)'
*cmd* 'STOR report_20250510.html'
*put* 'STOR report_20250510.html\r\n'
*get* '150 Accepted data connection\n'
*resp* '150 Accepted data connection'
*get* '226-File successfully transferred\n'
*get* '226 0.068 seconds (measured here), 478.48 Kbytes per second\n'
*resp* '226-File successfully transferred\n226 0.068 seconds (measured here), 478.48 Kbytes per second'
*cmd* 'TYPE A'
*put* 'TYPE A\r\n'
*get* '200 TYPE is now ASCII\n'
*resp* '200 TYPE is now ASCII'
*cmd* 'PASV'
*put* 'PASV\r\n'
*get* '227 Entering Passive Mode (211,149,249,234,155,189)\n'
*resp* '227 Entering Passive Mode (211,149,249,234,155,189)'
*cmd* 'NLST'
*put* 'NLST\r\n'
*get* '150 Accepted data connection\n'
*resp* '150 Accepted data connection'
*get* '226 12 matches total\n'
*resp* '226 12 matches total'
*cmd* 'QUIT'
*put* 'QUIT\r\n'
*get* '221-Goodbye. You uploaded 33 and downloaded 0 kbytes.\n'
*get* '221 Logout.\n'
*resp* '221-Goodbye. You uploaded 33 and downloaded 0 kbytes.\n221 Logout.'
[2025-05-11 08:00:01] 简单日报脚本执行成功
[2025-05-11 08:00:01] 开始执行全渠道日报脚本...
2025-05-11 08:00:04,206 - INFO - ==================================================
2025-05-11 08:00:04,206 - INFO - 开始生成并发送昨天的日报（全渠道生产环境）...
2025-05-11 08:00:04,206 - INFO - 开始执行昨天的日报发送流程（全渠道生产环境）...
2025-05-11 08:00:04,224 - INFO - 昨天日期: 2025-05-10
2025-05-11 08:00:04,224 - INFO - 开始获取昨天的订单数据（下单时间维度）...
2025-05-11 08:00:04,224 - INFO - 获取 2025-05-10 的订单数据（下单时间维度）...
2025-05-11 08:00:04,252 - INFO - 成功获取到 214 条去重后的订单数据（下单时间维度）
2025-05-11 08:00:04,252 - INFO - 开始获取昨天的订单数据（激活时间维度）...
2025-05-11 08:00:04,252 - INFO - 获取 2025-05-10 的订单数据（激活时间维度）...
2025-05-11 08:00:04,276 - INFO - 成功获取到 163 条去重后的订单数据（激活时间维度）
2025-05-11 08:00:04,276 - INFO - 成功获取到订单数据，继续生成日报
2025-05-11 08:00:04,276 - INFO - 开始生成昨天的日报...
2025-05-11 08:00:04,276 - INFO - 开始生成日报...
2025-05-11 08:00:04,276 - INFO - 开始分类并过滤订单数据...
2025-05-11 08:00:04,276 - INFO - 订单分类完成: 有效订单 214 单，其中骑手卡 182 单，大流量卡 32 单
2025-05-11 08:00:04,276 - INFO - 统计各类订单按渠道分类...
2025-05-11 08:00:04,277 - INFO - 开始分类并过滤订单数据...
2025-05-11 08:00:04,277 - INFO - 订单分类完成: 有效订单 163 单，其中骑手卡 150 单，大流量卡 13 单
2025-05-11 08:00:04,277 - INFO - 统计各类订单按渠道分类...
2025-05-11 08:00:04,277 - INFO - 日报生成完成
2025-05-11 08:00:04,277 - INFO - 开始分类并过滤订单数据...
2025-05-11 08:00:04,277 - INFO - 订单分类完成: 有效订单 214 单，其中骑手卡 182 单，大流量卡 32 单
2025-05-11 08:00:04,277 - INFO - 统计各类订单按渠道分类...
2025-05-11 08:00:04,278 - INFO - 开始分类并过滤订单数据...
2025-05-11 08:00:04,278 - INFO - 订单分类完成: 有效订单 163 单，其中骑手卡 150 单，大流量卡 13 单
2025-05-11 08:00:04,278 - INFO - 统计各类订单按渠道分类...
2025-05-11 08:00:04,280 - INFO - 开始生成 2025-05-10 的报表数据...
2025-05-11 08:00:04,280 - INFO - 获取 2025-05-10 的订单数据（下单时间维度）...
2025-05-11 08:00:04,307 - INFO - 成功获取到 214 条去重后的订单数据（下单时间维度）
2025-05-11 08:00:04,307 - INFO - 开始分类并过滤订单数据...
2025-05-11 08:00:04,307 - INFO - 订单分类完成: 有效订单 214 单，其中骑手卡 182 单，流量卡 32 单
2025-05-11 08:00:04,308 - INFO - 计算各渠道的D0下单和D1激活数据...
2025-05-11 08:00:04,332 - INFO - 成功获取到 163 条当日激活订单
2025-05-11 08:00:04,333 - INFO - 样本订单日期检查 (共5个):
2025-05-11 08:00:04,334 - INFO - 订单 20250509151174322140: 下单时间=2025-05-09, 激活时间=2025-05-10, 相差=1天
2025-05-11 08:00:04,334 - INFO - 订单 20250508171922395679: 下单时间=2025-05-08, 激活时间=2025-05-10, 相差=2天
2025-05-11 08:00:04,334 - INFO - 订单 20250506154670225778: 下单时间=2025-05-06, 激活时间=2025-05-10, 相差=4天
2025-05-11 08:00:04,334 - INFO - 订单 20250509162792053203: 下单时间=2025-05-09, 激活时间=2025-05-10, 相差=1天
2025-05-11 08:00:04,334 - INFO - 订单 20250509152754825469: 下单时间=2025-05-09, 激活时间=2025-05-10, 相差=1天
2025-05-11 08:00:04,334 - INFO - 熟龄度处理统计: 总订单=163, 成功处理=163, 处理失败=0
2025-05-11 08:00:04,334 - INFO - 熟龄度分布: 当天=7, 昨天=49, 前天=60, 更早=47
2025-05-11 08:00:04,334 - INFO - 骑手卡熟龄度分布: 当天=7, 昨天=48, 前天=55, 更早=40
2025-05-11 08:00:04,334 - INFO - 流量卡熟龄度分布: 当天=0, 昨天=1, 前天=5, 更早=7
2025-05-11 08:00:04,335 - INFO - 熟龄度数据: {'same_day': 7, 'one_day': 49, 'two_days': 60, 'more_days': 47}
2025-05-11 08:00:04,335 - INFO - 骑手卡熟龄度数据: {'same_day': 7, 'one_day': 48, 'two_days': 55, 'more_days': 40}, 总数: 150
2025-05-11 08:00:04,335 - INFO - 流量卡熟龄度数据: {'same_day': 0, 'one_day': 1, 'two_days': 5, 'more_days': 7}, 总数: 13
2025-05-11 08:00:04,335 - INFO - 当日下单当日激活比例: 4.3%（7/163）
2025-05-11 08:00:04,335 - INFO - 样本订单日期检查 (共5个): 163，熟龄度分布: 当天=7, 昨天=49, 前天=60, 更早=47
2025-05-11 08:00:04,335 - INFO - 开始发送模板卡片消息到企业微信...
2025-05-11 08:00:04,335 - INFO - 开始发送消息到企业微信（全渠道生产环境）...
2025-05-11 08:00:04,568 - INFO - 企业微信API响应状态码: 200
2025-05-11 08:00:04,568 - INFO - 企业微信消息发送成功
2025-05-11 08:00:04,568 - INFO - 昨天的日报发送成功
2025-05-11 08:00:04,569 - INFO - 昨天的日报处理完成。
2025-05-11 08:00:04,569 - INFO - ==================================================
[2025-05-11 08:00:01] 全渠道日报脚本执行成功
[2025-05-11 08:00:01] 开始执行OPPO渠道日报脚本...
2025-05-11 08:00:04,738 - INFO - ==================================================
2025-05-11 08:00:04,738 - INFO - 开始生成并发送OPPO和荣耀渠道日报（OPPO生产环境）...
2025-05-11 08:00:04,738 - INFO - 开始执行OPPO和荣耀渠道日报发送流程（OPPO生产环境）...
2025-05-11 08:00:04,755 - INFO - 查询日期: 2025-05-10
2025-05-11 08:00:04,755 - INFO - 获取 2025-05-10 的OPPO和荣耀渠道订单数据（下单时间维度）...
2025-05-11 08:00:04,773 - INFO - 查询到 32 条去重后的订单数据
2025-05-11 08:00:04,773 - INFO - 开始生成OPPO和荣耀渠道Markdown格式日报...
2025-05-11 08:00:04,774 - INFO - 开始处理订单数据，总数据条数: 32
2025-05-11 08:00:04,774 - INFO - 统计结果: 总订单 32
2025-05-11 08:00:04,774 - INFO -   OPPO: 26单
2025-05-11 08:00:04,774 - INFO -   荣耀: 6单
2025-05-11 08:00:04,774 - INFO - Markdown格式日报生成完成
2025-05-11 08:00:04,774 - INFO - 开始发送消息到企业微信（OPPO生产环境）...
2025-05-11 08:00:05,011 - INFO - 企业微信API响应状态码: 200
2025-05-11 08:00:05,011 - INFO - 企业微信消息发送成功
2025-05-11 08:00:05,011 - INFO - OPPO和荣耀渠道日报发送成功
2025-05-11 08:00:05,012 - INFO - OPPO和荣耀渠道日报处理完成。
2025-05-11 08:00:05,012 - INFO - ==================================================
[2025-05-11 08:00:01] OPPO渠道日报脚本执行成功
[2025-05-11 08:00:05] 所有日报脚本执行完成
----------------------------------------
