2025-05-05 07:39:09,299 - INFO - ==================================================
2025-05-05 07:39:09,300 - INFO - 开始生成日报统计数据...
2025-05-05 07:39:09,331 - INFO - 开始生成 2025-05-04 的报表数据...
2025-05-05 07:39:09,331 - INFO - 获取 2025-05-04 的订单数据（下单时间维度）...
2025-05-05 07:39:09,354 - INFO - 成功获取到 150 条去重后的订单数据（下单时间维度）
2025-05-05 07:39:09,354 - INFO - 开始分类并过滤订单数据...
2025-05-05 07:39:09,354 - INFO - 订单分类完成: 有效订单 150 单，其中骑手卡 144 单，流量卡 6 单
2025-05-05 07:39:09,354 - INFO - 计算各渠道的D0下单和D1激活数据...
2025-05-05 07:39:09,376 - INFO - 成功获取到 156 条当日激活订单
2025-05-05 07:39:09,380 - INFO - 样本订单日期检查 (共5个):
2025-05-05 07:39:09,380 - INFO - 订单 20250502100598235160: 下单时间=2025-05-02, 激活时间=2025-05-04, 相差=2天
2025-05-05 07:39:09,380 - INFO - 订单 20250502230214867899: 下单时间=2025-05-02, 激活时间=2025-05-04, 相差=2天
2025-05-05 07:39:09,380 - INFO - 订单 20250503154996620859: 下单时间=2025-05-03, 激活时间=2025-05-04, 相差=1天
2025-05-05 07:39:09,380 - INFO - 订单 20250502151316481246: 下单时间=2025-05-02, 激活时间=2025-05-04, 相差=2天
2025-05-05 07:39:09,380 - INFO - 订单 20250502114001188512: 下单时间=2025-05-02, 激活时间=2025-05-04, 相差=2天
2025-05-05 07:39:09,380 - INFO - 熟龄度处理统计: 总订单=156, 成功处理=156, 处理失败=0
2025-05-05 07:39:09,380 - INFO - 熟龄度分布: 当天=3, 昨天=56, 前天=45, 更早=52
2025-05-05 07:39:09,380 - INFO - 骑手卡熟龄度分布: 当天=3, 昨天=55, 前天=35, 更早=37
2025-05-05 07:39:09,381 - INFO - 流量卡熟龄度分布: 当天=0, 昨天=1, 前天=10, 更早=15
2025-05-05 07:39:09,381 - INFO - 熟龄度数据: {'same_day': 3, 'one_day': 56, 'two_days': 45, 'more_days': 52}
2025-05-05 07:39:09,381 - INFO - 骑手卡熟龄度数据: {'same_day': 3, 'one_day': 55, 'two_days': 35, 'more_days': 37}, 总数: 130
2025-05-05 07:39:09,381 - INFO - 流量卡熟龄度数据: {'same_day': 0, 'one_day': 1, 'two_days': 10, 'more_days': 15}, 总数: 26
2025-05-05 07:39:09,381 - INFO - 当日下单当日激活比例: 1.9%（3/156）
2025-05-05 07:39:09,381 - INFO - 样本订单日期检查 (共5个): 156，熟龄度分布: 当天=3, 昨天=56, 前天=45, 更早=52
2025-05-05 07:39:09,382 - INFO - 生成HTML报表模板，加载数据: 总活跃=156, 骑手卡=130, 流量卡=26
2025-05-05 07:39:09,382 - INFO - 骑手卡数据: [3, 55, 35, 37]
2025-05-05 07:39:09,382 - INFO - 流量卡数据: [0, 1, 10, 15]
2025-05-05 07:39:09,382 - INFO - HTML报表已生成: reports/report_20250504.html
2025-05-05 07:39:09,382 - INFO - 准备上传文件到FTP服务器: report_20250504.html
2025-05-05 07:39:09,382 - INFO - FTP配置: 主机=211.149.249.234, 端口=21, 目录=reports
2025-05-05 07:39:09,382 - INFO - 本地文件: reports/report_20250504.html, 大小: 33951 字节
2025-05-05 07:39:09,382 - INFO - 正在连接到FTP服务器: 211.149.249.234:21
2025-05-05 07:39:09,450 - INFO - 正在登录FTP服务器: 用户名=reports
2025-05-05 07:39:09,539 - INFO - FTP登录成功
2025-05-05 07:39:09,570 - INFO - 当前FTP目录: /
2025-05-05 07:39:09,571 - INFO - 尝试切换到目录: reports
2025-05-05 07:39:09,633 - INFO - 成功切换到目录: /reports
2025-05-05 07:39:09,762 - INFO - 上传前目录内容: ['report_20250503.html', 'report_20250502.html', '..', 'report_20250501.html', '.']
2025-05-05 07:39:09,762 - INFO - 开始上传文件: reports/report_20250504.html -> report_20250504.html
2025-05-05 07:39:09,950 - INFO - FTP上传结果: 226-File successfully transferred
226 0.063 seconds (measured here), 0.51 Mbytes per second
2025-05-05 07:39:10,078 - INFO - 上传后目录内容: ['report_20250503.html', 'report_20250502.html', '..', 'report_20250501.html', 'report_20250504.html', '.']
2025-05-05 07:39:10,078 - INFO - 文件已成功上传并验证: report_20250504.html
2025-05-05 07:39:10,078 - INFO - 文件访问URL: https://b.zhoumeiren.cn/reports/report_20250504.html
2025-05-05 07:39:10,110 - INFO - FTP连接已关闭
2025-05-05 07:39:10,110 - INFO - 报表已上传到服务器，URL: https://b.zhoumeiren.cn/reports/report_20250504.html
2025-05-05 07:39:10,110 - INFO - 日报生成完成
2025-05-05 07:39:10,110 - INFO - ==================================================
2025-05-05 07:39:10,317 - INFO - ==================================================
2025-05-05 07:39:10,317 - INFO - 开始生成并发送昨天的日报（测试环境）...
2025-05-05 07:39:10,317 - INFO - 开始执行昨天的日报发送流程（测试环境）...
2025-05-05 07:39:10,334 - INFO - 昨天日期: 2025-05-04
2025-05-05 07:39:10,334 - INFO - 开始获取昨天的订单数据（下单时间维度）...
2025-05-05 07:39:10,334 - INFO - 获取 2025-05-04 的订单数据（下单时间维度）...
2025-05-05 07:39:10,356 - INFO - 成功获取到 150 条去重后的订单数据（下单时间维度）
2025-05-05 07:39:10,357 - INFO - 开始获取昨天的订单数据（激活时间维度）...
2025-05-05 07:39:10,357 - INFO - 获取 2025-05-04 的订单数据（激活时间维度）...
2025-05-05 07:39:10,378 - INFO - 成功获取到 156 条去重后的订单数据（激活时间维度）
2025-05-05 07:39:10,379 - INFO - 成功获取到订单数据，继续生成日报
2025-05-05 07:39:10,379 - INFO - 开始生成昨天的日报...
2025-05-05 07:39:10,379 - INFO - 开始生成日报...
2025-05-05 07:39:10,379 - INFO - 开始分类并过滤订单数据...
2025-05-05 07:39:10,379 - INFO - 订单分类完成: 有效订单 150 单，其中骑手卡 144 单，大流量卡 6 单
2025-05-05 07:39:10,379 - INFO - 统计各类订单按渠道分类...
2025-05-05 07:39:10,379 - INFO - 开始分类并过滤订单数据...
2025-05-05 07:39:10,379 - INFO - 订单分类完成: 有效订单 156 单，其中骑手卡 130 单，大流量卡 26 单
2025-05-05 07:39:10,379 - INFO - 统计各类订单按渠道分类...
2025-05-05 07:39:10,380 - INFO - 日报生成完成
2025-05-05 07:39:10,380 - INFO - 开始分类并过滤订单数据...
2025-05-05 07:39:10,380 - INFO - 订单分类完成: 有效订单 150 单，其中骑手卡 144 单，大流量卡 6 单
2025-05-05 07:39:10,380 - INFO - 统计各类订单按渠道分类...
2025-05-05 07:39:10,380 - INFO - 开始分类并过滤订单数据...
2025-05-05 07:39:10,380 - INFO - 订单分类完成: 有效订单 156 单，其中骑手卡 130 单，大流量卡 26 单
2025-05-05 07:39:10,380 - INFO - 统计各类订单按渠道分类...
2025-05-05 07:39:10,382 - INFO - 开始生成 2025-05-04 的报表数据...
2025-05-05 07:39:10,382 - INFO - 获取 2025-05-04 的订单数据（下单时间维度）...
2025-05-05 07:39:10,404 - INFO - 成功获取到 150 条去重后的订单数据（下单时间维度）
2025-05-05 07:39:10,404 - INFO - 开始分类并过滤订单数据...
2025-05-05 07:39:10,405 - INFO - 订单分类完成: 有效订单 150 单，其中骑手卡 144 单，流量卡 6 单
2025-05-05 07:39:10,405 - INFO - 计算各渠道的D0下单和D1激活数据...
2025-05-05 07:39:10,425 - INFO - 成功获取到 156 条当日激活订单
2025-05-05 07:39:10,427 - INFO - 样本订单日期检查 (共5个):
2025-05-05 07:39:10,427 - INFO - 订单 20250502100598235160: 下单时间=2025-05-02, 激活时间=2025-05-04, 相差=2天
2025-05-05 07:39:10,427 - INFO - 订单 20250502230214867899: 下单时间=2025-05-02, 激活时间=2025-05-04, 相差=2天
2025-05-05 07:39:10,427 - INFO - 订单 20250503154996620859: 下单时间=2025-05-03, 激活时间=2025-05-04, 相差=1天
2025-05-05 07:39:10,427 - INFO - 订单 20250502151316481246: 下单时间=2025-05-02, 激活时间=2025-05-04, 相差=2天
2025-05-05 07:39:10,427 - INFO - 订单 20250502114001188512: 下单时间=2025-05-02, 激活时间=2025-05-04, 相差=2天
2025-05-05 07:39:10,427 - INFO - 熟龄度处理统计: 总订单=156, 成功处理=156, 处理失败=0
2025-05-05 07:39:10,427 - INFO - 熟龄度分布: 当天=3, 昨天=56, 前天=45, 更早=52
2025-05-05 07:39:10,427 - INFO - 骑手卡熟龄度分布: 当天=3, 昨天=55, 前天=35, 更早=37
2025-05-05 07:39:10,427 - INFO - 流量卡熟龄度分布: 当天=0, 昨天=1, 前天=10, 更早=15
2025-05-05 07:39:10,428 - INFO - 熟龄度数据: {'same_day': 3, 'one_day': 56, 'two_days': 45, 'more_days': 52}
2025-05-05 07:39:10,428 - INFO - 骑手卡熟龄度数据: {'same_day': 3, 'one_day': 55, 'two_days': 35, 'more_days': 37}, 总数: 130
2025-05-05 07:39:10,428 - INFO - 流量卡熟龄度数据: {'same_day': 0, 'one_day': 1, 'two_days': 10, 'more_days': 15}, 总数: 26
2025-05-05 07:39:10,428 - INFO - 当日下单当日激活比例: 1.9%（3/156）
2025-05-05 07:39:10,428 - INFO - 样本订单日期检查 (共5个): 156，熟龄度分布: 当天=3, 昨天=56, 前天=45, 更早=52
2025-05-05 07:39:10,428 - INFO - 开始发送模板卡片消息到企业微信...
2025-05-05 07:39:10,428 - INFO - 开始发送消息到企业微信（测试环境）...
2025-05-05 07:39:10,728 - INFO - 企业微信API响应状态码: 200
2025-05-05 07:39:10,729 - INFO - 企业微信消息发送成功
2025-05-05 07:39:10,729 - INFO - 昨天的日报发送成功
2025-05-05 07:39:10,729 - INFO - 昨天的日报处理完成。
2025-05-05 07:39:10,729 - INFO - ==================================================
2025-05-05 08:00:02,210 - INFO - ==================================================
2025-05-05 08:00:02,211 - INFO - 开始生成日报统计数据...
2025-05-05 08:00:02,228 - INFO - 开始生成 2025-05-04 的报表数据...
2025-05-05 08:00:02,228 - INFO - 获取 2025-05-04 的订单数据（下单时间维度）...
2025-05-05 08:00:02,252 - INFO - 成功获取到 150 条去重后的订单数据（下单时间维度）
2025-05-05 08:00:02,252 - INFO - 开始分类并过滤订单数据...
2025-05-05 08:00:02,252 - INFO - 订单分类完成: 有效订单 150 单，其中骑手卡 144 单，流量卡 6 单
2025-05-05 08:00:02,252 - INFO - 计算各渠道的D0下单和D1激活数据...
2025-05-05 08:00:02,273 - INFO - 成功获取到 156 条当日激活订单
2025-05-05 08:00:02,277 - INFO - 样本订单日期检查 (共5个):
2025-05-05 08:00:02,277 - INFO - 订单 20250502100598235160: 下单时间=2025-05-02, 激活时间=2025-05-04, 相差=2天
2025-05-05 08:00:02,277 - INFO - 订单 20250502230214867899: 下单时间=2025-05-02, 激活时间=2025-05-04, 相差=2天
2025-05-05 08:00:02,277 - INFO - 订单 20250503154996620859: 下单时间=2025-05-03, 激活时间=2025-05-04, 相差=1天
2025-05-05 08:00:02,277 - INFO - 订单 20250502151316481246: 下单时间=2025-05-02, 激活时间=2025-05-04, 相差=2天
2025-05-05 08:00:02,277 - INFO - 订单 20250502114001188512: 下单时间=2025-05-02, 激活时间=2025-05-04, 相差=2天
2025-05-05 08:00:02,277 - INFO - 熟龄度处理统计: 总订单=156, 成功处理=156, 处理失败=0
2025-05-05 08:00:02,277 - INFO - 熟龄度分布: 当天=3, 昨天=56, 前天=45, 更早=52
2025-05-05 08:00:02,277 - INFO - 骑手卡熟龄度分布: 当天=3, 昨天=55, 前天=35, 更早=37
2025-05-05 08:00:02,277 - INFO - 流量卡熟龄度分布: 当天=0, 昨天=1, 前天=10, 更早=15
2025-05-05 08:00:02,278 - INFO - 熟龄度数据: {'same_day': 3, 'one_day': 56, 'two_days': 45, 'more_days': 52}
2025-05-05 08:00:02,278 - INFO - 骑手卡熟龄度数据: {'same_day': 3, 'one_day': 55, 'two_days': 35, 'more_days': 37}, 总数: 130
2025-05-05 08:00:02,278 - INFO - 流量卡熟龄度数据: {'same_day': 0, 'one_day': 1, 'two_days': 10, 'more_days': 15}, 总数: 26
2025-05-05 08:00:02,278 - INFO - 当日下单当日激活比例: 1.9%（3/156）
2025-05-05 08:00:02,278 - INFO - 样本订单日期检查 (共5个): 156，熟龄度分布: 当天=3, 昨天=56, 前天=45, 更早=52
2025-05-05 08:00:02,278 - INFO - 生成HTML报表模板，加载数据: 总活跃=156, 骑手卡=130, 流量卡=26
2025-05-05 08:00:02,278 - INFO - 骑手卡数据: [3, 55, 35, 37]
2025-05-05 08:00:02,279 - INFO - 流量卡数据: [0, 1, 10, 15]
2025-05-05 08:00:02,280 - INFO - HTML报表已生成: reports/report_20250504.html
2025-05-05 08:00:02,280 - INFO - 准备上传文件到FTP服务器: report_20250504.html
2025-05-05 08:00:02,280 - INFO - FTP配置: 主机=211.149.249.234, 端口=21, 目录=reports
2025-05-05 08:00:02,280 - INFO - 本地文件: reports/report_20250504.html, 大小: 33951 字节
2025-05-05 08:00:02,280 - INFO - 正在连接到FTP服务器: 211.149.249.234:21
2025-05-05 08:00:02,341 - INFO - 正在登录FTP服务器: 用户名=reports
2025-05-05 08:00:02,416 - INFO - FTP登录成功
2025-05-05 08:00:02,445 - INFO - 当前FTP目录: /
2025-05-05 08:00:02,445 - INFO - 尝试切换到目录: reports
2025-05-05 08:00:02,504 - INFO - 成功切换到目录: /reports
2025-05-05 08:00:02,624 - INFO - 上传前目录内容: ['report_20250503.html', 'report_20250502.html', '..', 'report_20250501.html', 'report_20250504.html', '.']
2025-05-05 08:00:02,624 - INFO - 开始上传文件: reports/report_20250504.html -> report_20250504.html
2025-05-05 08:00:02,809 - INFO - FTP上传结果: 226-File successfully transferred
226 0.064 seconds (measured here), 0.51 Mbytes per second
2025-05-05 08:00:02,932 - INFO - 上传后目录内容: ['report_20250503.html', 'report_20250502.html', '..', 'report_20250501.html', 'report_20250504.html', '.']
2025-05-05 08:00:02,932 - INFO - 文件已成功上传并验证: report_20250504.html
2025-05-05 08:00:02,932 - INFO - 文件访问URL: https://b.zhoumeiren.cn/reports/report_20250504.html
2025-05-05 08:00:02,961 - INFO - FTP连接已关闭
2025-05-05 08:00:02,961 - INFO - 报表已上传到服务器，URL: https://b.zhoumeiren.cn/reports/report_20250504.html
2025-05-05 08:00:02,962 - INFO - 日报生成完成
2025-05-05 08:00:02,962 - INFO - ==================================================
2025-05-05 08:00:03,123 - INFO - ==================================================
2025-05-05 08:00:03,123 - INFO - 开始生成并发送昨天的日报（测试环境）...
2025-05-05 08:00:03,123 - INFO - 开始执行昨天的日报发送流程（测试环境）...
2025-05-05 08:00:03,140 - INFO - 昨天日期: 2025-05-04
2025-05-05 08:00:03,140 - INFO - 开始获取昨天的订单数据（下单时间维度）...
2025-05-05 08:00:03,140 - INFO - 获取 2025-05-04 的订单数据（下单时间维度）...
2025-05-05 08:00:03,162 - INFO - 成功获取到 150 条去重后的订单数据（下单时间维度）
2025-05-05 08:00:03,162 - INFO - 开始获取昨天的订单数据（激活时间维度）...
2025-05-05 08:00:03,162 - INFO - 获取 2025-05-04 的订单数据（激活时间维度）...
2025-05-05 08:00:03,184 - INFO - 成功获取到 156 条去重后的订单数据（激活时间维度）
2025-05-05 08:00:03,184 - INFO - 成功获取到订单数据，继续生成日报
2025-05-05 08:00:03,184 - INFO - 开始生成昨天的日报...
2025-05-05 08:00:03,185 - INFO - 开始生成日报...
2025-05-05 08:00:03,185 - INFO - 开始分类并过滤订单数据...
2025-05-05 08:00:03,185 - INFO - 订单分类完成: 有效订单 150 单，其中骑手卡 144 单，大流量卡 6 单
2025-05-05 08:00:03,185 - INFO - 统计各类订单按渠道分类...
2025-05-05 08:00:03,185 - INFO - 开始分类并过滤订单数据...
2025-05-05 08:00:03,185 - INFO - 订单分类完成: 有效订单 156 单，其中骑手卡 130 单，大流量卡 26 单
2025-05-05 08:00:03,185 - INFO - 统计各类订单按渠道分类...
2025-05-05 08:00:03,186 - INFO - 日报生成完成
2025-05-05 08:00:03,186 - INFO - 开始分类并过滤订单数据...
2025-05-05 08:00:03,186 - INFO - 订单分类完成: 有效订单 150 单，其中骑手卡 144 单，大流量卡 6 单
2025-05-05 08:00:03,186 - INFO - 统计各类订单按渠道分类...
2025-05-05 08:00:03,186 - INFO - 开始分类并过滤订单数据...
2025-05-05 08:00:03,186 - INFO - 订单分类完成: 有效订单 156 单，其中骑手卡 130 单，大流量卡 26 单
2025-05-05 08:00:03,186 - INFO - 统计各类订单按渠道分类...
2025-05-05 08:00:03,188 - INFO - 开始生成 2025-05-04 的报表数据...
2025-05-05 08:00:03,188 - INFO - 获取 2025-05-04 的订单数据（下单时间维度）...
2025-05-05 08:00:03,210 - INFO - 成功获取到 150 条去重后的订单数据（下单时间维度）
2025-05-05 08:00:03,210 - INFO - 开始分类并过滤订单数据...
2025-05-05 08:00:03,210 - INFO - 订单分类完成: 有效订单 150 单，其中骑手卡 144 单，流量卡 6 单
2025-05-05 08:00:03,210 - INFO - 计算各渠道的D0下单和D1激活数据...
2025-05-05 08:00:03,231 - INFO - 成功获取到 156 条当日激活订单
2025-05-05 08:00:03,233 - INFO - 样本订单日期检查 (共5个):
2025-05-05 08:00:03,233 - INFO - 订单 20250502100598235160: 下单时间=2025-05-02, 激活时间=2025-05-04, 相差=2天
2025-05-05 08:00:03,233 - INFO - 订单 20250502230214867899: 下单时间=2025-05-02, 激活时间=2025-05-04, 相差=2天
2025-05-05 08:00:03,234 - INFO - 订单 20250503154996620859: 下单时间=2025-05-03, 激活时间=2025-05-04, 相差=1天
2025-05-05 08:00:03,234 - INFO - 订单 20250502151316481246: 下单时间=2025-05-02, 激活时间=2025-05-04, 相差=2天
2025-05-05 08:00:03,234 - INFO - 订单 20250502114001188512: 下单时间=2025-05-02, 激活时间=2025-05-04, 相差=2天
2025-05-05 08:00:03,234 - INFO - 熟龄度处理统计: 总订单=156, 成功处理=156, 处理失败=0
2025-05-05 08:00:03,234 - INFO - 熟龄度分布: 当天=3, 昨天=56, 前天=45, 更早=52
2025-05-05 08:00:03,234 - INFO - 骑手卡熟龄度分布: 当天=3, 昨天=55, 前天=35, 更早=37
2025-05-05 08:00:03,234 - INFO - 流量卡熟龄度分布: 当天=0, 昨天=1, 前天=10, 更早=15
2025-05-05 08:00:03,234 - INFO - 熟龄度数据: {'same_day': 3, 'one_day': 56, 'two_days': 45, 'more_days': 52}
2025-05-05 08:00:03,234 - INFO - 骑手卡熟龄度数据: {'same_day': 3, 'one_day': 55, 'two_days': 35, 'more_days': 37}, 总数: 130
2025-05-05 08:00:03,235 - INFO - 流量卡熟龄度数据: {'same_day': 0, 'one_day': 1, 'two_days': 10, 'more_days': 15}, 总数: 26
2025-05-05 08:00:03,235 - INFO - 当日下单当日激活比例: 1.9%（3/156）
2025-05-05 08:00:03,235 - INFO - 样本订单日期检查 (共5个): 156，熟龄度分布: 当天=3, 昨天=56, 前天=45, 更早=52
2025-05-05 08:00:03,235 - INFO - 开始发送模板卡片消息到企业微信...
2025-05-05 08:00:03,235 - INFO - 开始发送消息到企业微信（测试环境）...
2025-05-05 08:00:03,547 - INFO - 企业微信API响应状态码: 200
2025-05-05 08:00:03,547 - INFO - 企业微信消息发送成功
2025-05-05 08:00:03,547 - INFO - 昨天的日报发送成功
2025-05-05 08:00:03,548 - INFO - 昨天的日报处理完成。
2025-05-05 08:00:03,548 - INFO - ==================================================
2025-05-05 08:01:19,074 - INFO - ==================================================
2025-05-05 08:01:19,075 - INFO - 开始生成日报统计数据...
2025-05-05 08:01:19,091 - INFO - 开始生成 2025-05-04 的报表数据...
2025-05-05 08:01:19,091 - INFO - 获取 2025-05-04 的订单数据（下单时间维度）...
2025-05-05 08:01:19,114 - INFO - 成功获取到 150 条去重后的订单数据（下单时间维度）
2025-05-05 08:01:19,115 - INFO - 开始分类并过滤订单数据...
2025-05-05 08:01:19,115 - INFO - 订单分类完成: 有效订单 150 单，其中骑手卡 144 单，流量卡 6 单
2025-05-05 08:01:19,115 - INFO - 计算各渠道的D0下单和D1激活数据...
2025-05-05 08:01:19,136 - INFO - 成功获取到 156 条当日激活订单
2025-05-05 08:01:19,140 - INFO - 样本订单日期检查 (共5个):
2025-05-05 08:01:19,140 - INFO - 订单 20250502100598235160: 下单时间=2025-05-02, 激活时间=2025-05-04, 相差=2天
2025-05-05 08:01:19,140 - INFO - 订单 20250502230214867899: 下单时间=2025-05-02, 激活时间=2025-05-04, 相差=2天
2025-05-05 08:01:19,140 - INFO - 订单 20250503154996620859: 下单时间=2025-05-03, 激活时间=2025-05-04, 相差=1天
2025-05-05 08:01:19,140 - INFO - 订单 20250502151316481246: 下单时间=2025-05-02, 激活时间=2025-05-04, 相差=2天
2025-05-05 08:01:19,140 - INFO - 订单 20250502114001188512: 下单时间=2025-05-02, 激活时间=2025-05-04, 相差=2天
2025-05-05 08:01:19,141 - INFO - 熟龄度处理统计: 总订单=156, 成功处理=156, 处理失败=0
2025-05-05 08:01:19,141 - INFO - 熟龄度分布: 当天=3, 昨天=56, 前天=45, 更早=52
2025-05-05 08:01:19,141 - INFO - 骑手卡熟龄度分布: 当天=3, 昨天=55, 前天=35, 更早=37
2025-05-05 08:01:19,141 - INFO - 流量卡熟龄度分布: 当天=0, 昨天=1, 前天=10, 更早=15
2025-05-05 08:01:19,141 - INFO - 熟龄度数据: {'same_day': 3, 'one_day': 56, 'two_days': 45, 'more_days': 52}
2025-05-05 08:01:19,141 - INFO - 骑手卡熟龄度数据: {'same_day': 3, 'one_day': 55, 'two_days': 35, 'more_days': 37}, 总数: 130
2025-05-05 08:01:19,141 - INFO - 流量卡熟龄度数据: {'same_day': 0, 'one_day': 1, 'two_days': 10, 'more_days': 15}, 总数: 26
2025-05-05 08:01:19,141 - INFO - 当日下单当日激活比例: 1.9%（3/156）
2025-05-05 08:01:19,141 - INFO - 样本订单日期检查 (共5个): 156，熟龄度分布: 当天=3, 昨天=56, 前天=45, 更早=52
2025-05-05 08:01:19,142 - INFO - 生成HTML报表模板，加载数据: 总活跃=156, 骑手卡=130, 流量卡=26
2025-05-05 08:01:19,142 - INFO - 骑手卡数据: [3, 55, 35, 37]
2025-05-05 08:01:19,142 - INFO - 流量卡数据: [0, 1, 10, 15]
2025-05-05 08:01:19,142 - INFO - HTML报表已生成: reports/report_20250504.html
2025-05-05 08:01:19,142 - INFO - 准备上传文件到FTP服务器: report_20250504.html
2025-05-05 08:01:19,142 - INFO - FTP配置: 主机=211.149.249.234, 端口=21, 目录=reports
2025-05-05 08:01:19,142 - INFO - 本地文件: reports/report_20250504.html, 大小: 33951 字节
2025-05-05 08:01:19,142 - INFO - 正在连接到FTP服务器: 211.149.249.234:21
2025-05-05 08:01:19,216 - INFO - 正在登录FTP服务器: 用户名=reports
2025-05-05 08:01:19,314 - INFO - FTP登录成功
2025-05-05 08:01:19,350 - INFO - 当前FTP目录: /
2025-05-05 08:01:19,350 - INFO - 尝试切换到目录: reports
2025-05-05 08:01:19,420 - INFO - 成功切换到目录: /reports
2025-05-05 08:01:19,562 - INFO - 上传前目录内容: ['report_20250503.html', 'report_20250502.html', '..', 'report_20250501.html', 'report_20250504.html', '.']
2025-05-05 08:01:19,562 - INFO - 开始上传文件: reports/report_20250504.html -> report_20250504.html
2025-05-05 08:01:19,765 - INFO - FTP上传结果: 226-File successfully transferred
226 0.067 seconds (measured here), 492.02 Kbytes per second
2025-05-05 08:01:19,903 - INFO - 上传后目录内容: ['report_20250503.html', 'report_20250502.html', '..', 'report_20250501.html', 'report_20250504.html', '.']
2025-05-05 08:01:19,903 - INFO - 文件已成功上传并验证: report_20250504.html
2025-05-05 08:01:19,903 - INFO - 文件访问URL: https://b.zhoumeiren.cn/reports/report_20250504.html
2025-05-05 08:01:19,938 - INFO - FTP连接已关闭
2025-05-05 08:01:19,939 - INFO - 报表已上传到服务器，URL: https://b.zhoumeiren.cn/reports/report_20250504.html
2025-05-05 08:01:19,939 - INFO - 日报生成完成
2025-05-05 08:01:19,939 - INFO - ==================================================
2025-05-05 08:01:20,100 - INFO - ==================================================
2025-05-05 08:01:20,100 - INFO - 开始生成并发送昨天的日报（全渠道生产环境）...
2025-05-05 08:01:20,100 - INFO - 开始执行昨天的日报发送流程（全渠道生产环境）...
2025-05-05 08:01:20,117 - INFO - 昨天日期: 2025-05-04
2025-05-05 08:01:20,118 - INFO - 开始获取昨天的订单数据（下单时间维度）...
2025-05-05 08:01:20,118 - INFO - 获取 2025-05-04 的订单数据（下单时间维度）...
2025-05-05 08:01:20,140 - INFO - 成功获取到 150 条去重后的订单数据（下单时间维度）
2025-05-05 08:01:20,141 - INFO - 开始获取昨天的订单数据（激活时间维度）...
2025-05-05 08:01:20,141 - INFO - 获取 2025-05-04 的订单数据（激活时间维度）...
2025-05-05 08:01:20,163 - INFO - 成功获取到 156 条去重后的订单数据（激活时间维度）
2025-05-05 08:01:20,163 - INFO - 成功获取到订单数据，继续生成日报
2025-05-05 08:01:20,163 - INFO - 开始生成昨天的日报...
2025-05-05 08:01:20,163 - INFO - 开始生成日报...
2025-05-05 08:01:20,164 - INFO - 开始分类并过滤订单数据...
2025-05-05 08:01:20,164 - INFO - 订单分类完成: 有效订单 150 单，其中骑手卡 144 单，大流量卡 6 单
2025-05-05 08:01:20,164 - INFO - 统计各类订单按渠道分类...
2025-05-05 08:01:20,164 - INFO - 开始分类并过滤订单数据...
2025-05-05 08:01:20,164 - INFO - 订单分类完成: 有效订单 156 单，其中骑手卡 130 单，大流量卡 26 单
2025-05-05 08:01:20,164 - INFO - 统计各类订单按渠道分类...
2025-05-05 08:01:20,164 - INFO - 日报生成完成
2025-05-05 08:01:20,165 - INFO - 开始分类并过滤订单数据...
2025-05-05 08:01:20,165 - INFO - 订单分类完成: 有效订单 150 单，其中骑手卡 144 单，大流量卡 6 单
2025-05-05 08:01:20,165 - INFO - 统计各类订单按渠道分类...
2025-05-05 08:01:20,165 - INFO - 开始分类并过滤订单数据...
2025-05-05 08:01:20,165 - INFO - 订单分类完成: 有效订单 156 单，其中骑手卡 130 单，大流量卡 26 单
2025-05-05 08:01:20,165 - INFO - 统计各类订单按渠道分类...
2025-05-05 08:01:20,167 - INFO - 开始生成 2025-05-04 的报表数据...
2025-05-05 08:01:20,167 - INFO - 获取 2025-05-04 的订单数据（下单时间维度）...
2025-05-05 08:01:20,189 - INFO - 成功获取到 150 条去重后的订单数据（下单时间维度）
2025-05-05 08:01:20,189 - INFO - 开始分类并过滤订单数据...
2025-05-05 08:01:20,189 - INFO - 订单分类完成: 有效订单 150 单，其中骑手卡 144 单，流量卡 6 单
2025-05-05 08:01:20,189 - INFO - 计算各渠道的D0下单和D1激活数据...
2025-05-05 08:01:20,211 - INFO - 成功获取到 156 条当日激活订单
2025-05-05 08:01:20,213 - INFO - 样本订单日期检查 (共5个):
2025-05-05 08:01:20,213 - INFO - 订单 20250502100598235160: 下单时间=2025-05-02, 激活时间=2025-05-04, 相差=2天
2025-05-05 08:01:20,213 - INFO - 订单 20250502230214867899: 下单时间=2025-05-02, 激活时间=2025-05-04, 相差=2天
2025-05-05 08:01:20,213 - INFO - 订单 20250503154996620859: 下单时间=2025-05-03, 激活时间=2025-05-04, 相差=1天
2025-05-05 08:01:20,213 - INFO - 订单 20250502151316481246: 下单时间=2025-05-02, 激活时间=2025-05-04, 相差=2天
2025-05-05 08:01:20,213 - INFO - 订单 20250502114001188512: 下单时间=2025-05-02, 激活时间=2025-05-04, 相差=2天
2025-05-05 08:01:20,213 - INFO - 熟龄度处理统计: 总订单=156, 成功处理=156, 处理失败=0
2025-05-05 08:01:20,213 - INFO - 熟龄度分布: 当天=3, 昨天=56, 前天=45, 更早=52
2025-05-05 08:01:20,213 - INFO - 骑手卡熟龄度分布: 当天=3, 昨天=55, 前天=35, 更早=37
2025-05-05 08:01:20,213 - INFO - 流量卡熟龄度分布: 当天=0, 昨天=1, 前天=10, 更早=15
2025-05-05 08:01:20,214 - INFO - 熟龄度数据: {'same_day': 3, 'one_day': 56, 'two_days': 45, 'more_days': 52}
2025-05-05 08:01:20,214 - INFO - 骑手卡熟龄度数据: {'same_day': 3, 'one_day': 55, 'two_days': 35, 'more_days': 37}, 总数: 130
2025-05-05 08:01:20,214 - INFO - 流量卡熟龄度数据: {'same_day': 0, 'one_day': 1, 'two_days': 10, 'more_days': 15}, 总数: 26
2025-05-05 08:01:20,214 - INFO - 当日下单当日激活比例: 1.9%（3/156）
2025-05-05 08:01:20,214 - INFO - 样本订单日期检查 (共5个): 156，熟龄度分布: 当天=3, 昨天=56, 前天=45, 更早=52
2025-05-05 08:01:20,214 - INFO - 开始发送模板卡片消息到企业微信...
2025-05-05 08:01:20,214 - INFO - 开始发送消息到企业微信（全渠道生产环境）...
2025-05-05 08:01:20,464 - INFO - 企业微信API响应状态码: 200
2025-05-05 08:01:20,464 - INFO - 企业微信消息发送成功
2025-05-05 08:01:20,464 - INFO - 昨天的日报发送成功
2025-05-05 08:01:20,464 - INFO - 昨天的日报处理完成。
2025-05-05 08:01:20,464 - INFO - ==================================================
