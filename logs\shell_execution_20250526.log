[2025-05-26 08:00:01] 开始执行日报脚本
[2025-05-26 08:00:01] 工作目录: /volume3/SSD-soft/fzsrb
[2025-05-26 08:00:01] 检查Python依赖...
WARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager, possibly rendering your system unusable. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv. Use the --root-user-action option if you know what you are doing and want to suppress this warning.
[2025-05-26 08:00:01] 开始执行简单日报脚本...
2025-05-26 08:00:03,780 - INFO - ==================================================
2025-05-26 08:00:03,780 - INFO - 开始生成日报统计数据...
2025-05-26 08:00:03,811 - INFO - 开始生成 2025-05-25 的报表数据...
2025-05-26 08:00:03,811 - INFO - 获取 2025-05-25 的订单数据（下单时间维度）...
2025-05-26 08:00:03,852 - INFO - 成功获取到 226 条去重后的订单数据（下单时间维度）
2025-05-26 08:00:03,852 - INFO - 开始分类并过滤订单数据...
2025-05-26 08:00:03,852 - INFO - 订单分类完成: 有效订单 226 单，其中骑手卡 226 单，流量卡 0 单
2025-05-26 08:00:03,853 - INFO - 计算各渠道的D0下单和D1激活数据...
2025-05-26 08:00:03,882 - INFO - 成功获取到 170 条当日激活订单
2025-05-26 08:00:03,885 - INFO - 样本订单日期检查 (共5个):
2025-05-26 08:00:03,885 - INFO - 订单 20250523223683471999: 下单时间=2025-05-23, 激活时间=2025-05-25, 相差=2天
2025-05-26 08:00:03,886 - INFO - 订单 20250524102435324206: 下单时间=2025-05-24, 激活时间=2025-05-25, 相差=1天
2025-05-26 08:00:03,886 - INFO - 订单 20250524120783546828: 下单时间=2025-05-24, 激活时间=2025-05-25, 相差=1天
2025-05-26 08:00:03,886 - INFO - 订单 20250523152623686816: 下单时间=2025-05-23, 激活时间=2025-05-25, 相差=2天
2025-05-26 08:00:03,886 - INFO - 订单 20250524150347556109: 下单时间=2025-05-24, 激活时间=2025-05-25, 相差=1天
2025-05-26 08:00:03,886 - INFO - 熟龄度处理统计: 总订单=170, 成功处理=170, 处理失败=0
2025-05-26 08:00:03,886 - INFO - 熟龄度分布: 当天=12, 昨天=84, 前天=47, 更早=27
2025-05-26 08:00:03,886 - INFO - 骑手卡熟龄度分布: 当天=12, 昨天=84, 前天=47, 更早=26
2025-05-26 08:00:03,886 - INFO - 流量卡熟龄度分布: 当天=0, 昨天=0, 前天=0, 更早=1
2025-05-26 08:00:03,886 - INFO - 获取报表日期与前一日订单数据对比...
2025-05-26 08:00:03,886 - INFO - 对比日期: 报表日期=2025-05-25, 前一日=2025-05-24
2025-05-26 08:00:03,887 - INFO - 获取 2025-05-25 的订单数据（下单时间维度）...
2025-05-26 08:00:03,918 - INFO - 成功获取到 226 条去重后的订单数据（下单时间维度）
2025-05-26 08:00:03,918 - INFO - 获取 2025-05-24 的订单数据（下单时间维度）...
2025-05-26 08:00:03,949 - INFO - 成功获取到 217 条去重后的订单数据（下单时间维度）
2025-05-26 08:00:03,950 - INFO - 获取周订单趋势数据...
2025-05-26 08:00:03,950 - INFO - 查询日期范围: 2025-05-19 00:00:00 至 2025-05-25 23:59:59
2025-05-26 08:00:03,976 - INFO - 成功获取到 7 天的订单趋势数据
2025-05-26 08:00:03,976 - INFO - 熟龄度数据: {'same_day': 12, 'one_day': 84, 'two_days': 47, 'more_days': 27}
2025-05-26 08:00:03,976 - INFO - 骑手卡熟龄度数据: {'same_day': 12, 'one_day': 84, 'two_days': 47, 'more_days': 26}, 总数: 169
2025-05-26 08:00:03,976 - INFO - 流量卡熟龄度数据: {'same_day': 0, 'one_day': 0, 'two_days': 0, 'more_days': 1}, 总数: 1
2025-05-26 08:00:03,976 - INFO - 当日下单当日激活比例: 7.1%（12/170）
2025-05-26 08:00:03,976 - INFO - 样本订单日期检查 (共5个): 170，熟龄度分布: 当天=12, 昨天=84, 前天=47, 更早=27
2025-05-26 08:00:03,976 - INFO - 环比分析: 当前=226单, 前一日=217单, 变化=4.1%
2025-05-26 08:00:03,976 - INFO - 周趋势数据获取成功，共7天数据
2025-05-26 08:00:03,977 - INFO - 生成HTML报表模板，加载数据: 总活跃=170, 骑手卡=169, 流量卡=1
2025-05-26 08:00:03,977 - INFO - 骑手卡数据: [12, 84, 47, 26]
2025-05-26 08:00:03,977 - INFO - 流量卡数据: [0, 0, 0, 1]
2025-05-26 08:00:03,977 - INFO - HTML报表已生成: reports/report_20250525.html
2025-05-26 08:00:03,977 - INFO - 准备上传文件到FTP服务器: report_20250525.html
2025-05-26 08:00:03,978 - INFO - FTP配置: 主机=211.149.249.234, 端口=21, 目录=reports
2025-05-26 08:00:03,978 - INFO - 本地文件: reports/report_20250525.html, 大小: 52886 字节
2025-05-26 08:00:03,978 - INFO - 正在连接到FTP服务器: 211.149.249.234:21
2025-05-26 08:00:04,045 - INFO - 正在登录FTP服务器: 用户名=reports
2025-05-26 08:00:04,142 - INFO - FTP登录成功
2025-05-26 08:00:04,174 - INFO - 当前FTP目录: /
2025-05-26 08:00:04,174 - INFO - 尝试切换到目录: reports
2025-05-26 08:00:04,241 - INFO - 成功切换到目录: /reports
2025-05-26 08:00:04,385 - INFO - 上传前目录内容: ['report_20250506.html', 'report_20250522.html', 'report_20250511.html', 'report_20250515.html', 'report_20250518.html', 'report_20250516.html', 'report_20250512.html', 'report_20250503.html', 'report_20250508.html', 'report_20250502.html', 'report_20250519.html', '..', 'report_20250513.html', 'report_20250524.html', 'report_20250509.html', 'report_20250514.html', 'report_20250510.html', 'report_20250520.html', 'report_20250521.html', 'report_20250523.html', 'report_20250501.html', 'report_20250505.html', 'report_20250504.html', 'report_20250507.html', 'report_20250517.html', '.']
2025-05-26 08:00:04,386 - INFO - 开始上传文件: reports/report_20250525.html -> report_20250525.html
2025-05-26 08:00:04,619 - INFO - FTP上传结果: 226-File successfully transferred
226 0.101 seconds (measured here), 511.55 Kbytes per second
2025-05-26 08:00:04,748 - INFO - 上传后目录内容: ['report_20250506.html', 'report_20250522.html', 'report_20250511.html', 'report_20250515.html', 'report_20250518.html', 'report_20250516.html', 'report_20250512.html', 'report_20250503.html', 'report_20250508.html', 'report_20250502.html', 'report_20250519.html', 'report_20250525.html', '..', 'report_20250513.html', 'report_20250524.html', 'report_20250509.html', 'report_20250514.html', 'report_20250510.html', 'report_20250520.html', 'report_20250521.html', 'report_20250523.html', 'report_20250501.html', 'report_20250505.html', 'report_20250504.html', 'report_20250507.html', 'report_20250517.html', '.']
2025-05-26 08:00:04,748 - INFO - 文件已成功上传并验证: report_20250525.html
2025-05-26 08:00:04,748 - INFO - 文件访问URL: https://rep.331126.com/reports/report_20250525.html
2025-05-26 08:00:04,780 - INFO - FTP连接已关闭
2025-05-26 08:00:04,780 - INFO - 报表已上传到服务器，URL: https://rep.331126.com/reports/report_20250525.html
2025-05-26 08:00:04,780 - INFO - 日报生成完成
2025-05-26 08:00:04,780 - INFO - ==================================================
*get* '220---------- Welcome to Pure-FTPd [privsep] [TLS] ----------\n'
*get* '220-You are user number 1 of 50 allowed.\n'
*get* '220-Local time is now 08:00. Server port: 21.\n'
*get* '220-This is a private system - No anonymous login\n'
*get* '220-IPv6 connections are also welcome on this server.\n'
*get* '220 You will be disconnected after 15 minutes of inactivity.\n'
*resp* '220---------- Welcome to Pure-FTPd [privsep] [TLS] ----------\n220-You are user number 1 of 50 allowed.\n220-Local time is now 08:00. Server port: 21.\n220-This is a private system - No anonymous login\n220-IPv6 connections are also welcome on this server.\n220 You will be disconnected after 15 minutes of inactivity.'
*cmd* 'USER reports'
*put* 'USER reports\r\n'
*get* '331 User reports OK. Password required\n'
*resp* '331 User reports OK. Password required'
*cmd* 'PASS ****************'
*put* 'PASS ****************\r\n'
*get* '230 OK. Current directory is /\n'
*resp* '230 OK. Current directory is /'
*cmd* 'PWD'
*put* 'PWD\r\n'
*get* '257 "/" is your current location\n'
*resp* '257 "/" is your current location'
*cmd* 'CWD reports'
*put* 'CWD reports\r\n'
*get* '250 OK. Current directory is /reports\n'
*resp* '250 OK. Current directory is /reports'
*cmd* 'PWD'
*put* 'PWD\r\n'
*get* '257 "/reports" is your current location\n'
*resp* '257 "/reports" is your current location'
*cmd* 'TYPE A'
*put* 'TYPE A\r\n'
*get* '200 TYPE is now ASCII\n'
*resp* '200 TYPE is now ASCII'
*cmd* 'PASV'
*put* 'PASV\r\n'
*get* '227 Entering Passive Mode (211,149,249,234,153,61)\n'
*resp* '227 Entering Passive Mode (211,149,249,234,153,61)'
*cmd* 'NLST'
*put* 'NLST\r\n'
*get* '150 Accepted data connection\n'
*resp* '150 Accepted data connection'
*get* '226 26 matches total\n'
*resp* '226 26 matches total'
*cmd* 'TYPE I'
*put* 'TYPE I\r\n'
*get* '200 TYPE is now 8-bit binary\n'
*resp* '200 TYPE is now 8-bit binary'
*cmd* 'PASV'
*put* 'PASV\r\n'
*get* '227 Entering Passive Mode (211,149,249,234,153,13)\n'
*resp* '227 Entering Passive Mode (211,149,249,234,153,13)'
*cmd* 'STOR report_20250525.html'
*put* 'STOR report_20250525.html\r\n'
*get* '150 Accepted data connection\n'
*resp* '150 Accepted data connection'
*get* '226-File successfully transferred\n'
*get* '226 0.101 seconds (measured here), 511.55 Kbytes per second\n'
*resp* '226-File successfully transferred\n226 0.101 seconds (measured here), 511.55 Kbytes per second'
*cmd* 'TYPE A'
*put* 'TYPE A\r\n'
*get* '200 TYPE is now ASCII\n'
*resp* '200 TYPE is now ASCII'
*cmd* 'PASV'
*put* 'PASV\r\n'
*get* '227 Entering Passive Mode (211,149,249,234,152,198)\n'
*resp* '227 Entering Passive Mode (211,149,249,234,152,198)'
*cmd* 'NLST'
*put* 'NLST\r\n'
*get* '150 Accepted data connection\n'
*resp* '150 Accepted data connection'
*get* '226 27 matches total\n'
*resp* '226 27 matches total'
*cmd* 'QUIT'
*put* 'QUIT\r\n'
*get* '221-Goodbye. You uploaded 52 and downloaded 0 kbytes.\n'
*get* '221 Logout.\n'
*resp* '221-Goodbye. You uploaded 52 and downloaded 0 kbytes.\n221 Logout.'
[2025-05-26 08:00:01] 简单日报脚本执行成功
[2025-05-26 08:00:01] 开始执行全渠道日报脚本...
2025-05-26 08:00:04,959 - INFO - ==================================================
2025-05-26 08:00:04,959 - INFO - 开始生成并发送昨天的日报（全渠道生产环境）...
2025-05-26 08:00:04,959 - INFO - 开始执行昨天的日报发送流程（全渠道生产环境）...
2025-05-26 08:00:04,976 - INFO - 昨天日期: 2025-05-25
2025-05-26 08:00:04,976 - INFO - 开始获取昨天的订单数据（下单时间维度）...
2025-05-26 08:00:04,976 - INFO - 获取 2025-05-25 的订单数据（下单时间维度）...
2025-05-26 08:00:05,009 - INFO - 成功获取到 226 条去重后的订单数据（下单时间维度）
2025-05-26 08:00:05,009 - INFO - 开始获取昨天的订单数据（激活时间维度）...
2025-05-26 08:00:05,009 - INFO - 获取 2025-05-25 的订单数据（激活时间维度）...
2025-05-26 08:00:05,039 - INFO - 成功获取到 170 条去重后的订单数据（激活时间维度）
2025-05-26 08:00:05,039 - INFO - 成功获取到订单数据，继续生成日报
2025-05-26 08:00:05,039 - INFO - 开始生成昨天的日报...
2025-05-26 08:00:05,039 - INFO - 开始生成日报...
2025-05-26 08:00:05,040 - INFO - 开始分类并过滤订单数据...
2025-05-26 08:00:05,040 - INFO - 订单分类完成: 有效订单 226 单，其中骑手卡 226 单，大流量卡 0 单
2025-05-26 08:00:05,040 - INFO - 统计各类订单按渠道分类...
2025-05-26 08:00:05,040 - INFO - 开始分类并过滤订单数据...
2025-05-26 08:00:05,040 - INFO - 订单分类完成: 有效订单 170 单，其中骑手卡 169 单，大流量卡 1 单
2025-05-26 08:00:05,040 - INFO - 统计各类订单按渠道分类...
2025-05-26 08:00:05,041 - INFO - 日报生成完成
2025-05-26 08:00:05,041 - INFO - 开始分类并过滤订单数据...
2025-05-26 08:00:05,041 - INFO - 订单分类完成: 有效订单 226 单，其中骑手卡 226 单，大流量卡 0 单
2025-05-26 08:00:05,041 - INFO - 统计各类订单按渠道分类...
2025-05-26 08:00:05,041 - INFO - 开始分类并过滤订单数据...
2025-05-26 08:00:05,041 - INFO - 订单分类完成: 有效订单 170 单，其中骑手卡 169 单，大流量卡 1 单
2025-05-26 08:00:05,041 - INFO - 统计各类订单按渠道分类...
2025-05-26 08:00:05,044 - INFO - 开始生成 2025-05-25 的报表数据...
2025-05-26 08:00:05,044 - INFO - 获取 2025-05-25 的订单数据（下单时间维度）...
2025-05-26 08:00:05,077 - INFO - 成功获取到 226 条去重后的订单数据（下单时间维度）
2025-05-26 08:00:05,078 - INFO - 开始分类并过滤订单数据...
2025-05-26 08:00:05,078 - INFO - 订单分类完成: 有效订单 226 单，其中骑手卡 226 单，流量卡 0 单
2025-05-26 08:00:05,078 - INFO - 计算各渠道的D0下单和D1激活数据...
2025-05-26 08:00:05,107 - INFO - 成功获取到 170 条当日激活订单
2025-05-26 08:00:05,109 - INFO - 样本订单日期检查 (共5个):
2025-05-26 08:00:05,109 - INFO - 订单 20250523223683471999: 下单时间=2025-05-23, 激活时间=2025-05-25, 相差=2天
2025-05-26 08:00:05,109 - INFO - 订单 20250524102435324206: 下单时间=2025-05-24, 激活时间=2025-05-25, 相差=1天
2025-05-26 08:00:05,109 - INFO - 订单 20250524120783546828: 下单时间=2025-05-24, 激活时间=2025-05-25, 相差=1天
2025-05-26 08:00:05,109 - INFO - 订单 20250523152623686816: 下单时间=2025-05-23, 激活时间=2025-05-25, 相差=2天
2025-05-26 08:00:05,109 - INFO - 订单 20250524150347556109: 下单时间=2025-05-24, 激活时间=2025-05-25, 相差=1天
2025-05-26 08:00:05,109 - INFO - 熟龄度处理统计: 总订单=170, 成功处理=170, 处理失败=0
2025-05-26 08:00:05,109 - INFO - 熟龄度分布: 当天=12, 昨天=84, 前天=47, 更早=27
2025-05-26 08:00:05,109 - INFO - 骑手卡熟龄度分布: 当天=12, 昨天=84, 前天=47, 更早=26
2025-05-26 08:00:05,109 - INFO - 流量卡熟龄度分布: 当天=0, 昨天=0, 前天=0, 更早=1
2025-05-26 08:00:05,110 - INFO - 获取报表日期与前一日订单数据对比...
2025-05-26 08:00:05,110 - INFO - 对比日期: 报表日期=2025-05-25, 前一日=2025-05-24
2025-05-26 08:00:05,110 - INFO - 获取 2025-05-25 的订单数据（下单时间维度）...
2025-05-26 08:00:05,141 - INFO - 成功获取到 226 条去重后的订单数据（下单时间维度）
2025-05-26 08:00:05,142 - INFO - 获取 2025-05-24 的订单数据（下单时间维度）...
2025-05-26 08:00:05,174 - INFO - 成功获取到 217 条去重后的订单数据（下单时间维度）
2025-05-26 08:00:05,175 - INFO - 获取周订单趋势数据...
2025-05-26 08:00:05,175 - INFO - 查询日期范围: 2025-05-19 00:00:00 至 2025-05-25 23:59:59
2025-05-26 08:00:05,199 - INFO - 成功获取到 7 天的订单趋势数据
2025-05-26 08:00:05,199 - INFO - 熟龄度数据: {'same_day': 12, 'one_day': 84, 'two_days': 47, 'more_days': 27}
2025-05-26 08:00:05,199 - INFO - 骑手卡熟龄度数据: {'same_day': 12, 'one_day': 84, 'two_days': 47, 'more_days': 26}, 总数: 169
2025-05-26 08:00:05,199 - INFO - 流量卡熟龄度数据: {'same_day': 0, 'one_day': 0, 'two_days': 0, 'more_days': 1}, 总数: 1
2025-05-26 08:00:05,199 - INFO - 当日下单当日激活比例: 7.1%（12/170）
2025-05-26 08:00:05,199 - INFO - 样本订单日期检查 (共5个): 170，熟龄度分布: 当天=12, 昨天=84, 前天=47, 更早=27
2025-05-26 08:00:05,199 - INFO - 环比分析: 当前=226单, 前一日=217单, 变化=4.1%
2025-05-26 08:00:05,199 - INFO - 周趋势数据获取成功，共7天数据
2025-05-26 08:00:05,272 - INFO - 开始发送模板卡片消息到企业微信...
2025-05-26 08:00:05,272 - INFO - 开始发送消息到企业微信（全渠道生产环境）...
2025-05-26 08:00:05,561 - INFO - 企业微信API响应状态码: 200
2025-05-26 08:00:05,562 - INFO - 企业微信消息发送成功
2025-05-26 08:00:05,562 - INFO - 昨天的日报发送成功
2025-05-26 08:00:05,562 - INFO - 昨天的日报处理完成。
2025-05-26 08:00:05,562 - INFO - ==================================================
[2025-05-26 08:00:01] 全渠道日报脚本执行成功
[2025-05-26 08:00:01] 开始执行OPPO渠道日报脚本...
2025-05-26 08:00:05,721 - INFO - ==================================================
2025-05-26 08:00:05,721 - INFO - 开始生成并发送OPPO和荣耀渠道日报（OPPO生产环境）...
2025-05-26 08:00:05,721 - INFO - 开始执行OPPO和荣耀渠道日报发送流程（OPPO生产环境）...
2025-05-26 08:00:05,737 - INFO - 查询日期: 2025-05-25
2025-05-26 08:00:05,738 - INFO - 获取 2025-05-25 的OPPO和荣耀渠道订单数据（下单时间维度）...
2025-05-26 08:00:05,759 - INFO - 查询到 0 条去重后的订单数据
2025-05-26 08:00:05,759 - WARNING - 未获取到OPPO和荣耀渠道订单数据，取消发送日报
2025-05-26 08:00:05,759 - WARNING - 由于没有数据，日报未发送
2025-05-26 08:00:05,759 - INFO - ==================================================
[2025-05-26 08:00:01] OPPO渠道日报脚本执行成功
[2025-05-26 08:00:05] 所有日报脚本执行完成
----------------------------------------
